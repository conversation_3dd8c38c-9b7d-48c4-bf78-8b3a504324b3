"""
共用處理器模組
提供各種常用的訊息和命令處理器
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from telegram import Update
from telegram.ext import ContextTypes

from bots.keyboards.common_keyboards import CommonKeyboards


class CommonHandlers:
    """共用處理器類別"""
    
    def __init__(self, bot_instance):
        """
        初始化共用處理器
        
        Args:
            bot_instance: 機器人實例
        """
        self.bot = bot_instance
        self.logger = logging.getLogger(f"{self.bot.bot_name}.CommonHandlers")
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /start 命令"""
        welcome_message = (
            "🎉 歡迎使用 FlipAdam 分析系統！\n\n"
            "📊 這是一個專業的金融分析機器人，提供：\n"
            "• 多時間框架技術分析\n"
            "• 股票、基金、加密貨幣分析\n"
            "• 智能訂閱和提醒功能\n"
            "• 即時市場監控\n\n"
            "請選擇您想要使用的功能："
        )
        
        await self.bot.send_message(
            update, context,
            welcome_message,
            reply_markup=CommonKeyboards.main_menu()
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /help 命令"""
        help_message = (
            "📚 FlipAdam 分析系統使用說明\n\n"
            "🔧 主要功能：\n"
            "• /start - 開始使用機器人\n"
            "• /help - 顯示此說明\n"
            "• /status - 查看系統狀態\n"
            "• /settings - 個人設定\n\n"
            "📊 分析功能：\n"
            "• 30分鐘、1小時、4小時、日線分析\n"
            "• 支援股票、基金、加密貨幣、指數\n"
            "• 自動生成技術分析圖表\n\n"
            "📈 訂閱功能：\n"
            "• 設定自動分析頻率\n"
            "• 接收即時分析結果\n"
            "• 自定義提醒條件\n\n"
            "💡 使用提示：\n"
            "• 點擊選單按鈕快速操作\n"
            "• 輸入股票代碼進行分析\n"
            "• 設定訂閱接收定期報告"
        )
        
        await self.bot.send_message(
            update, context,
            help_message,
            reply_markup=CommonKeyboards.back_button()
        )
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /status 命令"""
        try:
            # 獲取系統狀態信息
            status_info = await self._get_system_status()
            
            status_message = (
                f"📊 系統狀態報告\n\n"
                f"🤖 機器人狀態: {status_info['bot_status']}\n"
                f"📈 活躍訂閱: {status_info['active_subscriptions']}\n"
                f"💾 記憶體使用: {status_info['memory_usage']}\n"
                f"⏰ 最後更新: {status_info['last_update']}\n"
                f"🔗 連接狀態: {status_info['connection_status']}"
            )
            
            await self.bot.send_message(
                update, context,
                status_message,
                reply_markup=CommonKeyboards.back_button()
            )
            
        except Exception as e:
            self.logger.error(f"獲取狀態時發生錯誤: {e}")
            await self.bot.send_message(
                update, context,
                "❌ 無法獲取系統狀態，請稍後再試。"
            )
    
    async def settings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /settings 命令"""
        settings_message = (
            "⚙️ 個人設定\n\n"
            "請選擇您要調整的設定："
        )
        
        settings_keyboard = CommonKeyboards.custom_keyboard([
            ["🔔 通知設定", "🌍 語言設定"],
            ["⏰ 時區設定", "📊 分析偏好"],
            ["🔙 返回主選單"]
        ])
        
        await self.bot.send_message(
            update, context,
            settings_message,
            reply_markup=settings_keyboard
        )
    
    async def handle_back_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理返回按鈕"""
        await self.bot.send_message(
            update, context,
            "🔙 返回主選單",
            reply_markup=CommonKeyboards.main_menu()
        )
    
    async def handle_cancel_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理取消按鈕"""
        await self.bot.send_message(
            update, context,
            "❌ 操作已取消",
            reply_markup=CommonKeyboards.main_menu()
        )
    
    async def handle_unknown_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理未知命令"""
        unknown_message = (
            "❓ 未知命令\n\n"
            "請使用 /start 開始使用機器人，或使用 /help 查看可用命令。"
        )
        
        await self.bot.send_message(
            update, context,
            unknown_message,
            reply_markup=CommonKeyboards.main_menu()
        )
    
    async def handle_error_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理錯誤訊息"""
        error_message = (
            "⚠️ 發生錯誤\n\n"
            "請檢查您的輸入是否正確，或稍後再試。\n"
            "如需協助，請使用 /help 查看使用說明。"
        )
        
        await self.bot.send_message(
            update, context,
            error_message,
            reply_markup=CommonKeyboards.back_button()
        )
    
    async def _get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態信息"""
        # 這裡可以實現實際的狀態檢查邏輯
        # 目前返回模擬數據
        return {
            'bot_status': '🟢 正常運行',
            'active_subscriptions': '5',
            'memory_usage': '128MB',
            'last_update': '2025-01-27 15:30:00',
            'connection_status': '🟢 已連接'
        }
    
    async def handle_main_menu_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理主選單選擇"""
        text = update.message.text
        
        if text == "📊 分析功能":
            await self._show_analysis_menu(update, context)
        elif text == "📈 訂閱管理":
            await self._show_subscription_menu(update, context)
        elif text == "⚙️ 設定":
            await self.settings_command(update, context)
        elif text == "ℹ️ 說明":
            await self.help_command(update, context)
        else:
            await self.handle_unknown_command(update, context)
    
    async def _show_analysis_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """顯示分析功能選單"""
        analysis_message = (
            "📊 分析功能\n\n"
            "請選擇您要分析的時間框架："
        )
        
        await self.bot.send_message(
            update, context,
            analysis_message,
            reply_markup=CommonKeyboards.analysis_menu()
        )
    
    async def _show_subscription_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """顯示訂閱管理選單"""
        subscription_message = (
            "📈 訂閱管理\n\n"
            "管理您的分析訂閱和提醒設定："
        )
        
        await self.bot.send_message(
            update, context,
            subscription_message,
            reply_markup=CommonKeyboards.subscription_menu()
        ) 