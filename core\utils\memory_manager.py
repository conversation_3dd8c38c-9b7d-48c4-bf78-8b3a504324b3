import gc
import psutil
import os
import time
from functools import wraps

def log_memory_usage(message=""):
    """記錄記憶體使用情況"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    memory_mb = memory_info.rss / 1024 / 1024
    print(f"記憶體使用: {memory_mb:.2f} MB - {message}")

def cleanup_variables(**variables):
    """清理指定的變數"""
    for var_name, var_value in variables.items():
        if var_value is not None:
            del var_value
    gc.collect()

def monitor_memory(func):
    """裝飾器：監控函數執行時的記憶體使用"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        log_memory_usage(f"函數開始: {func.__name__}")
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            execution_time = end_time - start_time
            log_memory_usage(f"函數結束: {func.__name__} (執行時間: {execution_time:.2f}秒)")
            gc.collect()
    
    return wrapper 