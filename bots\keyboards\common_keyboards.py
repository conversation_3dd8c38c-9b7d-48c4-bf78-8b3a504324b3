"""
共用鍵盤模組
提供各種常用的鍵盤佈局和按鈕配置
"""

from telegram import ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from typing import List, Tuple, Optional


class CommonKeyboards:
    """共用鍵盤類別"""
    
    @staticmethod
    def main_menu() -> ReplyKeyboardMarkup:
        """主選單鍵盤"""
        keyboard = [
            [KeyboardButton("📊 分析功能"), KeyboardButton("⚙️ 設定")],
            [KeyboardButton("📈 訂閱管理"), KeyboardButton("ℹ️ 說明")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def analysis_menu() -> ReplyKeyboardMarkup:
        """分析功能選單"""
        keyboard = [
            [KeyboardButton("📊 30分鐘分析"), KeyboardButton("📈 1小時分析")],
            [KeyboardButton("📉 4小時分析"), KeyboardButton("📊 日線分析")],
            [KeyboardButton("🔙 返回主選單")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def timeframes() -> ReplyKeyboardMarkup:
        """時間框架選擇"""
        keyboard = [
            [KeyboardButton("30分鐘"), KeyboardButton("1小時")],
            [KeyboardButton("4小時"), KeyboardButton("日線")],
            [KeyboardButton("🔙 返回")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def asset_types() -> ReplyKeyboardMarkup:
        """資產類型選擇"""
        keyboard = [
            [KeyboardButton("📈 股票"), KeyboardButton("💰 基金")],
            [KeyboardButton("🪙 加密貨幣"), KeyboardButton("📊 指數")],
            [KeyboardButton("🔙 返回")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def subscription_menu() -> ReplyKeyboardMarkup:
        """訂閱管理選單"""
        keyboard = [
            [KeyboardButton("➕ 新增訂閱"), KeyboardButton("📋 查看訂閱")],
            [KeyboardButton("❌ 取消訂閱"), KeyboardButton("⚙️ 頻率設定")],
            [KeyboardButton("🔙 返回主選單")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def frequency_options() -> ReplyKeyboardMarkup:
        """頻率選項"""
        keyboard = [
            [KeyboardButton("每30分鐘"), KeyboardButton("每小時")],
            [KeyboardButton("每4小時"), KeyboardButton("每日")],
            [KeyboardButton("🔙 返回")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def confirmation() -> ReplyKeyboardMarkup:
        """確認選項"""
        keyboard = [
            [KeyboardButton("✅ 確認"), KeyboardButton("❌ 取消")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def back_button() -> ReplyKeyboardMarkup:
        """返回按鈕"""
        keyboard = [[KeyboardButton("🔙 返回")]]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def cancel_button() -> ReplyKeyboardMarkup:
        """取消按鈕"""
        keyboard = [[KeyboardButton("❌ 取消")]]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    @staticmethod
    def custom_keyboard(buttons: List[List[str]], one_time: bool = True) -> ReplyKeyboardMarkup:
        """自定義鍵盤"""
        keyboard = []
        for row in buttons:
            keyboard_row = [KeyboardButton(button) for button in row]
            keyboard.append(keyboard_row)
        
        return ReplyKeyboardMarkup(
            keyboard,
            one_time_keyboard=one_time,
            resize_keyboard=True
        )
    
    @staticmethod
    def inline_keyboard(buttons: List[List[Tuple[str, str]]]) -> InlineKeyboardMarkup:
        """內聯鍵盤"""
        keyboard = []
        for row in buttons:
            keyboard_row = []
            for text, callback_data in row:
                keyboard_row.append(InlineKeyboardButton(text, callback_data=callback_data))
            keyboard.append(keyboard_row)
        
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def subscription_actions(subscription_id: str) -> InlineKeyboardMarkup:
        """訂閱操作內聯鍵盤"""
        keyboard = [
            [
                InlineKeyboardButton("✅ 啟用", callback_data=f"enable_{subscription_id}"),
                InlineKeyboardButton("❌ 停用", callback_data=f"disable_{subscription_id}")
            ],
            [
                InlineKeyboardButton("⚙️ 編輯", callback_data=f"edit_{subscription_id}"),
                InlineKeyboardButton("🗑️ 刪除", callback_data=f"delete_{subscription_id}")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def analysis_actions(symbol: str, timeframe: str) -> InlineKeyboardMarkup:
        """分析操作內聯鍵盤"""
        keyboard = [
            [
                InlineKeyboardButton("📊 查看圖表", callback_data=f"chart_{symbol}_{timeframe}"),
                InlineKeyboardButton("📈 詳細分析", callback_data=f"detail_{symbol}_{timeframe}")
            ],
            [
                InlineKeyboardButton("🔔 訂閱提醒", callback_data=f"subscribe_{symbol}_{timeframe}"),
                InlineKeyboardButton("📋 歷史記錄", callback_data=f"history_{symbol}_{timeframe}")
            ]
        ]
        return InlineKeyboardMarkup(keyboard) 