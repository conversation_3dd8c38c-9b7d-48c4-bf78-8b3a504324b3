import logging
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes, MessageHandler, filters
import asyncio
import sys
import pandas as pd
import os
import re
import yfinance as yf
from telegram import InputMediaPhoto
from telegram.error import RetryAfter, TimedOut, NetworkError
from FlipAdamBox1D import getMLFlipAdambySymbol, get_cffi_session
import telegram
from telegram.request import HTTPXRequest
import io
from PIL import Image
import requests
import config
import aiohttp

# 設定日誌
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

# 您的 Bot Token
TOKEN = "8125853515:AAHXM3wRVgX1W8-KTYbKG6v3G3Q_bGUnCrc"

# 需要合併查詢的CSV檔案
CSV_FILES = [
    'stock_name_hold_fundsonly_SortbyValue.csv',
    'stock_name_hold_stockonly_SortbyValue.csv',
    'stock_names_Ady.csv',
    'stock_names_coin.csv',
    'stock_names_ETF.csv',
    'stock_names_watch_index.csv',
]

def sanitize_filename(symbol):
    """處理檔案名稱中的特殊符號"""
    # 將特殊符號替換為底線
    return re.sub(r'[<>:"/\\|?*]', '_', symbol)

def load_stock_codes():
    stock_dict = {}
    name_to_symbol = {}  # 新增：名稱到代碼的映射
    for csv_file in CSV_FILES:
        try:
            df = pd.read_csv(csv_file)
            # 避免重複，後面的檔案會覆蓋前面的同名代碼
            stock_dict.update(dict(zip(df['Symbol'], df['Name'])))
            # 建立名稱到代碼的映射（不分大小寫）
            for symbol, name in zip(df['Symbol'], df['Name']):
                name_to_symbol[name.lower()] = symbol
        except Exception as e:
            logging.error(f"讀取 {csv_file} 時發生錯誤: {e}")
    return stock_dict, name_to_symbol

# 載入股票代碼資料
STOCK_CODES, NAME_TO_SYMBOL = load_stock_codes()

def find_matching_stocks(query):
    """搜尋匹配的股票代碼或名稱"""
    query = query.lower()
    matches = []
    
    # 搜尋代碼（不分大小寫）
    for symbol in STOCK_CODES.keys():
        if query in symbol.lower():
            matches.append((symbol, STOCK_CODES[symbol]))
    
    # 搜尋名稱
    for name, symbol in NAME_TO_SYMBOL.items():
        if query in name:
            if symbol not in [m[0] for m in matches]:  # 避免重複
                matches.append((symbol, STOCK_CODES[symbol]))
    
    return matches

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /start 命令"""
    await update.message.reply_text('您好！我是 FlipAdam 通知機器人。\n'
                                  '使用 /help 查看可用命令。\n'
                                  '您也可以直接發送訊息給我！')

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /help 命令"""
    help_text = """
可用命令：
/start - 開始使用機器人
/help - 顯示此幫助訊息
/notify <訊息> - 發送通知訊息

您也可以直接發送訊息給我，我會回覆您！
直接輸入股票代碼或名稱，我會幫您查詢對應的股票資訊並發送圖表。
支援模糊搜尋，例如：
- 輸入 "台積電" 或 "tsm" 都可以找到台積電
- 輸入 "科技" 會列出所有包含科技的公司

注意：不是所有股票都有技術分析圖表，如果找不到圖表，會顯示提示訊息。
    """
    await update.message.reply_text(help_text)

async def notify(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /notify 命令"""
    if not context.args:
        await update.message.reply_text('請提供要發送的通知訊息。\n'
                                      '例如：/notify 這是一則測試通知')
        return
    
    message = ' '.join(context.args)
    await update.message.reply_text(f'已發送通知：{message}')

async def compress_image_to_jpeg(image_path, max_size_kb=100):
    """壓縮圖片到指定 KB 以內，回傳 BytesIO"""
    with Image.open(image_path) as img:
        if img.mode in ("RGBA", "LA"):
            background = Image.new("RGB", img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1])
            img = background
        quality = 95
        output = io.BytesIO()
        while quality > 10:
            output.seek(0)
            output.truncate()
            img.save(output, format="JPEG", quality=quality)
            size_kb = output.tell() / 1024
            if size_kb <= max_size_kb:
                break
            quality -= 5
        output.seek(0)
        return output

async def send_photo_with_aiohttp(token, chat_id, photo_path, caption):
    url = f"https://api.telegram.org/bot{token}/sendPhoto"
    # 壓縮圖片
    compressed = await compress_image_to_jpeg(photo_path, max_size_kb=100)
    data = aiohttp.FormData()
    data.add_field("chat_id", str(chat_id))
    data.add_field("caption", caption)
    data.add_field("photo", compressed, filename="chart.jpg", content_type="image/jpeg")
    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        async with session.post(url, data=data) as resp:
            result = await resp.json()
            return result

async def send_photo_with_retry(update, photo_path, caption, max_retries=3):
    for attempt in range(max_retries):
        try:
            logging.info(f"準備用 aiohttp 發送圖片: {photo_path}")
            result = await send_photo_with_aiohttp(
                config.TELEGRAM_TOKEN,
                update.effective_chat.id,
                photo_path,
                caption
            )
            logging.info(f"aiohttp 發送圖片結果: {result}")
            if result.get("ok"):
                return True
            else:
                await update.message.reply_text(f"aiohttp 發送圖片失敗: {result}")
        except Exception as e:
            logging.error(f"aiohttp 發送圖片時發生錯誤: {e}", exc_info=True)
            await update.message.reply_text(f"aiohttp 發送圖片時發生錯誤: {e}")
            raise
    return False

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理一般訊息"""
    try:
        user_message = update.message.text.strip()
        user = update.effective_user
        user_name = user.first_name
        logging.info(f"收到來自 {user_name} 的訊息: {user_message}")

        # 搜尋匹配的股票
        matches = find_matching_stocks(user_message)
        
        if matches:
            for symbol, name in matches[:10]:  # 限制最多10個
                response = f"您輸入的是股票代碼：{symbol}\n對應的股票名稱是：{name}"
                await update.message.reply_text(response)
                
                # 確保 figure 目錄存在
                if not os.path.exists('figure'):
                    os.makedirs('figure')
                    logging.info("已創建 figure 目錄")
                
                safe_symbol = sanitize_filename(symbol)
                image_path = os.path.join('figure', f'{safe_symbol}_FlipTwice.png')
                logging.info(f"嘗試讀取圖表文件: {image_path}")
                
                if os.path.exists(image_path):
                    try:
                        if await send_photo_with_retry(update, image_path, f"{symbol} 的技術分析圖表"):
                            logging.info(f"成功發送 {symbol} 的圖表")
                        else:
                            await update.message.reply_text("發送圖片失敗，請稍後再試。")
                    except Exception as e:
                        logging.error(f"發送圖片時發生錯誤: {e}", exc_info=True)
                        await update.message.reply_text("抱歉，發送圖片時發生錯誤。")
                else:
                    logging.info(f"找不到圖表文件，嘗試生成新的圖表: {image_path}")
                    # 使用新的 FlipAdambySymbol 模組產生圖
                    session = get_cffi_session()
                    image_path = getMLFlipAdambySymbol(symbol, session=session)
                    
                    if image_path and os.path.exists(image_path):
                        try:
                            if await send_photo_with_retry(update, image_path, f"{symbol} 的技術分析圖表"):
                                logging.info(f"成功生成並發送 {symbol} 的圖表")
                            else:
                                await update.message.reply_text("發送圖片失敗，請稍後再試。")
                        except Exception as e:
                            logging.error(f"發送新生成的圖片時發生錯誤: {e}", exc_info=True)
                            await update.message.reply_text("抱歉，發送圖片時發生錯誤。")
                    else:
                        logging.error(f"無法生成圖表: {symbol}")
                        await update.message.reply_text(
                            f"抱歉，無法生成 {symbol} 的技術分析圖表。\n"
                            f"這可能是因為：\n"
                            f"1. 該股票資料無法取得\n"
                            f"2. 生成圖表時發生錯誤\n"
                            f"請稍後再試或聯繫管理員。"
                        )
            if len(matches) > 10:
                await update.message.reply_text(f"...還有 {len(matches)-10} 個結果未顯示")
        else:
            # 本地查無，嘗試 yfinance 查詢
            try:
                logging.info(f"嘗試使用 Yahoo Finance 查詢 {user_message}")
                ticker = yf.Ticker(user_message)
                info = ticker.info
                if info and 'regularMarketPrice' in info and info['regularMarketPrice'] is not None:
                    await update.message.reply_text("本地查無此代碼，已為您查詢 Yahoo Finance 並產生技術分析圖表，請稍候...")
                    # 使用新的 FlipAdambySymbol 模組產生圖
                    session = get_cffi_session()
                    image_path = getMLFlipAdambySymbol(user_message, session=session)
                    
                    if image_path and os.path.exists(image_path):
                        try:
                            if await send_photo_with_retry(update, image_path, f"{user_message} 的技術分析圖表"):
                                logging.info(f"成功生成並發送 {user_message} 的圖表")
                            else:
                                await update.message.reply_text("發送圖片失敗，請稍後再試。")
                        except Exception as e:
                            logging.error(f"發送圖片時發生錯誤: {e}", exc_info=True)
                            await update.message.reply_text("抱歉，發送圖片時發生錯誤。")
                    else:
                        logging.error(f"無法生成圖表: {user_message}")
                        await update.message.reply_text("已查到代碼，但產生圖表失敗。")
                else:
                    logging.warning(f"查無此股票代碼: {user_message}")
                    await update.message.reply_text("查無此股票代碼。")
            except Exception as e:
                logging.error(f"查詢過程發生錯誤: {e}", exc_info=True)
                await update.message.reply_text("查詢過程發生錯誤，請稍後再試。")
    except Exception as e:
        logging.error(f"處理訊息時發生錯誤: {e}", exc_info=True)
        await update.message.reply_text("處理訊息時發生錯誤，請稍後再試。")

def main():
    """主函數"""
    # 設定更長的超時時間
    request = HTTPXRequest(
        connect_timeout=30.0,
        read_timeout=30.0,
        write_timeout=30.0,
        pool_timeout=30.0,
    )
    # 建立應用程式
    application = Application.builder().token(TOKEN).request(request).build()

    # 註冊命令處理器
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("notify", notify))
    
    # 註冊訊息處理器
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

    # 啟動機器人
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    if sys.platform.startswith('win'):
        # Windows 特定設定
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    main() 