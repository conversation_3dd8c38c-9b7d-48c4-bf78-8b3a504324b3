import pandas as pd
import numpy as np
import datetime as dt
import yfinance as yf
import matplotlib
matplotlib.use('Agg')  # 使用非交互式後端，避免 Tkinter 錯誤
import matplotlib.pyplot as plt
from pandas.tseries.offsets import BDay
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
import base64
import time
from curl_cffi import requests  # 添加 curl_cffi 支援
from FlipAdamCon2 import calculate_signals

matplotlib.font_manager.fontManager.addfont('TaipeiSansTCBeta-Regular.ttf')
matplotlib.rc('font', family='Taipei Sans TC Beta')

# 設定 curl_cffi session
def get_cffi_session():
    session = requests.Session(impersonate="chrome")
    return session

# 確保 figure 目錄存在
if not os.path.exists('figure'):
    os.makedirs('figure')
if not os.path.exists('figure/Ady'):
    os.makedirs('figure/Ady')

def send_email_with_report(html_content, image_files, report_name):
    sender_email = "<EMAIL>"
    sender_name = "<PERSON> Lin"
    sender_password = "ahxr abjr cpiy lydz"
    receivers = ["<EMAIL>", "<EMAIL>"]
    
    # 設定郵件主題
    subject = f"Ady 持股報告 - {dt.datetime.now().strftime('%Y-%m-%d')}"
    
    # 創建郵件內容
    msg = MIMEMultipart('related')
    msg['Subject'] = subject
    msg['From'] = f'{sender_name} <{sender_email}>'
    msg['To'] = ', '.join(receivers)
    
    # 將 HTML 內容轉換為 MIME 格式
    html_part = MIMEText(html_content, 'html')
    msg.attach(html_part)
    
    # 添加圖片
    for image_file in image_files:
        with open(image_file, 'rb') as f:
            img_data = f.read()
            img = MIMEImage(img_data)
            img.add_header('Content-ID', f'<{os.path.basename(image_file)}>')
            msg.attach(img)
    
    try:
        # 使用 Gmail SMTP 伺服器
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        server.login(sender_email, sender_password)
        server.send_message(msg)
        server.quit()
        print(f"{subject} 郵件發送成功！")
    except Exception as e:
        print(f"{subject} 郵件發送失敗：{str(e)}")

def generate_html_report(stock_dict, report_name="analysis_report.html"):
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>分析報告</title>
        <style>
            body { font-family: 'Taipei Sans TC Beta', sans-serif; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 10px; text-align: center; }
            th { background-color: #f2f2f2; }
            img { max-width: 100%; height: auto; }
            .stock-name { font-size: 24px; font-weight: bold; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1>分析報告</h1>
        <table>
    """
    
    image_files = []
    for symbol, name in stock_dict.items():
        plot_filename = os.path.join('figure/Ady', f"{symbol}_FlipTwice.png")
        if os.path.exists(plot_filename):
            image_files.append(plot_filename)
            html_content += f"""
            <tr>
                <td class="stock-name">{name}</td>
            </tr>
            <tr>
                <td><img src="cid:{os.path.basename(plot_filename)}" alt="{name} 分析圖"></td>
            </tr>
            """
    
    html_content += """
        </table>
    </body>
    </html>
    """
    
    # 保存 HTML 文件
    with open(report_name, 'w', encoding='utf-8') as f:
        f.write(html_content)
    print(f"HTML 報告已生成：{report_name}")
    
    # 計算所有圖片檔案的總大小（MB）
    total_size = sum(os.path.getsize(f) for f in image_files) / (1024 * 1024)
    print(f"總附件大小：{total_size:.2f} MB")
    
    # 如果總大小超過 20MB，分批發送
    if total_size > 20:
        print("附件大小超過 20MB，將分批發送郵件")
        # 計算每批郵件可以包含的圖片數量
        batch_size = int(len(image_files) * (20 / total_size))
        batches = [image_files[i:i + batch_size] for i in range(0, len(image_files), batch_size)]
        
        for i, batch in enumerate(batches, 1):
            # 為每個批次生成對應的 HTML 內容
            batch_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>分析報告</title>
                <style>
                    body {{ font-family: 'Taipei Sans TC Beta', sans-serif; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th, td {{ padding: 10px; text-align: center; }}
                    th {{ background-color: #f2f2f2; }}
                    img {{ max-width: 100%; height: auto; }}
                    .stock-name {{ font-size: 24px; font-weight: bold; margin: 20px 0; }}
                </style>
            </head>
            <body>
                <h1>分析報告 (第 {i} 部分，共 {len(batches)} 部分)</h1>
                <table>
            """
            
            for image_file in batch:
                symbol = os.path.basename(image_file).split('_')[0]
                name = stock_dict.get(symbol, symbol)
                batch_html += f"""
                <tr>
                    <td class="stock-name">{name}</td>
                </tr>
                <tr>
                    <td><img src="cid:{os.path.basename(image_file)}" alt="{name} 分析圖"></td>
                </tr>
                """
            
            batch_html += """
                </table>
            </body>
            </html>
            """
            
            # 發送每個批次的郵件
            send_email_with_report(batch_html, batch, f"{report_name}_part{i}")
    else:
        # 如果總大小不超過 20MB，直接發送一封郵件
        send_email_with_report(html_content, image_files, report_name)

def getMLFlipAdambySymbol(symbol, session=None):
    # ==========================#
    # 1. 資料抓取 (Data Acquisition) - 日線資料
    # ==========================#

    symbol = symbol
    end_date = dt.datetime.now()
    
    # 根據標的類型決定時間範圍
    # 檢查是否為加密貨幣（stock_names_coin.csv 中的標的）
    crypto_symbols = []
    try:
        crypto_df = pd.read_csv('stock_names_coin.csv')
        crypto_symbols = crypto_df['Symbol'].tolist()
    except:
        pass
    
    if symbol in crypto_symbols:
        start_date = end_date - dt.timedelta(days=365)  # 加密貨幣使用1年
        days_text = "1年"
    else:
        start_date = end_date - dt.timedelta(days=365)  # 其他標的使用1年
        days_text = "1年"

    print(f"開始下載 {symbol} 的資料...")
    print(f"時間範圍: {start_date} 到 {end_date} ({days_text})")
    print(f"使用 session: {session is not None}")

    # 添加重試機制和延遲
    max_retries = 5  # 增加重試次數
    base_delay = 2  # 增加基礎延遲時間（秒）
    
    for attempt in range(max_retries):
        try:
            print(f"嘗試下載 {symbol} (第 {attempt + 1} 次)...")
            if session:
                df = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False, session=session)
            else:
                df = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            
            print(f"下載完成，資料形狀: {df.shape}")
            print(f"資料欄位: {df.columns.tolist()}")
            print(f"資料索引範圍: {df.index[0]} 到 {df.index[-1]}")
            
            time.sleep(base_delay)  # 在每次下載後添加延遲
            break
        except Exception as e:
            print(f"下載 {symbol} 時發生錯誤: {str(e)}")
            print(f"錯誤類型: {type(e).__name__}")
            if attempt < max_retries - 1:
                wait_time = base_delay * (2 ** attempt)  # 指數退避策略
                print(f"下載 {symbol} 日線資料失敗，等待 {wait_time} 秒後重試... ({attempt + 1}/{max_retries})")
                print(f"錯誤訊息: {str(e)}")
                time.sleep(wait_time)
            else:
                print(f"警告：無法取得 {symbol} 的日線資料，跳過此標的")
                return

    # 檢查 DataFrame 是否為空
    if df.empty:
        print(f"警告：{symbol} 的資料為空，跳過此標的")
        return

    # 若欄位為 MultiIndex，扁平化
    if isinstance(df.columns, pd.MultiIndex):
        print(f"檢測到 MultiIndex 欄位，進行扁平化處理")
        print(f"原始欄位: {df.columns.tolist()}")
        # 提取第一層的欄位名稱
        df.columns = df.columns.get_level_values(0)
        print(f"扁平化後欄位: {df.columns.tolist()}")

    # 檢查必要的欄位是否存在
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"警告：{symbol} 缺少必要欄位: {missing_columns}")
        print(f"可用欄位: {df.columns.tolist()}")
        return

    df = df[required_columns].dropna()
    print(f"Downloaded {len(df)} rows of daily data for {symbol}")
    print(f"資料範圍: {df.index[0]} 到 {df.index[-1]}")
    print(f"最後5筆資料:")
    print(df.tail(5))

    # ==========================#
    # 2. 技術指標計算
    # ==========================#

    # --- ATR (14日) ---
    window_atr = 14
    high_low = df['High'] - df['Low']
    high_prevclose = (df['High'] - df['Close'].shift(1)).abs()
    low_prevclose = (df['Low'] - df['Close'].shift(1)).abs()
    df['TR'] = np.maximum.reduce([high_low, high_prevclose, low_prevclose])

    # 計算 ATR 的函數
    def atr(data, period):
        tr = data['TR']
        atr = pd.Series(index=data.index)
        atr.iloc[:period] = tr.iloc[:period].mean()
        for i in range(period, len(data)):
            atr.iloc[i] = (atr.iloc[i - 1] * (period - 1) + tr.iloc[i]) / period
        return atr

    # 計算追踪止損價的函式
    def calculate_trailing_stop(df, nATRPeriod, nATRMultip):
        xATR = atr(df, nATRPeriod)
        nLoss = nATRMultip * xATR

        xATRTrailingStop = pd.Series(index=df.index)
        for i in range(1, len(df)):
            prev_close = df['Close'].iloc[i - 1]
            prev_stop = xATRTrailingStop.iloc[i - 1]
            close = df['Close'].iloc[i]

            if close > prev_stop and prev_close > prev_stop:
                xATRTrailingStop.iloc[i] = max(prev_stop, close - nLoss.iloc[i])
            elif close < prev_stop and prev_close < prev_stop:
                xATRTrailingStop.iloc[i] = min(prev_stop, close + nLoss.iloc[i])
            else:
                xATRTrailingStop.iloc[i] = close - nLoss.iloc[i] if close > prev_stop else close + nLoss.iloc[i]
        return xATRTrailingStop

    # 計算回測報酬的函式
    def calculate_returns(df, nATRPeriod, nATRMultip):
        trailing_stop = calculate_trailing_stop(df, nATRPeriod, nATRMultip)
        position = 0  # 0: 空倉, 1: 多倉, -1: 空倉
        entry_price = 0
        returns = []

        for i in range(1, len(df)):
            close = df['Close'].iloc[i]
            prev_close = df['Close'].iloc[i - 1]

            if position == 0:  # 空倉
                if close > trailing_stop.iloc[i] and prev_close <= trailing_stop.iloc[i - 1]:
                    position = 1
                    entry_price = close
                elif close < trailing_stop.iloc[i] and prev_close >= trailing_stop.iloc[i - 1]:
                    position = -1
                    entry_price = close
            elif position == 1:  # 多倉
                if close < trailing_stop.iloc[i]:
                    returns.append((close - entry_price) / entry_price)
                    position = 0
            else:  # 空倉
                if close > trailing_stop.iloc[i]:
                    returns.append((entry_price - close) / entry_price)
                    position = 0

        return sum(returns) if returns else 0

    # ATR 參數最佳化
    # 對於日線資料，調整週期範圍
    periods = range(5, 31, 5)  # ATR 週期範圍：5-30個交易日
    multiples = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]  # ATR 倍數範圍
    best_return = float('-inf')
    best_period = None
    best_multiple = None

    for period in periods:
        for multiple in multiples:
            returns = calculate_returns(df, period, multiple)
            if returns > best_return:
                best_return = returns
                best_period = period
                best_multiple = multiple

    print(f"最佳 ATR 週期: {best_period} 個交易日")
    print(f"最佳 ATR 倍數: {best_multiple}")
    print(f"最佳累積報酬: {best_return:.2%}")

    # 使用最佳參數計算追踪止損價
    trailing_stop = calculate_trailing_stop(df, best_period, best_multiple)

    # ==========================#
    # 3. Adam Theory：歷史走勢 + 單一反射(左右) + 雙重反射(上下)
    # ==========================#

    # 初始化變數
    pivot_index = None
    start_index = None
    pivot_price = None
    start_price = None

    # 對於日線資料，調整 lookback 參數
    lookback = 60  # 60個交易日
    recent_window = df['Close'][-lookback:]

    # 尋找轉折點 - 改為使用最新收盤點
    pivot_index = df.index[-1]  # 使用最新日期
    pivot_price = df['Close'].iloc[-1]  # 使用最新收盤價
    
    # 使用最近60個交易日作為起點
    start_index = df.index[-60] if len(df) >= 60 else df.index[0]  # 60個交易日前的日期
    start_price = df.loc[start_index, 'Close']  # 60個交易日前的收盤價
    
    # 計算歷史路徑和反射路徑
    if pivot_index:
        # (A) 歷史實際走勢：從 start_index 到 pivot_index
        historical_path = df.loc[start_index:pivot_index, 'Close']
        hist_values = historical_path.values
        hist_duration = len(hist_values)

        # (B) 單一反射 (Horizontal Mirror) = 只做時間序列的左右翻轉
        once_values = hist_values[::-1]  # 左右翻轉
        # 對於日線資料，使用日頻率
        once_dates = pd.date_range(start=pivot_index + pd.Timedelta(days=1), periods=hist_duration, freq='D')
        once_reflect_path = pd.Series(data=once_values, index=once_dates)

        # (C) 雙重反射 (Vertical Mirror)
        pivot_for_vertical = pivot_price
        second_reflect_values = 2 * pivot_for_vertical - once_values
        twice_reflect_path = pd.Series(data=second_reflect_values, index=once_dates)

        # 移動平均線
        # 合併歷史價格和反射價格
        combined_prices = pd.concat([df['Close'], twice_reflect_path])
        # 計算合併後的均線（60個交易日）
        combined_sma60 = combined_prices.rolling(60).mean()
        # 分離歷史和反射部分的均線
        df['SMA60'] = combined_sma60[:len(df)]
        reflect_sma60 = combined_sma60[len(df):]

        # 計算原本歷史1年回歸線的斜率
        x_original = np.arange(len(df))
        y_original = df['Close'].values
        coefficients_original = np.polyfit(x_original, y_original, 1)
        slope_original = coefficients_original[0]

        # 合併歷史數據和雙重反射數據用於計算趨勢
        combined_data = pd.concat([historical_path, twice_reflect_path])

        # 計算(歷史1年回歸線+60天反射路徑)的回歸線斜率
        x_combined = np.arange(len(combined_data))
        y_combined = combined_data.values
        coefficients_combined = np.polyfit(x_combined, y_combined, 1)
        slope_combined = coefficients_combined[0]

        # 根據新的判斷邏輯判斷趨勢
        if slope_original > 0 and slope_combined > slope_original:
            trend_signal = "up"
        elif slope_original < 0 and slope_combined < slope_original:
            trend_signal = "down"
        elif slope_original < 0 and slope_combined > slope_original:
            trend_signal = "correction"
        elif slope_original > 0 and slope_combined < slope_original:
            trend_signal = "correction"
        else:
            trend_signal = "sideways"

        print(f"原本歷史1年回歸線斜率: {slope_original:.6f}")
        print(f"歷史+反射路徑回歸線斜率: {slope_combined:.6f}")
        print(f"Based on new trend logic, the trend signal is: {trend_signal}")
    else:
        print("No pivot point found, using default trend signal.")
        trend_signal = "sideways"

    # 計算 ATR 並加入到 DataFrame
    df['ATR'] = atr(df, best_period)

    # 計算重要轉折點
    prices_array = df['Close'].values
    local_max_idx = [i for i in range(1, len(prices_array) - 1)
                     if prices_array[i] > prices_array[i - 1] and prices_array[i] > prices_array[i + 1]]
    local_min_idx = [i for i in range(1, len(prices_array) - 1)
                     if prices_array[i] < prices_array[i - 1] and prices_array[i] < prices_array[i + 1]]
    pivots_idx = sorted(local_max_idx + local_min_idx)

    significant_pivots = []
    if pivots_idx:
        last_idx = pivots_idx[0]
        last_type = 'min' if last_idx in local_min_idx else 'max'
        significant_pivots.append(last_idx)
        for idx in pivots_idx[1:]:
            current_type = 'min' if idx in local_min_idx else 'max'
            if current_type == last_type:
                continue
            if abs(prices_array[idx] - prices_array[last_idx]) >= df['ATR'].iloc[idx]:
                significant_pivots.append(idx)
                last_idx = idx
                last_type = current_type

    # 下面就是繪圖部分
    plt.figure(figsize=(24, 13.5), dpi=300)  # 提高解析度：尺寸從16x9改為24x13.5，DPI從預設改為300
    plt.plot(df.index, df['Close'], label='Stock Price', color='black')
    plt.plot(historical_path.index, hist_values, label='Historical Path', color='blue', linewidth=2)
    plt.plot(twice_reflect_path.index, twice_reflect_path.values,
             label='Double Reflection', color='green', linestyle='--', linewidth=2)
    plt.plot(df.index, df['SMA60'], label='60MA', color='brown', linewidth=1.5)  # 歷史部分的均線
    plt.plot(twice_reflect_path.index, reflect_sma60, label='60MA (Reflection)', 
             color='orange', linestyle='--', linewidth=1.5)  # 反射部分的均線

    plt.axvline(pivot_index, color='gray', linestyle='--', alpha=0.7, label='Pivot Line')
    plt.scatter(pivot_index, pivot_price, color='orange', marker='o', s=100, label='Pivot Point')

    # 這裡維持你原本的回歸線與標準差線畫法
    x = np.arange(len(df))
    y = df['Close'].values
    coefficients = np.polyfit(x, y, 1)
    trend_line = np.polyval(coefficients, x)
    std_dev = np.std(y - trend_line)

    # 繪製趨勢線和標準差帶
    plt.plot(df.index, trend_line, color='purple', linestyle='-', label='Trend Line')

    # 添加標準差帶的底色（移除圖例標籤）
    plt.fill_between(df.index, trend_line + std_dev, trend_line - std_dev, color='white', alpha=0.2)
    plt.fill_between(df.index, trend_line + 2 * std_dev, trend_line + std_dev, color='lightgreen', alpha=0.2)
    plt.fill_between(df.index, trend_line - std_dev, trend_line - 2 * std_dev, color='lightcoral', alpha=0.2)
    plt.fill_between(df.index, trend_line + 3 * std_dev, trend_line + 2 * std_dev, color='green', alpha=0.2)
    plt.fill_between(df.index, trend_line - 2 * std_dev, trend_line - 3 * std_dev, color='red', alpha=0.2)

    # 繪製標準差線（移除圖例標籤）
    plt.plot(df.index, trend_line + std_dev, color='lightgreen', linestyle='--')
    plt.plot(df.index, trend_line - std_dev, color='lightcoral', linestyle='--')
    plt.plot(df.index, trend_line + 2 * std_dev, color='green', linestyle='--')
    plt.plot(df.index, trend_line - 2 * std_dev, color='red', linestyle='--')
    plt.plot(df.index, trend_line + 3 * std_dev, color='darkgreen', linestyle='--')
    plt.plot(df.index, trend_line - 3 * std_dev, color='darkred', linestyle='--')

    # 計算延伸到二次反射線圖的追踪止損線
    extended_trailing_stop = pd.Series(index=twice_reflect_path.index)
    last_stop = trailing_stop.iloc[-1]
    last_close = df['Close'].iloc[-1]

    for i in range(len(twice_reflect_path)):
        current_price = twice_reflect_path.iloc[i]
        if i == 0:
            extended_trailing_stop.iloc[i] = last_stop
        else:
            prev_stop = extended_trailing_stop.iloc[i - 1]
            if current_price > prev_stop:
                extended_trailing_stop.iloc[i] = max(prev_stop, current_price - best_multiple * df['ATR'].iloc[-1])
            else:
                extended_trailing_stop.iloc[i] = min(prev_stop, current_price + best_multiple * df['ATR'].iloc[-1])

    # 繪製追踪止損價（包括延伸部分）
    plt.plot(df.index, trailing_stop, color='gray', linestyle='--', label='Trailing Stop')
    plt.plot(twice_reflect_path.index, extended_trailing_stop, color='gray', linestyle='--')

    # 檢測突破和跌破點（包括延伸部分）
    for i in range(1, len(df)):
        prev_close = df['Close'].iloc[i - 1]
        curr_close = df['Close'].iloc[i]
        prev_stop = trailing_stop.iloc[i - 1]
        curr_stop = trailing_stop.iloc[i]

        # 檢測向上突破
        if prev_close <= prev_stop and curr_close > curr_stop:
            plt.scatter(df.index[i], curr_close, color='green', marker='.', s=50, label='突破點' if i == 1 else "")
            plt.annotate(f'{curr_close:.2f}',
                         (df.index[i], curr_close),
                         xytext=(2, 2),
                         textcoords='offset points',
                         color='green',
                         fontsize=8)

        # 檢測向下跌破
        elif prev_close >= prev_stop and curr_close < curr_stop:
            plt.scatter(df.index[i], curr_close, color='red', marker='.', s=50, label='跌破點' if i == 1 else "")
            plt.annotate(f'{curr_close:.2f}',
                         (df.index[i], curr_close),
                         xytext=(2, -8),
                         textcoords='offset points',
                         color='red',
                         fontsize=8)

    # 檢測延伸部分的突破和跌破點
    for i in range(1, len(twice_reflect_path)):
        prev_price = twice_reflect_path.iloc[i - 1]
        curr_price = twice_reflect_path.iloc[i]
        prev_stop = extended_trailing_stop.iloc[i - 1]
        curr_stop = extended_trailing_stop.iloc[i]

        # 檢測向上突破
        if prev_price <= prev_stop and curr_price > curr_stop:
            plt.scatter(twice_reflect_path.index[i], curr_price, color='green', marker='.', s=50)
            plt.annotate(f'{curr_price:.2f}',
                         (twice_reflect_path.index[i], curr_price),
                         xytext=(2, 2),
                         textcoords='offset points',
                         color='green',
                         fontsize=8)

        # 檢測向下跌破
        elif prev_price >= prev_stop and curr_price < curr_stop:
            plt.scatter(twice_reflect_path.index[i], curr_price, color='red', marker='.', s=50)
            plt.annotate(f'{curr_price:.2f}',
                         (twice_reflect_path.index[i], curr_price),
                         xytext=(2, -8),
                         textcoords='offset points',
                         color='red',
                         fontsize=8)

    # 修改圖例位置為左上方
    plt.legend(loc='upper left', bbox_to_anchor=(0.0, 1.0))

    # ==========================#
    # 4. 交易策略回測與勝率計算
    # ==========================#

    wins = 0
    trades = 0
    for i in range(len(significant_pivots) - 1):
        s_idx = significant_pivots[i]
        e_idx = significant_pivots[i + 1]
        if s_idx in local_min_idx and e_idx in local_max_idx:
            entry_price = prices_array[s_idx]
            exit_price = prices_array[e_idx]
            profit = exit_price - entry_price
        elif s_idx in local_max_idx and e_idx in local_min_idx:
            entry_price = prices_array[s_idx]
            exit_price = prices_array[e_idx]
            profit = entry_price - exit_price
        else:
            continue
        trades += 1
        if profit > 0:
            wins += 1

    # 計算反射路徑最後一個價位與目前價位的差異
    current_price = df['Close'].iloc[-1]  # 今日價位
    current_date = df.index[-1]  # 今日日期
    
    # 根據 trend_signal 選擇對應的目標價位
    if trend_signal == "up":
        # 上漲趨勢，使用反射路徑最高價
        reflection_target_price = max(second_reflect_values)
        target_index = np.argmax(second_reflect_values)
        reflection_direction = "上漲趨勢-最高價"
    elif trend_signal == "down":
        # 下跌趨勢，使用反射路徑最低價
        reflection_target_price = min(second_reflect_values)
        target_index = np.argmin(second_reflect_values)
        reflection_direction = "下跌趨勢-最低價"
    elif trend_signal == "correction":
        # 修正趨勢，使用反射路徑最後一個價格
        reflection_target_price = second_reflect_values[-1]
        target_index = len(second_reflect_values) - 1
        reflection_direction = "修正趨勢-最後價"
    else:
        # sideways 或其他情況，使用反射路徑最後一個價格
        reflection_target_price = second_reflect_values[-1]
        target_index = len(second_reflect_values) - 1
        reflection_direction = "橫盤趨勢-最後價"
    
    reflection_target_date = twice_reflect_path.index[target_index]
    
    # 計算價差和百分比
    price_difference = reflection_target_price - current_price
    price_ratio = reflection_target_price / current_price
    percentage_change = (price_ratio - 1) * 100
    
    # 判斷漲跌方向
    if price_difference > 0:
        direction = "漲"
    elif price_difference < 0:
        direction = "跌"
    else:
        direction = "持平"
    
    if trades > 0:
        win_rate = wins / trades
        text_info = [
            f"Based on regression slope, the trend signal is: {trend_signal}",
            f"Historical Path start: {start_index.date()}, price={start_price:.2f}",
            f"Pivot index: {pivot_index.date()}, price={pivot_price:.2f}",
            f"Double Reflection Path: Start={second_reflect_values[0]:.2f}, End={second_reflect_values[-1]:.2f}",
            f"Backtest Result: Total Trades: {trades}, Winning Trades: {wins}, Win Rate: {win_rate * 100:.2f}%",
            f"今日: {current_date.date()} {current_price:.2f} | 反射目標: {reflection_target_date.date()} {reflection_target_price:.2f} | 價差: {price_difference:.2f} ({direction} {abs(percentage_change):.2f}%)"
        ]
    else:
        text_info = [
            f"Based on regression slope, the trend signal is: {trend_signal}",
            f"Historical Path start: {start_index.date()}, price={start_price:.2f}",
            f"Pivot index: {pivot_index.date()}, price={pivot_price:.2f}",
            f"Double Reflection Path: Start={second_reflect_values[0]:.2f}, End={second_reflect_values[-1]:.2f}",
            "No significant pivot-based signals found for backtesting.",
            f"今日: {current_date.date()} {current_price:.2f} | 反射目標: {reflection_target_date.date()} {reflection_target_price:.2f} | 價差: {price_difference:.2f} ({direction} {abs(percentage_change):.2f}%)"
        ]

    # 在圖的左下方顯示文本
    y_text = 0.3
    for info in text_info:
        plt.text(0.02, y_text, info, transform=plt.gca().transAxes, ha='left', va='top', fontsize=12)
        y_text -= 0.05

    # 使用 FlipAdamCon2 的原始判斷條件
    df = calculate_signals(df, N=30)  # 將突破週期改為30天
    
    # 添加調試信息
    print(f"\n=== {symbol} 信號統計 ===")
    print(f"做多訊號數量: {len(df[df['entry_long']])}")
    print(f"做空訊號數量: {len(df[df['entry_short']])}")
    print(f"跳空訊號數量: {len(df[df['entry_gap']])}")
    print("使用原始參數設定：")
    print("- 突破：30天")  # 更新顯示的突破週期
    print("- 趨勢：60天 SMA + 10天新高/新低")
    print("- 跳空：20天 ATR, 1.5倍")
    
    # 檢查跳空訊號的具體數據
    gap_signals = df[df['entry_gap']]
    if not gap_signals.empty:
        print("\n跳空訊號詳情:")
        print(gap_signals[['Open', 'High', 'Low', 'Close', 'ATR20', 'range']].tail())
    else:
        print("\n沒有找到跳空訊號")

    # 標記做多訊號
    # 找出同時出現的訊號
    both_signals = df[df['breakout_long'] & df['trend_long']]
    # 找出只有突破的訊號
    only_breakout = df[df['breakout_long'] & ~df['trend_long']]
    # 找出只有趨勢的訊號
    only_trend = df[~df['breakout_long'] & df['trend_long']]

    # 標記同時出現的訊號（綠色大"B"）
    plt.scatter(both_signals.index, both_signals['Close'], 
               color='green', marker='$B$', s=200, label='上方突破', alpha=0.7)

    # 標記只有突破的訊號（紅色大"B"）
    plt.scatter(only_breakout.index, only_breakout['Close'], 
               color='red', marker='$B$', s=200, label='下方突破', alpha=0.7)

    # 標記趨勢信號
    # 上漲趨勢（綠色大往上"T"）
    trend_up_signals = df[df['trend_long']]
    plt.scatter(trend_up_signals.index, trend_up_signals['Close'], 
               color='green', marker='$T$', s=200, label='上漲趨勢', alpha=0.7)
    
    # 下跌趨勢（紅色大往下"T"）
    trend_down_signals = df[df['trend_short']]
    plt.scatter(trend_down_signals.index, trend_down_signals['Close'], 
               color='red', marker='$T$', s=200, label='下跌趨勢', alpha=0.7)

    # 標記跳空訊號（紫色大猩猩）
    gap_signals = df[df['entry_gap']]
    plt.scatter(gap_signals.index, gap_signals['Close'], 
               color='purple', marker='$G$', s=300, label='跳空信號', alpha=0.7)

    plt.title(f"{symbol} Adam Theory ({days_text}日線資料)", fontsize=16, fontweight='bold')
    plt.xlabel("Date", fontsize=14)
    plt.ylabel("Price", fontsize=14)
    plt.legend(loc='upper left', bbox_to_anchor=(0.0, 1.0), fontsize=12)
    
    # 調整軸刻度標籤的字體大小
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)
    
    # 設定圖片尺寸和 DPI，確保符合 Telegram 限制
    plt.gcf().set_size_inches(12, 8)  # 設定圖片尺寸為 12x8 英寸
    
    plt.tight_layout()
    
    # 記得把圖另存檔案
    plot_filename = os.path.join('figure/Ady', f"{symbol}_FlipTwice.png")
    if os.path.exists(plot_filename):
        os.remove(plot_filename)
    plt.savefig(plot_filename, dpi=150, bbox_inches='tight', pad_inches=0.1, 
                facecolor='white', edgecolor='none')
    print(f"Plot saved as {plot_filename}")

if __name__ == "__main__":
    # 清除 figure/Ady 目錄中的所有 PNG 檔案
    if os.path.exists('figure/Ady'):
        for file in os.listdir('figure/Ady'):
            if file.endswith('.png'):
                os.remove(os.path.join('figure/Ady', file))
        print("已清除 figure/Ady 目錄中的所有 PNG 檔案")
    else:
        os.makedirs('figure/Ady')
        print("已建立 figure/Ady 目錄")
    
    # 讀取 Ady 的 CSV 檔案
    ady_df = pd.read_csv('stock_names_Ady.csv')
    
    # 創建 curl_cffi session
    session = get_cffi_session()
    
    # 設定批次處理參數
    batch_size = 5  # 每批處理的數量
    batch_delay = 5  # 每批之間的延遲時間（秒）
    
    # 處理 Ady 清單
    print("\n處理 Ady 清單...")
    ady_dict = dict(zip(ady_df['Symbol'], ady_df['Name']))
    ady_symbols = list(ady_dict.keys())
    ady_batches = [ady_symbols[i:i + batch_size] for i in range(0, len(ady_symbols), batch_size)]
    
    for batch_idx, batch in enumerate(ady_batches, 1):
        print(f"\n處理第 {batch_idx} 批（共 {len(ady_batches)} 批）...")
        for symbol in batch:
            print(f"處理 {symbol}...")
            getMLFlipAdambySymbol(symbol, session=session)
        
        if batch_idx < len(ady_batches):
            print(f"等待 {batch_delay} 秒後處理下一批...")
            time.sleep(batch_delay)
    
    generate_html_report(ady_dict, 'ady')
    
    
    
    
    