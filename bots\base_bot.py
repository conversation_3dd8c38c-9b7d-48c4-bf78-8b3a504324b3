"""
基礎 Telegram 機器人類別
提供共用的初始化、錯誤處理和用戶介面管理功能
"""

import asyncio
import logging
from typing import Optional, Dict, Any, Callable
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from telegram.error import TelegramError

from core.config.settings import BOT_TOKEN
from core.utils.memory_manager import cleanup_variables, log_memory_usage, monitor_memory


class BaseBot:
    """基礎 Telegram 機器人類別"""
    
    def __init__(self, bot_name: str = "BaseBot"):
        """
        初始化基礎機器人
        
        Args:
            bot_name: 機器人名稱，用於日誌記錄
        """
        self.bot_name = bot_name
        self.application: Optional[Application] = None
        self.logger = self._setup_logger()
        
        # 註冊的命令和處理器
        self.commands: Dict[str, Callable] = {}
        self.message_handlers: Dict[str, Callable] = {}
        
        # 記憶體監控
        self.memory_monitor_enabled = True
        
    def _setup_logger(self) -> logging.Logger:
        """設置日誌記錄器"""
        logger = logging.getLogger(f"{self.bot_name}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def start(self):
        """啟動機器人"""
        try:
            self.logger.info(f"正在啟動 {self.bot_name}...")
            
            # 創建應用程式
            self.application = Application.builder().token(BOT_TOKEN).build()
            
            # 註冊處理器
            self._register_handlers()
            
            # 啟動機器人
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()
            
            self.logger.info(f"{self.bot_name} 已成功啟動")
            
        except Exception as e:
            self.logger.error(f"啟動 {self.bot_name} 時發生錯誤: {e}")
            raise
    
    async def stop(self):
        """停止機器人"""
        try:
            if self.application:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
                self.logger.info(f"{self.bot_name} 已停止")
        except Exception as e:
            self.logger.error(f"停止 {self.bot_name} 時發生錯誤: {e}")
    
    def _register_handlers(self):
        """註冊命令和訊息處理器"""
        # 註冊命令處理器
        for command, handler in self.commands.items():
            self.application.add_handler(CommandHandler(command, handler))
            self.logger.info(f"已註冊命令: /{command}")
        
        # 註冊訊息處理器
        for pattern, handler in self.message_handlers.items():
            self.application.add_handler(MessageHandler(filters.Regex(pattern), handler))
            self.logger.info(f"已註冊訊息處理器: {pattern}")
    
    def register_command(self, command: str):
        """裝飾器：註冊命令處理器"""
        def decorator(func: Callable):
            self.commands[command] = func
            return func
        return decorator
    
    def register_message_handler(self, pattern: str):
        """裝飾器：註冊訊息處理器"""
        def decorator(func: Callable):
            self.message_handlers[pattern] = func
            return func
        return decorator
    
    async def send_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, 
                          text: str, reply_markup: Optional[ReplyKeyboardMarkup] = None):
        """發送訊息的安全包裝器"""
        try:
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=text,
                reply_markup=reply_markup
            )
        except TelegramError as e:
            self.logger.error(f"發送訊息時發生錯誤: {e}")
            await self._handle_telegram_error(update, context, e)
    
    async def _handle_telegram_error(self, update: Update, context: ContextTypes.DEFAULT_TYPE, 
                                   error: TelegramError):
        """處理 Telegram 錯誤"""
        error_message = f"發生錯誤: {str(error)}"
        try:
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=error_message
            )
        except:
            self.logger.error(f"無法發送錯誤訊息: {error_message}")
    
    def create_keyboard(self, buttons: list, one_time_keyboard: bool = True) -> ReplyKeyboardMarkup:
        """創建鍵盤"""
        keyboard = []
        for row in buttons:
            keyboard_row = [KeyboardButton(button) for button in row]
            keyboard.append(keyboard_row)
        
        return ReplyKeyboardMarkup(
            keyboard,
            one_time_keyboard=one_time_keyboard,
            resize_keyboard=True
        )
    
    async def monitor_memory_usage(self):
        """監控記憶體使用量"""
        if self.memory_monitor_enabled:
            await monitor_memory(self.bot_name)
    
    async def cleanup_resources(self):
        """清理資源"""
        if self.memory_monitor_enabled:
            cleanup_variables()
            log_memory_usage(f"{self.bot_name} 資源清理完成")
    
    async def handle_error(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理未捕獲的錯誤"""
        self.logger.error(f"未處理的錯誤: {context.error}")
        await self.send_message(
            update, context, 
            "抱歉，發生了一個未預期的錯誤。請稍後再試。"
        ) 