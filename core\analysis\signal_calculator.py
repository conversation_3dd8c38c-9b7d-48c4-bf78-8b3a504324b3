import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import matplotlib.font_manager as fm
from datetime import datetime, timedelta
import argparse

def setup_font():
    """設定中文字型"""
    font_path = os.path.join(os.path.dirname(__file__), 'TaipeiSansTCBeta-Regular.ttf')
    if os.path.exists(font_path):
        fm.fontManager.addfont(font_path)
        plt.rcParams['font.family'] = 'Taipei Sans TC Beta'
    else:
        print('警告：TaipeiSansTCBeta-Regular.ttf 未放在專案資料夾，中文可能無法正常顯示')

def download_data(ticker, start_date, end_date):
    """下載股票資料"""
    print(f"\n=== 分析標的：{ticker} ===")
    df = yf.download(ticker, start=start_date, end=end_date, auto_adjust=False)
    print("df.columns:", df.columns)
    
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)
    return df

def calculate_signals(df, N=60, atr_period=None, atr_multiple=None):
    """計算各種交易信號"""
    # ---------- 線索一：突破 (Breakthrough) ----------
    df['highest_N'] = df['High'].rolling(window=N).max()
    df['lowest_N'] = df['Low'].rolling(window=N).min()
    df['breakout_long'] = df['Close'] > df['highest_N'].shift(1)
    df['breakout_short'] = df['Close'] < df['lowest_N'].shift(1)

    # ---------- 線索二：趨勢改變 (Trend Change) ----------
    df['SMA60'] = df['Close'].rolling(window=60).mean()
    df['trend_long'] = (df['Close'] > df['SMA60']) & (df['Close'] > df['Close'].shift(1).rolling(window=10).max())
    df['trend_short'] = (df['Close'] < df['SMA60']) & (df['Close'] < df['Close'].shift(1).rolling(window=10).min())

    # ---------- 線索三：跳空與區間放大 (Gap + Range) ----------
    df['H-L'] = df['High'] - df['Low']
    df['H-PC'] = np.abs(df['High'] - df['Close'].shift(1))
    df['L-PC'] = np.abs(df['Low'] - df['Close'].shift(1))
    df['TR'] = df[['H-L', 'H-PC', 'L-PC']].max(axis=1)
    
    # 使用提供的 ATR 參數或默認值
    if atr_period is not None and atr_multiple is not None:
        df['ATR20'] = df['TR'].rolling(window=atr_period).mean()
        df['range'] = df['High'] - df['Low']
        df['gap_long'] = (df['Open'] > df['High'].shift(1)) & (df['range'] > atr_multiple * df['ATR20'])
        df['gap_short'] = (df['Open'] < df['Low'].shift(1)) & (df['range'] > atr_multiple * df['ATR20'])
    else:
        df['ATR20'] = df['TR'].rolling(window=20).mean()
        df['range'] = df['High'] - df['Low']
        df['gap_long'] = (df['Open'] > df['High'].shift(1)) & (df['range'] > 1.5 * df['ATR20'])
        df['gap_short'] = (df['Open'] < df['Low'].shift(1)) & (df['range'] > 1.5 * df['ATR20'])

    # ---------- 整合三線索產生進場信號 ----------
    df['entry_long'] = df['breakout_long'] | df['trend_long']
    df['entry_short'] = df['breakout_short'] | df['trend_short']
    df['entry_gap'] = df['gap_long'] | df['gap_short']
    
    return df

def print_signals(df):
    """輸出各種信號"""
    print("\n=== 線索一：突破信號 ===")
    breakout_signals = df[df['breakout_long'] | df['breakout_short']][['Close', 'highest_N', 'lowest_N', 'breakout_long', 'breakout_short']]
    print(breakout_signals.tail(10))

    print("\n=== 線索二：趨勢改變信號 ===")
    trend_signals = df[df['trend_long'] | df['trend_short']][['Close', 'SMA60', 'trend_long', 'trend_short']]
    print(trend_signals.tail(10))

    print("\n=== 線索三：跳空與區間放大信號 ===")
    gap_signals = df[df['gap_long'] | df['gap_short']][['Close', 'Open', 'High', 'Low', 'ATR20', 'range', 'gap_long', 'gap_short']]
    print(gap_signals.tail(10))

    print("\n=== 最終進場信號 ===")
    final_signals = df[df['entry_long'] | df['entry_short'] | df['entry_gap']][['Close', 
                    'breakout_long', 'trend_long', 'entry_long',
                    'breakout_short', 'trend_short', 'entry_short',
                    'gap_long', 'gap_short', 'entry_gap']]
    print(final_signals.tail(10))

def plot_signals(df, ticker):
    """繪製股價走勢圖"""
    plt.figure(figsize=(15, 7))
    plt.plot(df.index, df['Close'], label='收盤價')
    plt.plot(df.index, df['SMA60'], label='60日均線', alpha=0.7)

    # 線索一：突破信號
    # 在趨勢線之上的突破（綠色大"B"）
    breakout_above = df[(df['breakout_long'] | df['breakout_short']) & (df['Close'] > df['SMA60'])]
    plt.scatter(breakout_above.index, breakout_above['Close'], 
               color='green', marker='$B$', s=200, label='上方突破', alpha=0.7)
    
    # 在趨勢線之下的突破（紅色大"B"）
    breakout_below = df[(df['breakout_long'] | df['breakout_short']) & (df['Close'] < df['SMA60'])]
    plt.scatter(breakout_below.index, breakout_below['Close'], 
               color='red', marker='$B$', s=200, label='下方突破', alpha=0.7)

    # 線索二：趨勢信號
    # 上漲趨勢（綠色大往上"T"）
    trend_up_signals = df[df['trend_long']]
    plt.scatter(trend_up_signals.index, trend_up_signals['Close'], 
               color='green', marker='$T$', s=200, label='上漲趨勢', alpha=0.7)
    
    # 下跌趨勢（紅色大往下"T"）
    trend_down_signals = df[df['trend_short']]
    plt.scatter(trend_down_signals.index, trend_down_signals['Close'], 
               color='red', marker='$T$', s=200, label='下跌趨勢', alpha=0.7)

    # 線索三：跳空信號（紫色大猩猩）
    gap_signals = df[df['gap_long'] | df['gap_short']]
    plt.scatter(gap_signals.index, gap_signals['Close'], 
               color='purple', marker='$G$', s=300, label='跳空信號', alpha=0.7)

    plt.title(f'{ticker} 股價走勢與交易訊號')
    plt.xlabel('日期')
    plt.ylabel('價格')
    plt.legend()
    plt.grid(True)
    plt.show()

def analyze_stock(symbol):
    """分析單一股票"""
    setup_font()
    
    # 設定日期範圍（改為730天，約2年）
    end_date = datetime.today().strftime('%Y-%m-%d')
    start_date = (datetime.today() - timedelta(days=730)).strftime('%Y-%m-%d')
    print(f"下載資料期間：{start_date} 到 {end_date}")
    
    # 下載並處理資料
    df = download_data(symbol, start_date, end_date)
    df = calculate_signals(df)
    
    # 輸出信號
    print_signals(df)
    
    # 繪製圖表
    plot_signals(df, symbol)

def main():
    """主程式"""
    
    analyze_stock("2330.TW")
    analyze_stock("NFLX")
    #analyze_stock("TSLA")
    
    
    # 讀取加密貨幣代碼
    
    
    # try:
    #     coins_df = pd.read_csv('stock_names_coin.csv')
    #     coins_dict = dict(zip(coins_df['Symbol'], coins_df['Name']))
        
    #     # 分析每個加密貨幣
    #     for symbol in coins_dict.keys():
    #         print(f"\n分析 {symbol} ({coins_dict[symbol]})")
    #         analyze_stock(symbol)
            
    # except FileNotFoundError:
    #     print("錯誤：找不到 stock_names_coin.csv 檔案")
    # except Exception as e:
    #     print(f"錯誤：{str(e)}")

if __name__ == "__main__":
    main()
