Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: httpx in c:\users\<USER>\appdata\roaming\python\python313\site-packages (0.28.1)
Requirement already satisfied: anyio in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from httpx) (4.9.0)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from httpx) (2025.4.26)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from httpx) (1.0.9)
Requirement already satisfied: idna in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from httpx) (3.10)
Requirement already satisfied: h11>=0.16 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from httpcore==1.*->httpx) (0.16.0)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from anyio->httpx) (1.3.1)
