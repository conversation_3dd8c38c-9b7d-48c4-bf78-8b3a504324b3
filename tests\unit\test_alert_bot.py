#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 FlipAdam 警報機器人功能
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from telegram_bot_alert import AlertBot

async def test_alert_bot():
    """測試警報機器人功能"""
    print("=== 開始測試 FlipAdam 警報機器人 ===")
    
    # 建立警報機器人實例
    alert_bot = AlertBot()
    
    # 測試1：獲取所有標的
    print("\n1. 測試獲取所有標的...")
    symbols = alert_bot.get_all_symbols()
    print(f"找到 {len(symbols)} 個標的: {symbols}")
    
    if not symbols:
        print("❌ 沒有找到任何標的資料")
        return
    
    # 測試2：載入標的資料
    print(f"\n2. 測試載入標的資料...")
    test_symbol = symbols[0]
    data_list = alert_bot.load_symbol_data(test_symbol, hours=24)
    print(f"載入 {test_symbol} 的資料: {len(data_list)} 筆")
    
    if len(data_list) < 1:
        print("❌ 沒有資料，無法進行警報測試")
        return
    
    # 測試3：檢查單筆資料警示
    print(f"\n3. 測試單筆資料警示...")
    single_alerts = alert_bot.check_single_data_alerts(data_list)
    if single_alerts:
        print(f"✅ 發現 {len(single_alerts)} 個單筆警示:")
        for alert in single_alerts:
            print(f"  - {alert['type']}: {alert}")
            message = alert_bot.format_alert_message(alert)
            print(f"    警報訊息:\n{message}")
    else:
        print("ℹ️ 沒有發現單筆警示")
    
    # 測試4：產生趨勢圖（如果有足夠資料）
    print(f"\n4. 測試產生趨勢圖...")
    if len(data_list) >= 2:
        trend_chart_path = alert_bot.create_trend_chart(test_symbol, data_list)
        if trend_chart_path:
            print(f"✅ 趨勢圖已產生: {trend_chart_path}")
        else:
            print("❌ 趨勢圖產生失敗")
    else:
        print("ℹ️ 資料不足，跳過趨勢圖測試")
    
    # 測試5：檢查信號變化（如果有足夠資料）
    print(f"\n5. 測試信號變化檢測...")
    if len(data_list) >= 2:
        signal_alert = alert_bot.check_signal_change(data_list)
        if signal_alert:
            print(f"✅ 發現信號變化: {signal_alert}")
            message = alert_bot.format_alert_message(signal_alert)
            print(f"警報訊息:\n{message}")
        else:
            print("ℹ️ 沒有發現信號變化")
    else:
        print("ℹ️ 資料不足，跳過信號變化測試")
    
    # 測試6：檢查漲跌幅變化（如果有足夠資料）
    print(f"\n6. 測試漲跌幅變化檢測...")
    if len(data_list) >= 2:
        direction_alert = alert_bot.check_price_change_direction(data_list)
        if direction_alert:
            print(f"✅ 發現方向變化: {direction_alert}")
            message = alert_bot.format_alert_message(direction_alert)
            print(f"警報訊息:\n{message}")
        else:
            print("ℹ️ 沒有發現方向變化")
    else:
        print("ℹ️ 資料不足，跳過漲跌幅變化測試")
    
    # 測試7：分析所有標的
    print(f"\n7. 測試分析所有標的...")
    await alert_bot.analyze_all_symbols()
    
    print("\n=== 測試完成 ===")

def test_data_structure():
    """測試資料結構"""
    print("\n=== 測試資料結構 ===")
    
    data_dir = 'data'
    if not os.path.exists(data_dir):
        print("❌ data 目錄不存在")
        return
    
    # 讀取第一個檔案來檢查結構
    files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
    if not files:
        print("❌ 沒有找到 JSON 檔案")
        return
    
    test_file = os.path.join(data_dir, files[0])
    with open(test_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"檔案: {files[0]}")
    print(f"標的: {data.get('symbol', 'N/A')}")
    print(f"時間版本: {data.get('time_version', 'N/A')}")
    print(f"時間戳記: {data.get('timestamp', 'N/A')}")
    
    analysis_data = data.get('analysis_data', {})
    print(f"\n分析資料欄位:")
    for key, value in analysis_data.items():
        print(f"  {key}: {value}")
    
    print("\n=== 資料結構測試完成 ===")

if __name__ == '__main__':
    # 測試資料結構
    test_data_structure()
    
    # 測試警報機器人
    asyncio.run(test_alert_bot()) 