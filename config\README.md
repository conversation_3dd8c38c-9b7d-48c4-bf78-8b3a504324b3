# Config 資料夾

此資料夾包含所有配置檔案和 JSON 資料檔案。

## 檔案說明

### subscriptions.json
- **用途**: 儲存用戶訂閱資料
- **格式**: JSON
- **內容**: 用戶ID -> 訂閱標的清單的映射
- **範例**:
```json
{
  "1057529499": {
    "BTC-USD": {
      "interval_minutes": 30,
      "symbol_name": "Bitcoin USD 比特幣",
      "added_time": "2025-01-XX..."
    }
  }
}
```

### frequency_lists.json
- **用途**: 儲存頻率傳送清單配置
- **格式**: JSON
- **內容**: 預定義的標的清單，包含上午清單、晚上清單、全日清單
- **範例**:
```json
{
  "morning_list": {
    "name": "上午清單",
    "description": "適合上午交易的標的",
    "symbols": [
      {
        "symbol": "00881.TW",
        "name": "國泰台灣5G+ ETF",
        "interval_minutes": 30
      }
    ]
  }
}
```

## 注意事項

1. 這些檔案會由程式自動讀取和更新
2. 請勿手動修改這些檔案，除非您了解其結構
3. 建議定期備份這些檔案
4. 如果檔案損壞，程式會自動重新建立預設配置 