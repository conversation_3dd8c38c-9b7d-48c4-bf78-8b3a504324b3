bot:
  allowed_groups: []
  allowed_users: []
  name: UnifiedBot
  owner_id: ''
  token: ''
database:
  path: data/bot.db
  type: sqlite
logging:
  backup_count: 5
  file: logs/bot.log
  max_size_mb: 10
plugins:
  AlertPlugin:
    config:
      alert_threshold: 0.05
      notification_channels:
      - telegram
      trend_analysis: true
    enabled: true
  NotifyPlugin:
    config:
      auto_generate: true
      ranking_limit: 20
      report_frequency: daily
    enabled: true
  SubscriptionPlugin:
    config:
      auto_cleanup: true
      max_subscriptions: 100
      update_frequency: 30min
    enabled: true
system:
  auto_restart: true
  debug: false
  log_level: INFO
  max_memory_mb: 512
test_key: test_value
