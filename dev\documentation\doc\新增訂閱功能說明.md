# 📈 新增訂閱功能詳細說明

`telegram_bot_30minV2.py` 的新增訂閱功能包含多種操作方式，我來詳細說明：

## 🔄 操作流程

### 方式一：按鈕操作（主要方式）

1. **點擊「📈 訂閱」按鈕**
   - 觸發 `handle_subscribe_button()` 函數
   - 設定用戶狀態為 `waiting_stock_selection`
   - 顯示標的選擇鍵盤（分頁顯示）

2. **選擇標的**
   - 從 `stock_name_hold_stockonly_SortbyValue.csv` 載入標的清單
   - 每頁顯示 8 個選項（2列4行佈局）
   - 支援分頁瀏覽（⬅️ 上一頁、➡️ 下一頁）
   - 按鈕格式：`SYMBOL\nNAME`

3. **選擇間隔時間**
   - 顯示間隔選擇鍵盤：5分鐘、10分鐘、15分鐘、30分鐘
   - 觸發 `handle_interval_selection()` 函數

4. **完成訂閱**
   - 呼叫 `add_subscription()` 函數
   - 自動啟動訂閱任務
   - 返回主選單

### 方式二：命令操作

- **格式**：`/subscribe <標的代碼> [間隔分鐘]`
- **範例**：`/subscribe BTC 30`
- **預設間隔**：30分鐘
- **支援間隔**：5、10、15、30分鐘

## 🔧 核心功能函數

### `add_subscription()` 函數

```python
def add_subscription(user_id: int, symbol: str, interval_minutes: int, symbol_name: str = None):
```

**功能**：
- 驗證標的存在（透過 CSV 查表）
- 儲存訂閱資訊到 `subscriptions` 字典
- 自動啟動訂閱任務
- 保存到 `subscriptions.json` 檔案

**訂閱資料結構**：
```python
subscriptions[user_id][symbol] = {
    'interval_minutes': interval_minutes,
    'symbol_name': symbol_name,
    'added_time': datetime.now().isoformat()
}
```

### `start_subscription_task()` 函數

```python
def start_subscription_task(user_id: int, symbol: str, interval_minutes: int):
```

**功能**：
- 創建唯一的任務 ID
- 啟動異步訂閱循環任務
- 記錄到 `subscription_tasks` 字典

### `subscription_task_loop()` 函數

```python
async def subscription_task_loop(user_id: int, task_id: str, symbol: str, interval_minutes: int):
```

**功能**：
- 按指定間隔循環執行
- 檢查訂閱是否還存在
- 自動發送技術分析圖表
- 錯誤處理和日誌記錄

## 📊 標的資料來源

### CSV 檔案載入

```python
CSV_FILES = [
    'stock_name_hold_fundsonly_SortbyValue.csv',  # 基金
    'stock_name_hold_stockonly_SortbyValue.csv',  # 股票
    'stock_names_Ady.csv',                        # 其他標的
    'stock_names_coin.csv',                       # 加密貨幣
    'stock_names_ETF.csv',                        # ETF
    'stock_names_watch_index.csv',                # 指數
]
```

### 智能搜尋功能

```python
def find_matching_stocks(query):
```

- 支援股票代碼和名稱模糊搜尋
- 不分大小寫搜尋
- 避免重複結果

## 🎯 用戶狀態管理

### 狀態追蹤

```python
user_states = {}  # 用戶ID -> 當前狀態
user_pages = {}   # 用戶ID -> 當前頁面
```

### 狀態流程

1. `main` - 主選單
2. `waiting_stock_selection` - 等待選擇標的
3. `waiting_interval` - 等待選擇間隔

## 🤖 自動化功能

### 程式啟動時

```python
async def start_all_subscriptions():
```

- 自動載入所有已存在的訂閱
- 重新啟動所有訂閱任務

### 預設訂閱

- 自動為指定用戶（ID: 1057529499）添加預設訂閱
- 預設標的：BTC-USD、ETH-USD
- 預設間隔：30分鐘

## 📱 用戶介面特色

### 動態鍵盤

- 根據用戶狀態顯示不同鍵盤
- 分頁瀏覽支援
- 永遠顯示返回主選單按鈕

### 錯誤處理

- 標的不存在時的提示
- 狀態錯誤時的引導
- 完整的異常處理

## 💾 資料持久化

### 訂閱資料保存

```python
def save_subscriptions():
```

- 自動保存到 `subscriptions.json`
- 支援中文編碼
- 格式化儲存

### 圖片檔案管理

```python
def cleanup_old_images():
```

- 自動清理超過1小時的舊圖片
- 發送後自動刪除圖片檔案

## 📋 使用範例

### 按鈕操作流程

1. 用戶點擊「📈 訂閱」
2. 機器人顯示標的清單（第1頁）
3. 用戶選擇「TSM\n台積電」
4. 機器人顯示間隔選擇鍵盤
5. 用戶選擇「30分鐘」
6. 機器人確認訂閱成功並返回主選單

### 命令操作範例

```
用戶輸入：/subscribe BTC 15
機器人回應：已新增訂閱：BTC-USD (Bitcoin USD 比特幣) 每 15 分鐘
```

## 🔍 技術細節

### 異步處理

- 使用 `asyncio.create_task()` 啟動訂閱任務
- 支援多個用戶同時訂閱不同標的
- 非阻塞式執行

### 記憶體管理

- 定期清理舊圖片檔案
- 發送後立即刪除圖片
- 避免磁碟空間浪費

### 錯誤恢復

- 程式重啟時自動恢復所有訂閱
- 網路錯誤時自動重試
- 完整的日誌記錄

---

這個新增訂閱功能設計完整，支援多種操作方式，具有完善的錯誤處理和用戶體驗，能夠讓用戶方便地管理多個標的的訂閱。 