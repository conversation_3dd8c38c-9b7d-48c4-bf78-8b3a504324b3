"""
配置管理器
負責管理所有配置的載入、保存和熱更新
"""

import asyncio
import json
import logging
import os
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import yaml


class ConfigManager:
    """
    配置管理器
    提供配置的載入、保存、監聽和熱更新功能
    """
    
    def __init__(self):
        """初始化配置管理器"""
        self.logger = logging.getLogger("ConfigManager")
        self.config: Dict[str, Any] = {}
        self.config_file = "config/config.yaml"
        self.plugins_config_file = "config/plugins.yaml"
        self.is_initialized = False
        
        # 配置監聽器
        self.config_watchers: Dict[str, callable] = {}
        self.watch_task: Optional[asyncio.Task] = None
        
        # 配置歷史
        self.config_history: list = []
        self.max_history_size = 50
    
    async def initialize(self):
        """初始化配置管理器"""
        try:
            self.logger.info("初始化配置管理器...")
            
            # 確保配置目錄存在
            self._ensure_config_directory()
            
            # 載入主配置
            await self.load_config()
            
            # 載入插件配置
            await self.load_plugins_config()
            
            # 啟動配置監聽
            await self._start_config_watching()
            
            self.is_initialized = True
            self.logger.info("配置管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"配置管理器初始化失敗: {e}")
            raise
    
    def _ensure_config_directory(self):
        """確保配置目錄存在"""
        config_dir = Path("config")
        if not config_dir.exists():
            config_dir.mkdir(parents=True)
            self.logger.info("創建配置目錄: config/")
    
    async def load_config(self) -> Dict[str, Any]:
        """
        載入主配置檔案
        
        Returns:
            配置字典
        """
        try:
            config_path = Path(self.config_file)
            
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                self.logger.info(f"載入配置檔案: {self.config_file}")
            else:
                # 創建預設配置
                self.config = self._get_default_config()
                await self.save_config()
                self.logger.info(f"創建預設配置檔案: {self.config_file}")
            
            return self.config
            
        except Exception as e:
            self.logger.error(f"載入配置失敗: {e}")
            # 使用預設配置
            self.config = self._get_default_config()
            return self.config
    
    async def load_plugins_config(self) -> Dict[str, Any]:
        """
        載入插件配置檔案
        
        Returns:
            插件配置字典
        """
        try:
            config_path = Path(self.plugins_config_file)
            
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    plugins_config = yaml.safe_load(f) or {}
                self.logger.info(f"載入插件配置檔案: {self.plugins_config_file}")
            else:
                # 創建預設插件配置
                plugins_config = self._get_default_plugins_config()
                await self.save_plugins_config(plugins_config)
                self.logger.info(f"創建預設插件配置檔案: {self.plugins_config_file}")
            
            # 將插件配置合併到主配置中
            self.config["plugins"] = plugins_config
            
            return plugins_config
            
        except Exception as e:
            self.logger.error(f"載入插件配置失敗: {e}")
            # 使用預設插件配置
            plugins_config = self._get_default_plugins_config()
            self.config["plugins"] = plugins_config
            return plugins_config
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取預設配置"""
        return {
            "bot": {
                "name": "UnifiedBot",
                "token": "",
                "owner_id": "",
                "allowed_users": [],
                "allowed_groups": []
            },
            "system": {
                "debug": False,
                "log_level": "INFO",
                "max_memory_mb": 512,
                "auto_restart": True
            },
            "plugins": {
                "auto_load": True,
                "plugins_directory": "plugins",
                "default_plugins": []
            },
            "database": {
                "type": "sqlite",
                "path": "data/bot.db"
            },
            "logging": {
                "file": "logs/bot.log",
                "max_size_mb": 10,
                "backup_count": 5
            }
        }
    
    def _get_default_plugins_config(self) -> Dict[str, Any]:
        """獲取預設插件配置"""
        return {
            "SubscriptionPlugin": {
                "enabled": True,
                "config": {
                    "update_frequency": "30min",
                    "max_subscriptions": 100,
                    "auto_cleanup": True
                }
            },
            "AlertPlugin": {
                "enabled": True,
                "config": {
                    "alert_threshold": 0.05,
                    "trend_analysis": True,
                    "notification_channels": ["telegram"]
                }
            },
            "NotifyPlugin": {
                "enabled": True,
                "config": {
                    "report_frequency": "daily",
                    "ranking_limit": 20,
                    "auto_generate": True
                }
            }
        }
    
    async def save_config(self):
        """保存主配置檔案"""
        try:
            config_path = Path(self.config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            
            # 添加到歷史記錄
            self._add_to_history("main_config", self.config.copy())
            
            self.logger.info(f"配置已保存: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"保存配置失敗: {e}")
            raise
    
    async def save_plugins_config(self, plugins_config: Dict[str, Any]):
        """
        保存插件配置檔案
        
        Args:
            plugins_config: 插件配置字典
        """
        try:
            config_path = Path(self.plugins_config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(plugins_config, f, default_flow_style=False, allow_unicode=True)
            
            # 更新主配置中的插件配置
            self.config["plugins"] = plugins_config
            
            # 添加到歷史記錄
            self._add_to_history("plugins_config", plugins_config.copy())
            
            self.logger.info(f"插件配置已保存: {self.plugins_config_file}")
            
        except Exception as e:
            self.logger.error(f"保存插件配置失敗: {e}")
            raise
    
    def get_config(self, key: str = None, default: Any = None) -> Any:
        """
        獲取配置值
        
        Args:
            key: 配置鍵（支援點號分隔，如 "bot.name"）
            default: 預設值
            
        Returns:
            配置值
        """
        try:
            if key is None:
                return self.config
            
            # 支援點號分隔的鍵
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            self.logger.error(f"獲取配置失敗: {e}")
            return default
    
    async def get_plugin_config(self, plugin_name: str) -> Dict[str, Any]:
        """
        獲取插件配置
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            插件配置字典
        """
        try:
            plugins_config = self.get_config("plugins", {})
            return plugins_config.get(plugin_name, {})
            
        except Exception as e:
            self.logger.error(f"獲取插件配置失敗: {e}")
            return {}
    
    async def update_config(self, key: str, value: Any):
        """
        更新配置值
        
        Args:
            key: 配置鍵（支援點號分隔）
            value: 新值
        """
        try:
            # 支援點號分隔的鍵
            keys = key.split('.')
            config = self.config
            
            # 遍歷到最後一個鍵
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 設置值
            config[keys[-1]] = value
            
            # 保存配置
            await self.save_config()
            
            # 通知配置變更
            await self._notify_config_change(key, value)
            
            self.logger.info(f"配置已更新: {key} = {value}")
            
        except Exception as e:
            self.logger.error(f"更新配置失敗: {e}")
            raise
    
    async def update_plugin_config(self, plugin_name: str, config: Dict[str, Any]):
        """
        更新插件配置
        
        Args:
            plugin_name: 插件名稱
            config: 新配置
        """
        try:
            plugins_config = self.get_config("plugins", {})
            plugins_config[plugin_name] = config
            
            await self.save_plugins_config(plugins_config)
            
            # 通知插件配置變更
            await self._notify_config_change(f"plugins.{plugin_name}", config)
            
            self.logger.info(f"插件配置已更新: {plugin_name}")
            
        except Exception as e:
            self.logger.error(f"更新插件配置失敗: {e}")
            raise
    
    def watch_config_changes(self, key: str, callback: callable):
        """
        監聽配置變更
        
        Args:
            key: 配置鍵
            callback: 回調函數
        """
        self.config_watchers[key] = callback
        self.logger.info(f"註冊配置監聽器: {key}")
    
    def unwatch_config_changes(self, key: str):
        """
        取消監聽配置變更
        
        Args:
            key: 配置鍵
        """
        if key in self.config_watchers:
            del self.config_watchers[key]
            self.logger.info(f"取消配置監聽器: {key}")
    
    async def _notify_config_change(self, key: str, value: Any):
        """通知配置變更"""
        try:
            # 通知監聽器
            if key in self.config_watchers:
                callback = self.config_watchers[key]
                if asyncio.iscoroutinefunction(callback):
                    await callback(key, value)
                else:
                    callback(key, value)
            
            # 通知通配符監聽器
            for pattern, callback in self.config_watchers.items():
                if pattern != key and self._match_pattern(key, pattern):
                    if asyncio.iscoroutinefunction(callback):
                        await callback(key, value)
                    else:
                        callback(key, value)
            
        except Exception as e:
            self.logger.error(f"通知配置變更失敗: {e}")
    
    def _match_pattern(self, key: str, pattern: str) -> bool:
        """檢查配置鍵是否匹配模式"""
        if pattern == "*":
            return True
        
        if "*" not in pattern:
            return key == pattern
        
        # 處理通配符模式
        pattern_parts = pattern.split('.')
        key_parts = key.split('.')
        
        if len(pattern_parts) != len(key_parts):
            return False
        
        for i, part in enumerate(pattern_parts):
            if part != "*" and part != key_parts[i]:
                return False
        
        return True
    
    async def _start_config_watching(self):
        """啟動配置檔案監聽"""
        try:
            # 這裡可以實現檔案系統監聽
            # 目前使用簡單的輪詢方式
            self.watch_task = asyncio.create_task(self._watch_config_files())
            self.logger.info("配置檔案監聽已啟動")
            
        except Exception as e:
            self.logger.error(f"啟動配置監聽失敗: {e}")
    
    async def _watch_config_files(self):
        """監聽配置檔案變更"""
        try:
            while self.is_initialized:
                # 檢查配置檔案是否有變更
                await self._check_config_file_changes()
                await asyncio.sleep(5)  # 每5秒檢查一次
                
        except asyncio.CancelledError:
            self.logger.info("配置檔案監聽已停止")
        except Exception as e:
            self.logger.error(f"配置檔案監聽錯誤: {e}")
    
    async def _check_config_file_changes(self):
        """檢查配置檔案變更"""
        try:
            # 檢查主配置檔案
            config_path = Path(self.config_file)
            if config_path.exists():
                stat = config_path.stat()
                last_modified = getattr(self, '_last_config_modified', 0)
                
                if stat.st_mtime > last_modified:
                    self._last_config_modified = stat.st_mtime
                    await self.load_config()
                    self.logger.info("檢測到主配置檔案變更，已重新載入")
            
            # 檢查插件配置檔案
            plugins_config_path = Path(self.plugins_config_file)
            if plugins_config_path.exists():
                stat = plugins_config_path.stat()
                last_modified = getattr(self, '_last_plugins_config_modified', 0)
                
                if stat.st_mtime > last_modified:
                    self._last_plugins_config_modified = stat.st_mtime
                    await self.load_plugins_config()
                    self.logger.info("檢測到插件配置檔案變更，已重新載入")
            
        except Exception as e:
            self.logger.error(f"檢查配置檔案變更失敗: {e}")
    
    def _add_to_history(self, config_type: str, config_data: Dict[str, Any]):
        """添加到配置歷史"""
        history_entry = {
            "type": config_type,
            "data": config_data,
            "timestamp": datetime.now().isoformat()
        }
        
        self.config_history.append(history_entry)
        
        # 限制歷史記錄大小
        if len(self.config_history) > self.max_history_size:
            self.config_history.pop(0)
    
    def get_config_history(self, config_type: str = None, limit: int = 10) -> list:
        """
        獲取配置歷史
        
        Args:
            config_type: 配置類型（可選）
            limit: 限制數量
            
        Returns:
            配置歷史列表
        """
        if config_type:
            filtered_history = [h for h in self.config_history if h["type"] == config_type]
        else:
            filtered_history = self.config_history
        
        return filtered_history[-limit:]
    
    def get_config_stats(self) -> Dict[str, Any]:
        """獲取配置統計信息"""
        return {
            "total_configs": len(self.config),
            "total_plugins": len(self.get_config("plugins", {})),
            "history_size": len(self.config_history),
            "watchers_count": len(self.config_watchers),
            "is_initialized": self.is_initialized
        }
    
    async def reload_config(self):
        """重新載入配置"""
        try:
            self.logger.info("重新載入配置...")
            
            await self.load_config()
            await self.load_plugins_config()
            
            self.logger.info("配置重新載入完成")
            
        except Exception as e:
            self.logger.error(f"重新載入配置失敗: {e}")
            raise
    
    async def cleanup(self):
        """清理配置管理器"""
        try:
            self.logger.info("清理配置管理器...")
            
            # 停止配置監聽
            if self.watch_task:
                self.watch_task.cancel()
                try:
                    await self.watch_task
                except asyncio.CancelledError:
                    pass
            
            # 清空監聽器
            self.config_watchers.clear()
            
            # 清空歷史記錄
            self.config_history.clear()
            
            self.is_initialized = False
            self.logger.info("配置管理器清理完成")
            
        except Exception as e:
            self.logger.error(f"清理配置管理器失敗: {e}")


# 便捷函數
def create_config_manager() -> ConfigManager:
    """創建配置管理器實例"""
    return ConfigManager()


async def setup_config_manager() -> ConfigManager:
    """設置並初始化配置管理器"""
    config_manager = create_config_manager()
    await config_manager.initialize()
    return config_manager 