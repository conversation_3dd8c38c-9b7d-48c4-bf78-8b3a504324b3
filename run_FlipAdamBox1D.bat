@echo off
chcp 65001 >nul
echo ========================================
echo 啟動 FlipAdamBox1D 分析程式
echo ========================================
echo.

:: 檢查 Python 環境
echo 檢查 Python 環境...
python --version
if errorlevel 1 (
    echo 錯誤：找不到 Python，請確認 Python 已安裝並加入 PATH
    pause
    exit /b 1
)

:: 檢查虛擬環境
if exist "venv312\Scripts\activate.bat" (
    echo 啟動 venv312 虛擬環境...
    call venv312\Scripts\activate.bat
) else if exist "venv\Scripts\activate.bat" (
    echo 啟動 venv 虛擬環境...
    call venv\Scripts\activate.bat
) else (
    echo 警告：找不到虛擬環境，使用系統 Python
)

:: 檢查必要檔案
echo 檢查必要檔案...
if not exist "FlipAdamBox1D.py" (
    echo 錯誤：找不到 FlipAdamBox1D.py
    pause
    exit /b 1
)

if not exist "stock_names_coin.csv" (
    echo 錯誤：找不到 stock_names_coin.csv
    pause
    exit /b 1
)

if not exist "stock_name_hold_stockonly_SortbyValue.csv" (
    echo 錯誤：找不到 stock_name_hold_stockonly_SortbyValue.csv
    pause
    exit /b 1
)

if not exist "stock_name_hold_fundsonly_SortbyValue.csv" (
    echo 錯誤：找不到 stock_name_hold_fundsonly_SortbyValue.csv
    pause
    exit /b 1
)

if not exist "stock_names_watch_index.csv" (
    echo 錯誤：找不到 stock_names_watch_index.csv
    pause
    exit /b 1
)

if not exist "FlipAdamCon2.py" (
    echo 錯誤：找不到 FlipAdamCon2.py
    pause
    exit /b 1
)

:: 建立 figure 目錄
if not exist "figure" (
    echo 建立 figure 目錄...
    mkdir figure
)

echo.
echo ========================================
echo 開始執行 FlipAdamBox1D 分析
echo ========================================
echo 時間：%date% %time%
echo.

:: 執行 Python 程式
REM 啟用 venv 虛擬環境
REM (已於前面自動判斷並啟用)
REM call .\venv\Scripts\activate.bat
REM 執行 FlipAdamBox1D.py
python FlipAdamBox1D.py

:: 檢查執行結果
if errorlevel 1 (
    echo.
    echo ========================================
    echo 程式執行失敗！
    echo ========================================
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo 程式執行完成！
    echo ========================================
    echo 時間：%date% %time%
    echo.
    echo 分析結果已儲存在 figure 目錄中
    echo HTML 報告已生成
    echo.
)

:: 顯示 figure 目錄內容
echo figure 目錄內容：
dir figure\*.png /b 2>nul
if errorlevel 1 (
    echo 沒有找到 PNG 檔案
)

echo.
echo 按任意鍵結束...
pause >nul 