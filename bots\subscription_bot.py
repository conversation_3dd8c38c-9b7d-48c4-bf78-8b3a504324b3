import logging
from telegram import Update, <PERSON>ly<PERSON><PERSON>boardMarkup, KeyboardButton, ReplyKeyboardRemove
from telegram.ext import Application, CommandHandler, ContextTypes, MessageHandler, filters
import asyncio
import sys
import pandas as pd
import os
import re
import yfinance as yf
from telegram import InputMediaPhoto
from telegram.error import RetryAfter, TimedOut, NetworkError
from core.analysis.flip_adam_30min import getMLFlipAdambySymbol, getMLFlipAdambySymbolWithData, get_cffi_session
from core.analysis.flip_adam_1hr import getMLFlipAdambySymbol as getMLFlipAdambySymbol1HR, getMLFlipAdambySymbolWithData as getMLFlipAdambySymbolWithData1HR
from core.analysis.flip_adam_4hr import getMLFlipAdambySymbol as getMLFlipAdambySymbol4HR, getMLFlipAdambySymbolWithData as getMLFlipAdambySymbolWithData4HR
import telegram
from telegram.request import HTTPXRequest
import io
from PIL import Image
import requests
import config
import aiohttp
import time
from datetime import datetime, timedelta
import json
from core.utils.memory_manager import cleanup_variables, log_memory_usage, monitor_memory

# 設定日誌
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

# 您的 Bot Token
TOKEN = "7749365105:AAE448qrt8TNTjgwG9CKg6GM1rx3Xr1uAUo"

# 需要合併查詢的CSV檔案
CSV_FILES = [
    'data/raw/stock_name_hold_fundsonly_SortbyValue.csv',
    'data/raw/stock_name_hold_stockonly_SortbyValue.csv',
    'data/raw/stock_names_Ady.csv',
    'data/raw/stock_names_coin.csv',
    'data/raw/stock_names_ETF.csv',
    'data/raw/stock_names_watch_index.csv',
]

# 頻率傳送任務管理
frequency_tasks = {}  # 用戶ID -> 任務資訊
task_counter = 0

# 訂閱管理
subscriptions = {}  # 用戶ID -> {symbol: interval_minutes}
subscription_tasks = {}  # 用戶ID -> {symbol: task_id}

# 用戶狀態管理
user_states = {}  # 用戶ID -> 當前狀態

# 分頁管理
user_pages = {}  # 用戶ID -> 當前頁面

# 頻率傳送清單管理
frequency_lists = {}  # 載入的頻率傳送清單
user_selected_list = {}  # 用戶ID -> 選擇的清單

def create_main_keyboard():
    """創建主選單鍵盤"""
    keyboard = [
        [KeyboardButton("📈 訂閱"), KeyboardButton("❌ 取消訂閱")],
        [KeyboardButton("📋 檢查訂閱"), KeyboardButton("🔄 頻率傳送清單")],
        [KeyboardButton("⏰ 時間版本選擇")],
        [KeyboardButton("❓ 幫助")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def create_interval_keyboard():
    """創建間隔選擇鍵盤"""
    keyboard = [
        [KeyboardButton("5分鐘"), KeyboardButton("10分鐘")],
        [KeyboardButton("15分鐘"), KeyboardButton("30分鐘")],
        [KeyboardButton("🔙 返回主選單")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def create_cancel_keyboard():
    """創建取消鍵盤"""
    keyboard = [
        [KeyboardButton("🔙 返回主選單")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def create_frequency_list_keyboard():
    """創建頻率傳送清單鍵盤"""
    keyboard = [
        [KeyboardButton("🌅 上午清單"), KeyboardButton("🌙 晚上清單")],
        [KeyboardButton("🌞 全日清單")],
        [KeyboardButton("🔙 返回主選單")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def create_list_action_keyboard():
    """創建清單動作選擇鍵盤"""
    keyboard = [
        [KeyboardButton("✅ 新增到現有訂閱"), KeyboardButton("🔄 替換現有訂閱")],
        [KeyboardButton("🔙 返回主選單")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def create_time_version_keyboard():
    """創建時間版本選擇鍵盤"""
    keyboard = [
        [KeyboardButton("⏰ 30分鐘版本"), KeyboardButton("⏰ 1小時版本")],
        [KeyboardButton("⏰ 4小時版本")],
        [KeyboardButton("🔙 返回主選單")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)



def create_unsubscribe_keyboard(user_subs):
    """創建取消訂閱的動態鍵盤"""
    keyboard = []
    row = []
    
    # 如果有多個訂閱，先添加一鍵取消按鈕在最上方
    if len(user_subs) > 1:
        keyboard.append([KeyboardButton("🗑️ 一鍵取消所有訂閱")])
        keyboard.append([])  # 空行分隔
    
    for i, (symbol, info) in enumerate(user_subs.items()):
        interval = info['interval_minutes']
        name = info.get('symbol_name', symbol)
        # 創建按鈕文字，包含標的代碼和間隔
        button_text = f"❌ {symbol} ({interval}分)"
        row.append(KeyboardButton(button_text))
        
        # 每行最多2個按鈕
        if len(row) == 2:
            keyboard.append(row)
            row = []
    
    # 添加最後一行（如果還有按鈕）
    if row:
        keyboard.append(row)
    
    # 添加返回按鈕
    keyboard.append([KeyboardButton("🔙 返回主選單")])
    
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def sanitize_filename(symbol):
    """處理檔案名稱中的特殊符號"""
    # 將特殊符號替換為底線
    return re.sub(r'[<>:"/\\|?*]', '_', symbol)

@monitor_memory
def load_stock_codes():
    stock_dict = {}
    name_to_symbol = {}  # 新增：名稱到代碼的映射
    for csv_file in CSV_FILES:
        try:
            df = pd.read_csv(csv_file)
            # 避免重複，後面的檔案會覆蓋前面的同名代碼
            stock_dict.update(dict(zip(df['Symbol'], df['Name'])))
            # 建立名稱到代碼的映射（不分大小寫）
            for symbol, name in zip(df['Symbol'], df['Name']):
                name_to_symbol[name.lower()] = symbol
        except Exception as e:
            logging.error(f"讀取 {csv_file} 時發生錯誤: {e}")
    
    # 記憶體清理
    try:
        variables_to_clean = {}
        for var_name in ['csv_file', 'df']:
            if var_name in locals():
                variables_to_clean[var_name] = locals()[var_name]
        
        cleanup_variables(**variables_to_clean)
        log_memory_usage(f"股票代碼載入完成 - {len(stock_dict)} 個標的")
    except Exception as cleanup_error:
        logging.error(f"股票代碼載入記憶體清理時發生錯誤: {cleanup_error}")
    
    return stock_dict, name_to_symbol

# 載入股票代碼資料
STOCK_CODES, NAME_TO_SYMBOL = load_stock_codes()

@monitor_memory
def find_matching_stocks(query):
    """搜尋匹配的股票代碼或名稱"""
    query = query.lower()
    matches = []
    found_via_yfinance = False  # 新增：標記是否透過 yfinance 找到
    
    # 搜尋代碼（不分大小寫）
    for symbol in STOCK_CODES.keys():
        if query in symbol.lower():
            matches.append((symbol, STOCK_CODES[symbol]))
    
    # 搜尋名稱
    for name, symbol in NAME_TO_SYMBOL.items():
        if query in name:
            if symbol not in [m[0] for m in matches]:  # 避免重複
                if symbol in STOCK_CODES:
                    matches.append((symbol, STOCK_CODES[symbol]))
    
    # 如果本地清單中找不到，使用 yfinance 搜尋
    if not matches:
        try:
            logging.info(f"本地清單中未找到 {query}，嘗試使用 yfinance 搜尋")
            
            # 使用 yfinance 搜尋
            ticker = yf.Ticker(query.upper())
            info = ticker.info
            
            # 檢查是否有有效資訊
            if info and 'symbol' in info and info['symbol']:
                symbol = info['symbol']
                name = info.get('longName', info.get('shortName', symbol))
                
                # 檢查是否為有效的股票代碼（避免一些無效結果）
                if symbol and len(symbol) > 0 and not symbol.startswith('^'):
                    matches.append((symbol, name))
                    found_via_yfinance = True  # 標記為透過 yfinance 找到
                    logging.info(f"yfinance 搜尋成功: {symbol} ({name})")
                else:
                    logging.warning(f"yfinance 搜尋結果無效: {symbol}")
            else:
                logging.warning(f"yfinance 搜尋無結果: {query}")
                
        except Exception as e:
            logging.error(f"yfinance 搜尋時發生錯誤: {e}")
    
    # 記憶體清理
    try:
        variables_to_clean = {}
        for var_name in ['symbol', 'name', 'ticker', 'info']:
            if var_name in locals():
                variables_to_clean[var_name] = locals()[var_name]
        
        cleanup_variables(**variables_to_clean)
        log_memory_usage(f"股票搜尋完成 - 查詢: {query}, 結果: {len(matches)} 個")
    except Exception as cleanup_error:
        logging.error(f"股票搜尋記憶體清理時發生錯誤: {cleanup_error}")
    
    return matches, found_via_yfinance  # 回傳結果和標記

def load_frequency_lists():
    """載入頻率傳送清單"""
    global frequency_lists
    try:
        frequency_lists_path = os.path.join('config', 'frequency_lists.json')
        if os.path.exists(frequency_lists_path):
            with open(frequency_lists_path, 'r', encoding='utf-8') as f:
                frequency_lists = json.load(f)
                logging.info(f"載入頻率傳送清單: {len(frequency_lists)} 個清單")
        else:
            logging.error("頻率傳送清單檔案不存在")
            frequency_lists = {}
    except Exception as e:
        logging.error(f"載入頻率傳送清單失敗: {e}")
        frequency_lists = {}

def load_subscriptions():
    """載入訂閱資料"""
    global subscriptions
    try:
        subscriptions_path = os.path.join('config', 'subscriptions.json')
        if os.path.exists(subscriptions_path):
            with open(subscriptions_path, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
                # 確保載入的資料是字典
                if isinstance(loaded_data, dict):
                    # 確保用戶ID是整數
                    subscriptions = {int(k): v for k, v in loaded_data.items()}
                    logging.info(f"載入訂閱資料: {len(subscriptions)} 個用戶")
                else:
                    logging.error(f"訂閱資料格式錯誤，預期字典但得到 {type(loaded_data)}")
                    subscriptions = {}
        else:
            # 如果檔案不存在，創建預設訂閱
            subscriptions = {}
            logging.info("訂閱檔案不存在，將創建預設訂閱")
    except Exception as e:
        logging.error(f"載入訂閱資料失敗: {e}")
        subscriptions = {}
    
    # 檢查是否有預設訂閱，如果沒有則添加
    default_user_id = 1057529499  # 你的用戶ID
    if default_user_id not in subscriptions:
        subscriptions[default_user_id] = {}
    
    # 添加預設訂閱
    default_subscriptions = {
        'BTC-USD': {
            'interval_minutes': 30,
            'symbol_name': 'Bitcoin USD 比特幣',
            'added_time': datetime.now().isoformat()
        },
        'ETH-USD': {
            'interval_minutes': 30,
            'symbol_name': 'Ethereum USD 以太幣',
            'added_time': datetime.now().isoformat()
        }
    }
    
    # 檢查並添加預設訂閱（如果不存在）
    added_defaults = []
    for symbol, info in default_subscriptions.items():
        if symbol not in subscriptions[default_user_id]:
            subscriptions[default_user_id][symbol] = info
            added_defaults.append(symbol)
    
    if added_defaults:
        logging.info(f"已添加預設訂閱: {', '.join(added_defaults)}")
        save_subscriptions()

def save_subscriptions():
    """保存訂閱資料"""
    try:
        subscriptions_path = os.path.join('config', 'subscriptions.json')
        with open(subscriptions_path, 'w', encoding='utf-8') as f:
            json.dump(subscriptions, f, ensure_ascii=False, indent=2)
        logging.info("訂閱資料已保存")
    except Exception as e:
        logging.error(f"保存訂閱資料失敗: {e}")

def add_subscription(user_id: int, symbol: str, interval_minutes: int, symbol_name: str = None):
    """新增訂閱"""
    if user_id not in subscriptions:
        subscriptions[user_id] = {}
    
    # 如果沒有提供symbol_name，則查表確認標的存在
    if symbol_name is None:
        matches, _ = find_matching_stocks(symbol)
        if not matches:
            return False, f"查無此標的：{symbol}"
        yfinance_symbol, symbol_name = matches[0]
    else:
        yfinance_symbol = symbol
    
    subscriptions[user_id][yfinance_symbol] = {
        'interval_minutes': interval_minutes,
        'symbol_name': symbol_name,
        'added_time': datetime.now().isoformat()
    }
    save_subscriptions()
    
    return True, f"已新增訂閱：{yfinance_symbol} ({symbol_name}) 每 {interval_minutes} 分鐘"

def remove_subscription(user_id: int, symbol: str):
    """刪除訂閱"""
    if user_id not in subscriptions:
        return False, "您沒有任何訂閱"
    
    # 查表確認標的存在
    matches, _ = find_matching_stocks(symbol)
    if not matches:
        return False, f"查無此標的：{symbol}"
    
    yfinance_symbol, symbol_name = matches[0]
    if yfinance_symbol in subscriptions[user_id]:
        del subscriptions[user_id][yfinance_symbol]
        save_subscriptions()
        
        # 停止訂閱任務
        stop_subscription_task(user_id, yfinance_symbol)
        
        return True, f"已刪除訂閱：{yfinance_symbol} ({symbol_name})"
    else:
        return False, f"您沒有訂閱：{yfinance_symbol}"

def remove_all_subscriptions(user_id: int):
    """一鍵取消所有訂閱"""
    if user_id not in subscriptions or not subscriptions[user_id]:
        return False, "您沒有任何訂閱"
    
    # 取得所有訂閱的標的
    user_subs = subscriptions[user_id].copy()
    removed_count = len(user_subs)
    
    # 停止所有訂閱任務
    for symbol in user_subs.keys():
        stop_subscription_task(user_id, symbol)
    
    # 清空用戶的所有訂閱
    subscriptions[user_id].clear()
    save_subscriptions()
    
    return True, f"已取消所有訂閱（共 {removed_count} 個標的）"

def get_user_subscriptions(user_id: int):
    """取得用戶的訂閱列表"""
    if user_id not in subscriptions:
        return {}
    
    user_subs = subscriptions[user_id]
    # 確保返回的是字典
    if isinstance(user_subs, dict):
        return user_subs
    else:
        logging.error(f"用戶 {user_id} 的訂閱資料格式錯誤，預期字典但得到 {type(user_subs)}")
        return {}

def start_subscription_task(user_id: int, symbol: str, interval_minutes: int):
    """啟動訂閱任務"""
    global task_counter
    task_counter += 1
    task_id = f"sub_{task_counter}"
    
    if user_id not in subscription_tasks:
        subscription_tasks[user_id] = {}
    
    subscription_tasks[user_id][symbol] = task_id
    
    # 啟動異步任務
    asyncio.create_task(subscription_task_loop(user_id, task_id, symbol, interval_minutes))
    logging.info(f"啟動訂閱任務: 用戶 {user_id}, 標的 {symbol}, 間隔 {interval_minutes} 分鐘")

def stop_subscription_task(user_id: int, symbol: str):
    """停止訂閱任務"""
    if user_id in subscription_tasks and symbol in subscription_tasks[user_id]:
        del subscription_tasks[user_id][symbol]
        logging.info(f"停止訂閱任務: 用戶 {user_id}, 標的 {symbol}")

async def subscription_task_loop(user_id: int, task_id: str, symbol: str, interval_minutes: int):
    """訂閱任務循環"""
    while True:
        try:
            # 等待指定的間隔時間
            await asyncio.sleep(interval_minutes * 60)
            
            # 檢查訂閱是否還存在
            if (user_id not in subscriptions or 
                symbol not in subscriptions[user_id] or
                user_id not in subscription_tasks or
                symbol not in subscription_tasks[user_id]):
                logging.info(f"訂閱已取消: 用戶 {user_id}, 標的 {symbol}")
                break
            
            # 發送訂閱圖片
            await send_subscription_image(user_id, symbol)
            
            logging.info(f"訂閱任務: 已發送 {symbol} 圖片給用戶 {user_id}")
            
        except Exception as e:
            logging.error(f"訂閱任務發生錯誤: {e}")
            break

@monitor_memory
async def send_subscription_image(user_id: int, symbol: str):
    """發送訂閱圖片"""
    try:
        # 確保 figure 目錄存在
        if not os.path.exists('figure'):
            os.makedirs('figure')
        
        # 生成新的圖片
        session = get_cffi_session()
        image_path, analysis_data = getMLFlipAdambySymbolWithData(symbol, session=session)
        
        # 儲存 analysis_data
        if analysis_data:
            save_analysis_data(symbol, analysis_data, "30min")
        
        if image_path and os.path.exists(image_path):
            # 發送圖片給指定用戶
            if await send_photo_to_user(user_id, image_path, f"{symbol} 的技術分析圖表 (訂閱更新)"):
                logging.info(f"訂閱: 成功發送 {symbol} 圖片給用戶 {user_id}")
            else:
                logging.error(f"訂閱: 發送 {symbol} 圖片給用戶 {user_id} 失敗")
        else:
            logging.error(f"訂閱: 無法生成 {symbol} 圖片")
            
    except Exception as e:
        logging.error(f"訂閱發送圖片時發生錯誤: {e}")
    finally:
        # 記憶體清理
        try:
            variables_to_clean = {}
            for var_name in ['session', 'image_path']:
                if var_name in locals():
                    variables_to_clean[var_name] = locals()[var_name]
            
            cleanup_variables(**variables_to_clean)
            log_memory_usage(f"訂閱圖片發送完成 - {symbol}")
        except Exception as cleanup_error:
            logging.error(f"訂閱圖片發送記憶體清理時發生錯誤: {cleanup_error}")

@monitor_memory
async def send_multi_version_images(user_id: int, symbol: str, versions: list):
    """發送多版本圖片"""
    try:
        if not os.path.exists('figure'):
            os.makedirs('figure')
        
        session = get_cffi_session()
        symbol_name = get_symbol_name(symbol)
        
        for version in versions:
            try:
                if version == "30分鐘":
                    image_path, analysis_data = getMLFlipAdambySymbolWithData(symbol, session=session)
                    caption = f"{symbol_name}-30分鐘版本"
                    time_version = "30min"
                elif version == "1小時":
                    image_path, analysis_data = getMLFlipAdambySymbolWithData1HR(symbol, session=session)
                    caption = f"{symbol_name}-1小時版本"
                    time_version = "1hr"
                elif version == "4小時":
                    image_path, analysis_data = getMLFlipAdambySymbolWithData4HR(symbol, session=session)
                    caption = f"{symbol_name}-4小時版本"
                    time_version = "4hr"
                else:
                    continue
                
                # 儲存 analysis_data
                if analysis_data:
                    save_analysis_data(symbol, analysis_data, time_version)
                
                if image_path and os.path.exists(image_path):
                    if await send_photo_to_user(user_id, image_path, caption):
                        logging.info(f"多版本: 成功發送 {symbol} {version} 圖片給用戶 {user_id}")
                    else:
                        logging.error(f"多版本: 發送 {symbol} {version} 圖片給用戶 {user_id} 失敗")
                else:
                    logging.error(f"多版本: 無法生成 {symbol} {version} 圖片")
                    
            except Exception as e:
                logging.error(f"多版本: 生成 {symbol} {version} 圖片時發生錯誤: {e}")
                
    except Exception as e:
        logging.error(f"多版本發送圖片時發生錯誤: {e}")
    finally:
        try:
            variables_to_clean = {}
            for var_name in ['session', 'image_path', 'symbol_name']:
                if var_name in locals():
                    variables_to_clean[var_name] = locals()[var_name]
            
            cleanup_variables(**variables_to_clean)
            log_memory_usage(f"多版本圖片發送完成 - {symbol}")
        except Exception as cleanup_error:
            logging.error(f"多版本圖片發送記憶體清理時發生錯誤: {cleanup_error}")

def get_symbol_name(symbol):
    """獲取標的名稱"""
    try:
        # 從所有CSV檔案中查找標的名稱
        for csv_file in CSV_FILES:
            try:
                df = pd.read_csv(csv_file)
                if 'Symbol' in df.columns and 'Name' in df.columns:
                    symbol_row = df[df['Symbol'] == symbol]
                    if not symbol_row.empty:
                        return symbol_row.iloc[0]['Name']
            except Exception as e:
                logging.error(f"讀取 {csv_file} 時發生錯誤: {e}")
        
        # 如果找不到，返回原始符號
        return symbol
    except Exception as e:
        logging.error(f"獲取標的名稱時發生錯誤: {e}")
        return symbol

def format_ranking_message(results, time_version):
    """格式化排名訊息"""
    if not results:
        return f"❌ 無法取得 {time_version} 的排名資料"
    
    # 按漲跌幅排序（從大到小）
    sorted_results = sorted(results, key=lambda x: x['percentage_change'], reverse=True)
    
    message = f"📊 {time_version} 漲跌幅排行\n"
    message += f"📅 分析時間: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
    message += f"📈 總計分析: {len(results)} 個標的\n"
    message += f"🔄 資料同步: ✅ 與產圖邏輯完全一致\n\n"
    
    # 顯示所有標的的漲跌幅排行
    message += "📈 完整漲跌幅排行:\n"
    for i, result in enumerate(sorted_results, 1):
        name = get_symbol_name(result['symbol'])
        direction_emoji = "🚀" if result['direction'] == '漲' else "📉" if result['direction'] == '跌' else "➡️"
        message += f"{i:2d}. {direction_emoji} {result['symbol']} ({name})\n"
        message += f"    漲跌幅: {result['percentage_change']:+.2f}% | 價差: {result['price_difference']:+.2f}\n"
        message += f"    趨勢: {result['trend_signal']} | 計算: {result['reflection_direction']}\n"
        message += f"    📈 趨勢線: {result.get('current_trend_line', 0):.2f} | 🛡️ 停損價: {result.get('current_trailing_stop', 0):.2f}\n"
        message += f"    📊 ATR: {result.get('current_atr', 0):.2f} (週期:{result.get('best_atr_period', 0)} 倍數:{result.get('best_atr_multiple', 0):.1f})\n"
        
        # 顯示目前信號
        latest_signals = result.get('latest_signals', [])
        if latest_signals:
            signal_emojis = []
            for signal in latest_signals:
                if signal == "做多":
                    signal_emojis.append("🟢")
                elif signal == "做空":
                    signal_emojis.append("🔴")
                elif signal == "跳空":
                    signal_emojis.append("🟣")
            message += f"    📊 目前信號: {' '.join(signal_emojis)} {' '.join(latest_signals)}\n"
        else:
            message += f"    📊 目前信號: ⚪ 無信號\n"
        
        # 顯示交易模擬建議
        win_rate = result.get('win_rate', 0.0)
        total_trades = result.get('total_trades', 0)
        if total_trades > 0:
            message += f"    📈 回測勝率: {win_rate*100:.1f}% ({result.get('winning_trades', 0)}/{total_trades})\n"
            
            # 根據勝率和信號給出建議
            if latest_signals:
                if win_rate >= 0.6:
                    message += f"    💡 建議: ✅ 強烈推薦 (勝率優秀)\n"
                elif win_rate >= 0.5:
                    message += f"    💡 建議: ⚠️ 謹慎考慮 (勝率一般)\n"
                else:
                    message += f"    💡 建議: ❌ 不建議 (勝率偏低)\n"
            else:
                message += f"    💡 建議: ⏸️ 觀望 (無交易信號)\n"
        else:
            message += f"    📈 回測勝率: 無交易記錄\n"
            message += f"    💡 建議: ⏸️ 觀望 (無交易記錄)\n"
        
        message += "\n"
    
    # 添加信號分布統計
    if results:
        signal_stats = {}
        for result in results:
            latest_signals = result.get('latest_signals', [])
            for signal in latest_signals:
                signal_stats[signal] = signal_stats.get(signal, 0) + 1
        
        if signal_stats:
            message += "📊 信號分布統計:\n"
            for signal, count in signal_stats.items():
                emoji = "🟢" if signal == "做多" else "🔴" if signal == "做空" else "🟣"
                message += f"    {emoji} {signal}: {count} 個標的\n"
    
    return message



async def send_photo_to_user(user_id: int, photo_path: str, caption: str):
    """發送圖片給指定用戶"""
    try:
        logging.info(f"準備發送圖片給用戶 {user_id}: {photo_path}")
        result = await send_photo_with_aiohttp(TOKEN, user_id, photo_path, caption)
        logging.info(f"發送圖片結果: {result}")
        
        if result.get("ok"):
            # 成功發送後刪除圖片檔案
            try:
                os.remove(photo_path)
                logging.info(f"成功刪除圖片檔案: {photo_path}")
            except Exception as e:
                logging.warning(f"刪除圖片檔案失敗: {photo_path}, 錯誤: {e}")
            return True
        else:
            logging.error(f"發送圖片給用戶 {user_id} 失敗: {result}")
            return False
    except Exception as e:
        logging.error(f"發送圖片給用戶 {user_id} 時發生錯誤: {e}", exc_info=True)
        return False

async def start_all_subscriptions():
    """啟動所有訂閱任務，並先對每個訂閱項目執行一次繪圖並發送"""
    for user_id, user_subs in subscriptions.items():
        # 為每個用戶的訂閱標的生成圖片
        success_count = 0
        total_count = len(user_subs)
        analysis_results = []  # 保存所有分析結果
        
        for symbol, info in user_subs.items():
            interval_minutes = info['interval_minutes']
            # 啟動訂閱任務前，先執行一次繪圖並發送
            try:
                session = get_cffi_session()
                image_path, analysis_data = getMLFlipAdambySymbolWithData(symbol, session=session)
                if image_path and os.path.exists(image_path):
                    # 發送圖片給用戶
                    await send_photo_to_user(user_id, image_path, f"{symbol} 的技術分析圖表 (啟動自動推播)")
                    success_count += 1
                    
                    # 保存分析資料，避免重複計算
                    if analysis_data:
                        analysis_data['symbol'] = symbol
                        analysis_results.append(analysis_data)
                        
                        # 儲存 analysis_data
                        save_analysis_data(symbol, analysis_data, "30min")
                        
            except Exception as e:
                logging.error(f"啟動時預先繪圖/發送 {symbol} 失敗: {e}")
            start_subscription_task(user_id, symbol, interval_minutes)
        
        # 所有圖片都產生完成後，為該用戶生成排行
        if success_count > 0 and analysis_results:
            try:
                # 直接使用已計算的分析結果生成排行
                ranking_message = format_ranking_message(analysis_results, "30分鐘")
                ranking_message += f"\n📊 訂閱標的分析統計:\n"
                ranking_message += f"✅ 成功分析: {len(analysis_results)} 個標的\n"
                ranking_message += f"❌ 分析失敗: {total_count - len(analysis_results)} 個標的\n"
                ranking_message += f"📈 成功率: {len(analysis_results)/total_count*100:.1f}%\n"
                
                # 發送排行報告
                if len(ranking_message) > 4000:
                    segments = [ranking_message[i:i+4000] for i in range(0, len(ranking_message), 4000)]
                    for i, segment in enumerate(segments):
                        if i == 0:
                            await send_message_to_user(user_id, segment)
                        else:
                            await send_message_to_user(user_id, f"(續 {i+1}/{len(segments)})\n{segment}")
                else:
                    await send_message_to_user(user_id, ranking_message)
                    
                logging.info(f"用戶 {user_id} 的排行分析完成")
            except Exception as e:
                logging.error(f"用戶 {user_id} 的排行分析失敗: {e}")
    
    logging.info("已啟動所有訂閱任務，並預先繪圖、發送與排行分析完成")

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /start 命令"""
    user_id = update.effective_user.id
    user_states[user_id] = 'main'  # 設定用戶狀態為主選單
    
    welcome_text = (
        "您好！我是 FlipAdam 通知機器人 🤖\n\n"
        "📊 功能特色：\n"
        "• 多時間版本技術分析圖表（30分鐘/1小時/4小時）\n"
        "• 支援股票、ETF、加密貨幣、基金、指數\n"
        "• 自動訂閱管理\n"
        "• 頻率傳送功能\n"
        "• 多版本對比分析\n"
        "• 自動排行分析（產圖後自動顯示）\n\n"
        "請使用下方按鈕或直接輸入股票代碼開始使用！"
    )
    
    await update.message.reply_text(
        welcome_text,
        reply_markup=create_main_keyboard()
    )

async def handle_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理按鈕點擊"""
    user_id = update.effective_user.id
    user_message = update.message.text
    
    if user_message == "📈 訂閱":
        await handle_subscribe_button(update, context)
    elif user_message == "❌ 取消訂閱":
        await handle_unsubscribe_button(update, context)
    elif user_message == "📋 檢查訂閱":
        await handle_check_subscriptions_button(update, context)
    elif user_message == "🔄 頻率傳送清單":
        await handle_frequency_list_button(update, context)
    elif user_message == "⏰ 時間版本選擇":
        await handle_time_version_button(update, context)
    elif user_message == "❓ 幫助":
        await handle_help_button(update, context)
    elif user_message == "🔙 返回主選單":
        await handle_back_to_main(update, context)
    elif user_message in ["🌅 上午清單", "🌙 晚上清單", "🌞 全日清單"]:
        await handle_frequency_list_selection(update, context)
    elif user_message in ["✅ 新增到現有訂閱", "🔄 替換現有訂閱"]:
        await handle_list_action_selection(update, context)
    elif user_message in ["⏰ 30分鐘版本", "⏰ 1小時版本", "⏰ 4小時版本"]:
        await handle_time_version_selection(update, context)
    elif user_message in ["5分鐘", "10分鐘", "15分鐘", "30分鐘"]:
        # 檢查用戶狀態
        current_state = user_states.get(user_id, 'main')
        if current_state == 'waiting_interval':
            await handle_interval_selection(update, context)
        else:
            await update.message.reply_text(
                "❌ 請先選擇標的",
                reply_markup=create_main_keyboard()
            )
            user_states[user_id] = 'main'
    elif user_message == "⬅️ 上一頁":
        await handle_previous_page(update, context)
    elif user_message == "➡️ 下一頁":
        await handle_next_page(update, context)
    elif user_message.startswith("📄"):  # 頁面資訊按鈕，忽略
        return
    elif user_message == "✅ 加入訂閱":
        await handle_add_to_subscription(update, context)
    elif user_message == "❌ 不加入":
        await handle_decline_subscription(update, context)
    else:
        # 檢查是否為狀態相關的輸入
        current_state = user_states.get(user_id, 'main')
        if current_state == 'waiting_symbol':
            await handle_symbol_input(update, context)
        elif current_state == 'waiting_stock_selection':
            await handle_stock_selection(update, context)
        elif current_state == 'waiting_unsubscribe_selection':
            await handle_unsubscribe_selection(update, context)
        else:
            # 如果不是按鈕，當作一般訊息處理
            await handle_message(update, context)

async def handle_subscribe_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理訂閱按鈕"""
    user_id = update.effective_user.id
    user_states[user_id] = 'waiting_stock_selection'
    user_pages[user_id] = 0  # 重置到第一頁
    
    # 顯示標的選擇鍵盤
    keyboard = create_stock_selection_keyboard(page=0)
    
    await update.message.reply_text(
        "📈 請從以下清單中選擇要訂閱的標的：\n"
        "使用 ⬅️ 上一頁 和 ➡️ 下一頁 瀏覽更多選項",
        reply_markup=keyboard
    )

async def handle_unsubscribe_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理取消訂閱按鈕"""
    user_id = update.effective_user.id
    user_states[user_id] = 'waiting_unsubscribe_selection'
    
    # 先顯示用戶當前的訂閱
    user_subs = get_user_subscriptions(user_id)
    if not user_subs:
        await update.message.reply_text(
            "您目前沒有任何訂閱標的。",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 創建動態取消訂閱鍵盤
    unsubscribe_keyboard = create_unsubscribe_keyboard(user_subs)
    
    sub_list = []
    if isinstance(user_subs, dict):
        for symbol, info in user_subs.items():
            if isinstance(info, dict):
                interval = info.get('interval_minutes', '未知')
                name = info.get('symbol_name', symbol)
                sub_list.append(f"📈 {symbol} ({name}) - {interval}分鐘")
            else:
                logging.error(f"訂閱資訊格式錯誤，預期字典但得到 {type(info)}")
    else:
        logging.error(f"用戶訂閱資料格式錯誤，預期字典但得到 {type(user_subs)}")
    
    # 增加除錯資訊
    has_multiple_subs = len(user_subs) > 1
    message = f"請點選要取消訂閱的標的：\n\n" + "\n".join(sub_list)
    if has_multiple_subs:
        message += f"\n\n💡 您有 {len(user_subs)} 個訂閱，可以使用「🗑️ 一鍵取消所有訂閱」快速取消"
    
    await update.message.reply_text(
        message,
        reply_markup=unsubscribe_keyboard
    )

async def handle_check_subscriptions_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理檢查訂閱按鈕"""
    user_id = update.effective_user.id
    user_subs = get_user_subscriptions(user_id)
    
    if not user_subs:
        await update.message.reply_text(
            "您目前沒有任何訂閱標的。\n使用「📈 訂閱」按鈕來新增訂閱！",
            reply_markup=create_main_keyboard()
        )
        return
    
    # 格式化訂閱列表
    sub_list = []
    if isinstance(user_subs, dict):
        for symbol, info in user_subs.items():
            if isinstance(info, dict):
                interval = info.get('interval_minutes', '未知')
                name = info.get('symbol_name', symbol)
                added_time = info.get('added_time', '未知')
                # 轉換時間格式
                try:
                    dt_obj = datetime.fromisoformat(added_time)
                    added_str = dt_obj.strftime('%Y-%m-%d %H:%M')
                except:
                    added_str = added_time
                
                sub_list.append(f"📈 {symbol} ({name})\n⏰ 間隔：{interval}分鐘\n📅 新增：{added_str}")
            else:
                logging.error(f"訂閱資訊格式錯誤，預期字典但得到 {type(info)}")
    else:
        logging.error(f"用戶訂閱資料格式錯誤，預期字典但得到 {type(user_subs)}")
    
    message = f"您的訂閱標的（共 {len(user_subs) if isinstance(user_subs, dict) else 0} 個）：\n\n" + "\n\n".join(sub_list)
    
    await update.message.reply_text(
        message,
        reply_markup=create_main_keyboard()
    )

async def handle_help_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理幫助按鈕"""
    help_text = """
🤖 FlipAdam 通知機器人使用說明

📊 主要功能：
• 技術分析圖表生成
• 自動訂閱管理
• 頻率傳送功能
• 多時間版本分析
• 自動排行分析（產圖後自動顯示）

📈 訂閱功能：
• 支援 5、10、15、30 分鐘間隔
• 自動發送最新技術分析圖表
• 可同時管理多個標的

⏰ 時間版本功能：
• 30分鐘版本：高頻率短期分析
• 1小時版本：中頻率中期分析（含30分鐘對比）
• 4小時版本：低頻率長期分析（含30分鐘和1小時對比）

📊 排行分析功能：
• 自動排行：產圖後自動顯示訂閱標的排行
• 包含完整技術指標和交易建議
• 支援多時間版本對比分析

📋 支援標的：
• 股票：AAPL、TSM、台積電
• 加密貨幣：BTC、ETH、比特幣
• ETF、基金、指數

💡 使用方式：
1. 點擊「📈 訂閱」新增訂閱
2. 點擊「📋 檢查訂閱」查看狀態
3. 點擊「❌ 取消訂閱」移除訂閱
4. 點擊「⏰ 時間版本選擇」查看多版本分析（自動包含排行）
5. 直接輸入股票代碼查詢

🔄 頻率傳送：
輸入格式：股票代碼,分鐘數
例如：BTC,30 或 AAPL,60

📞 命令：
/start - 重新開始
/help - 顯示幫助
/stop - 停止頻率傳送
/status - 查看狀態
    """
    
    await update.message.reply_text(
        help_text,
        reply_markup=create_main_keyboard()
    )

async def handle_frequency_list_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理頻率傳送清單按鈕"""
    user_id = update.effective_user.id
    user_states[user_id] = 'waiting_frequency_list'
    
    await update.message.reply_text(
        "🔄 請選擇要載入的頻率傳送清單：\n\n"
        "🌅 上午清單：適合上午交易的標的\n"
        "🌙 晚上清單：適合晚上交易的標的\n"
        "🌞 全日清單：適合全日交易的標的",
        reply_markup=create_frequency_list_keyboard()
    )

async def handle_time_version_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理時間版本選擇按鈕"""
    user_id = update.effective_user.id
    user_states[user_id] = 'waiting_time_version'
    
    await update.message.reply_text(
        "⏰ 請選擇要查看的時間版本：\n\n"
        "⏰ 30分鐘版本：高頻率短期分析\n"
        "⏰ 1小時版本：中頻率中期分析\n"
        "⏰ 4小時版本：低頻率長期分析\n\n"
        "選擇後將為您的訂閱標的生成對應版本的圖表",
        reply_markup=create_time_version_keyboard()
    )



async def handle_time_version_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理時間版本選擇"""
    user_id = update.effective_user.id
    user_message = update.message.text
    
    # 檢查用戶是否有訂閱
    user_subs = get_user_subscriptions(user_id)
    if not user_subs:
        await update.message.reply_text(
            "❌ 您目前沒有任何訂閱標的。\n請先使用「📈 訂閱」功能新增訂閱標的。",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 根據選擇的版本決定要生成的圖表
    if user_message == "⏰ 30分鐘版本":
        versions = ["30分鐘"]
        version_text = "30分鐘版本"
    elif user_message == "⏰ 1小時版本":
        versions = ["30分鐘", "1小時"]  # 同時顯示30分鐘和1小時
        version_text = "1小時版本（含30分鐘對比）"
    elif user_message == "⏰ 4小時版本":
        versions = ["30分鐘", "4小時"]  # 只顯示30分鐘和4小時對比
        version_text = "4小時版本（含30分鐘對比）"
    else:
        await update.message.reply_text(
            "❌ 無效的選擇",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 顯示處理訊息
    await update.message.reply_text(
        f"⏰ 正在生成 {version_text} 圖表...\n"
        f"📊 將為 {len(user_subs)} 個訂閱標的生成圖表\n"
        f"⏳ 請稍候，這可能需要一些時間...",
        reply_markup=create_main_keyboard()
    )
    
    # 為每個訂閱標的生成圖表
    success_count = 0
    total_count = len(user_subs)
    
    for symbol in user_subs.keys():
        try:
            await send_multi_version_images(user_id, symbol, versions)
            success_count += 1
            # 添加延遲避免發送過快
            await asyncio.sleep(1)
        except Exception as e:
            logging.error(f"生成 {symbol} 多版本圖表時發生錯誤: {e}")
    
    # 發送圖片完成訊息
    await update.message.reply_text(
        f"✅ {version_text} 圖表生成完成！\n"
        f"📊 成功生成: {success_count}/{total_count} 個標的\n"
        f"📈 正在生成排行分析...",
        reply_markup=create_main_keyboard()
    )
    
    # 所有圖片都產生完成後，統一做排行分析
    try:
        await send_subscription_ranking(user_id, versions)
        await update.message.reply_text(
            f"📊 排行分析完成！\n"
            f"📈 已為您生成完整的排行報告",
            reply_markup=create_main_keyboard()
        )
    except Exception as e:
        logging.error(f"生成排行分析時發生錯誤: {e}")
        await update.message.reply_text(
            f"❌ 排行分析生成失敗: {str(e)}",
            reply_markup=create_main_keyboard()
        )
    
    user_states[user_id] = 'main'

async def handle_frequency_list_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理頻率傳送清單選擇"""
    user_id = update.effective_user.id
    user_message = update.message.text
    
    # 映射清單名稱到清單ID
    list_mapping = {
        "🌅 上午清單": "morning_list",
        "🌙 晚上清單": "evening_list",
        "🌞 全日清單": "full_day_list"
    }
    
    selected_list_id = list_mapping.get(user_message)
    if not selected_list_id or selected_list_id not in frequency_lists:
        await update.message.reply_text(
            "❌ 清單不存在，請重新選擇",
            reply_markup=create_frequency_list_keyboard()
        )
        return
    
    # 儲存用戶選擇的清單
    user_selected_list[user_id] = selected_list_id
    selected_list = frequency_lists[selected_list_id]
    
    # 檢查用戶現有訂閱
    user_subs = get_user_subscriptions(user_id)
    existing_symbols = set(user_subs.keys())
    list_symbols = {symbol['symbol'] for symbol in selected_list['symbols']}
    
    # 找出重複的標的
    overlapping_symbols = existing_symbols.intersection(list_symbols)
    
    if overlapping_symbols:
        # 有重複標的，詢問用戶要如何處理
        user_states[user_id] = 'waiting_list_action'
        
        overlap_text = "\n".join([f"• {symbol}" for symbol in overlapping_symbols])
        message = (
            f"📋 您選擇的清單：{selected_list['name']}\n"
            f"📝 描述：{selected_list['description']}\n"
            f"📊 包含 {len(selected_list['symbols'])} 個標的\n\n"
            f"⚠️ 發現以下標的已存在於您的訂閱中：\n{overlap_text}\n\n"
            f"請選擇處理方式："
        )
        
        await update.message.reply_text(
            message,
            reply_markup=create_list_action_keyboard()
        )
    else:
        # 沒有重複，直接新增
        await add_frequency_list_to_subscriptions(user_id, selected_list_id, "add", update)

async def handle_list_action_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理清單動作選擇"""
    user_id = update.effective_user.id
    user_message = update.message.text
    
    if user_message == "✅ 新增到現有訂閱":
        action = "add"
    elif user_message == "🔄 替換現有訂閱":
        action = "replace"
    else:
        await update.message.reply_text(
            "❌ 無效的選擇",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 取得用戶選擇的清單
    selected_list_id = user_selected_list.get(user_id)
    if not selected_list_id:
        await update.message.reply_text(
            "❌ 未找到選擇的清單，請重新開始",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 執行清單載入
    await add_frequency_list_to_subscriptions(user_id, selected_list_id, action, update)

async def add_frequency_list_to_subscriptions(user_id: int, list_id: str, action: str, update: Update = None):
    """將頻率傳送清單加入到用戶訂閱"""
    selected_list = frequency_lists[list_id]
    user_subs = get_user_subscriptions(user_id)
    
    if action == "replace":
        # 替換現有訂閱
        subscriptions[user_id] = {}
        added_count = 0
        for symbol_info in selected_list['symbols']:
            symbol = symbol_info['symbol']
            name = symbol_info['name']
            interval = symbol_info['interval_minutes']
            
            subscriptions[user_id][symbol] = {
                'interval_minutes': interval,
                'symbol_name': name,
                'added_time': datetime.now().isoformat(),
                'source_list': list_id,  # 記錄來源清單
                'list_name': selected_list['name']  # 記錄清單名稱
            }
            added_count += 1
        
        save_subscriptions()
        
        # 重新啟動所有訂閱任務
        for symbol, info in subscriptions[user_id].items():
            start_subscription_task(user_id, symbol, info['interval_minutes'])
        
        if update:
            await update.message.reply_text(
                f"✅ 已替換所有訂閱為 {selected_list['name']}\n"
                f"📊 新增 {added_count} 個標的\n"
                f"⏰ 間隔：{interval} 分鐘",
                reply_markup=create_main_keyboard()
            )
    else:
        # 新增到現有訂閱
        added_count = 0
        for symbol_info in selected_list['symbols']:
            symbol = symbol_info['symbol']
            name = symbol_info['name']
            interval = symbol_info['interval_minutes']
            
            # 檢查是否已存在
            if symbol not in subscriptions[user_id]:
                subscriptions[user_id][symbol] = {
                    'interval_minutes': interval,
                    'symbol_name': name,
                    'added_time': datetime.now().isoformat(),
                    'source_list': list_id,  # 記錄來源清單
                    'list_name': selected_list['name']  # 記錄清單名稱
                }
                added_count += 1
                # 啟動新訂閱任務
                start_subscription_task(user_id, symbol, interval)
        
        save_subscriptions()
        
        if update:
            await update.message.reply_text(
                f"✅ 已新增 {selected_list['name']} 到您的訂閱\n"
                f"📊 新增 {added_count} 個標的\n"
                f"⏰ 間隔：{interval} 分鐘",
                reply_markup=create_main_keyboard()
            )
    
    # 清理用戶狀態
    user_states[user_id] = 'main'
    if user_id in user_selected_list:
        del user_selected_list[user_id]

async def handle_back_to_main(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理返回主選單"""
    user_id = update.effective_user.id
    user_states[user_id] = 'main'
    
    # 清理用戶選擇的清單
    if user_id in user_selected_list:
        del user_selected_list[user_id]
    
    await update.message.reply_text(
        "已返回主選單，請選擇功能：",
        reply_markup=create_main_keyboard()
    )

async def handle_previous_page(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理上一頁"""
    user_id = update.effective_user.id
    current_page = user_pages.get(user_id, 0)
    
    if current_page > 0:
        user_pages[user_id] = current_page - 1
        keyboard = create_stock_selection_keyboard(page=current_page - 1)
        
        await update.message.reply_text(
            f"📄 第 {current_page} 頁",
            reply_markup=keyboard
        )

async def handle_next_page(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理下一頁"""
    user_id = update.effective_user.id
    current_page = user_pages.get(user_id, 0)
    
    stock_list = get_stock_list_from_csv()
    total_pages = (len(stock_list) + 7) // 8  # 每頁8個項目
    
    if current_page < total_pages - 1:
        user_pages[user_id] = current_page + 1
        keyboard = create_stock_selection_keyboard(page=current_page + 1)
        
        await update.message.reply_text(
            f"📄 第 {current_page + 2} 頁",
            reply_markup=keyboard
        )

async def handle_stock_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理標的選擇"""
    user_id = update.effective_user.id
    user_message = update.message.text
    
    # 解析按鈕文字，格式為 "SYMBOL\nNAME"
    lines = user_message.split('\n')
    if len(lines) >= 2:
        symbol = lines[0].strip()
        name = lines[1].strip()
        
        # 儲存選擇的標的
        context.user_data['selected_symbol'] = symbol
        context.user_data['selected_symbol_name'] = name
        
        await update.message.reply_text(
            f"已選擇：{symbol} ({name})\n\n請選擇訂閱間隔：",
            reply_markup=create_interval_keyboard()
        )
        
        # 更新狀態
        user_states[user_id] = 'waiting_interval'
    else:
        await update.message.reply_text(
            "❌ 選擇無效，請重新選擇標的",
            reply_markup=create_stock_selection_keyboard(page=user_pages.get(user_id, 0))
        )

async def handle_symbol_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理標的代碼輸入"""
    user_id = update.effective_user.id
    symbol_input = update.message.text.strip().upper()
    
    # 查表確認標的存在
    matches, _ = find_matching_stocks(symbol_input)
    if not matches:
        await update.message.reply_text(
            f"❌ 查無此標的：{symbol_input}\n\n已嘗試：\n• 本地清單搜尋\n• yfinance 線上搜尋\n\n請確認輸入或嘗試其他代碼",
            reply_markup=create_cancel_keyboard()
        )
        return
    
    yfinance_symbol, symbol_name = matches[0]
    
    # 儲存選擇的標的
    context.user_data['selected_symbol'] = yfinance_symbol
    context.user_data['selected_symbol_name'] = symbol_name
    
    await update.message.reply_text(
        f"已選擇：{yfinance_symbol} ({symbol_name})\n\n請選擇訂閱間隔：",
        reply_markup=create_interval_keyboard()
    )

async def handle_interval_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理間隔選擇"""
    user_id = update.effective_user.id
    interval_text = update.message.text
    
    # 檢查用戶狀態
    current_state = user_states.get(user_id, 'main')
    if current_state != 'waiting_interval':
        await update.message.reply_text(
            "❌ 請先選擇標的",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 解析間隔時間
    interval_map = {
        "5分鐘": 5,
        "10分鐘": 10,
        "15分鐘": 15,
        "30分鐘": 30
    }
    
    interval_minutes = interval_map.get(interval_text, 30)
    symbol = context.user_data.get('selected_symbol')
    symbol_name = context.user_data.get('selected_symbol_name')
    
    if not symbol:
        await update.message.reply_text(
            "❌ 發生錯誤，請重新開始訂閱流程",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 新增訂閱
    success, message = add_subscription(user_id, symbol, interval_minutes, symbol_name)
    
    if success:
        # 啟動訂閱任務
        start_subscription_task(user_id, symbol, interval_minutes)
    
    await update.message.reply_text(
        message,
        reply_markup=create_main_keyboard()
    )
    
    # 清理用戶數據
    context.user_data.clear()
    user_states[user_id] = 'main'

async def handle_unsubscribe_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理取消訂閱的選擇"""
    user_id = update.effective_user.id
    selection = update.message.text
    
    # 檢查是否為返回按鈕
    if selection == "🔙 返回主選單":
        await handle_back_to_main(update, context)
        return
    
    # 檢查是否為一鍵取消所有訂閱
    if selection == "🗑️ 一鍵取消所有訂閱":
        # 執行一鍵取消所有訂閱
        success, message = remove_all_subscriptions(user_id)
        
        await update.message.reply_text(
            message,
            reply_markup=create_main_keyboard()
        )
        
        user_states[user_id] = 'main'
        return
    
    # 解析選擇的標的（格式：❌ SYMBOL (間隔分)）
    if selection.startswith("❌ "):
        # 提取標的代碼
        symbol_part = selection[3:]  # 移除 "❌ "
        symbol = symbol_part.split(" (")[0]  # 提取標的代碼部分
        
        # 執行取消訂閱
        success, message = remove_subscription(user_id, symbol)
        
        await update.message.reply_text(
            message,
            reply_markup=create_main_keyboard()
        )
        
        user_states[user_id] = 'main'
    else:
        await update.message.reply_text(
            "❌ 無效的選擇，請重新選擇",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /help 命令"""
    help_text = """
可用命令：
/start - 開始使用機器人
/help - 顯示此幫助訊息
/notify <訊息> - 發送通知訊息
/stop - 停止頻率傳送
/status - 查看頻率傳送狀態

📈 訂閱管理：
/subscribe <標的代碼> [間隔分鐘] - 新增訂閱標的
/unsubscribe <標的代碼> - 刪除訂閱標的
/subscriptions - 查看所有訂閱標的

📊 單次查詢：
直接輸入股票代碼或名稱，我會幫您查詢對應的股票資訊並發送10天30分鐘高解析度技術分析圖表。
支援模糊搜尋，例如：
- 輸入 "台積電" 或 "tsm" 都可以找到台積電
- 輸入 "科技" 會列出所有包含科技的公司
- 如果本地清單中找不到，會自動使用 yfinance 線上搜尋

🔄 頻率傳送功能：
輸入格式：股票代碼,分鐘數
例如：
- "TSM,30" - 每30分鐘傳送一次台積電的技術分析圖表
- "BTC-USD,60" - 每60分鐘傳送一次比特幣的技術分析圖表
- "AAPL,120" - 每2小時傳送一次蘋果的技術分析圖表

📋 手動訂閱功能：
輸入格式：股票代碼,分鐘數,s
例如：
- "TSM,30,s" - 訂閱台積電，每30分鐘自動發送技術分析圖表
- "BTC-USD,60,s" - 訂閱比特幣，每60分鐘自動發送技術分析圖表
- "AAPL,120,s" - 訂閱蘋果，每2小時自動發送技術分析圖表

📋 訂閱間隔選項：
- 5分鐘 (5)
- 10分鐘 (10)
- 15分鐘 (15)
- 30分鐘 (30) - 預設

注意：
- 間隔時間範圍：1-1440分鐘（最大24小時）
- 每個用戶同時只能運行一個頻率傳送任務
- 訂閱功能可以同時管理多個標的
- 使用 /stop 停止頻率傳送
- 使用 /status 查看當前狀態
- 圖片為高解析度格式，適合詳細查看和列印
    """
    await update.message.reply_text(help_text)

async def notify(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /notify 命令"""
    if not context.args:
        await update.message.reply_text('請提供要發送的通知訊息。\n'
                                      '例如：/notify 這是一則測試通知')
        return
    
    message = ' '.join(context.args)
    await update.message.reply_text(f'已發送通知：{message}')

async def compress_image_to_jpeg(image_path, max_size_kb=100):
    """壓縮圖片到指定 KB 以內，回傳 BytesIO（目前不使用，改為發送原始高解析度圖片）"""
    with Image.open(image_path) as img:
        if img.mode in ("RGBA", "LA"):
            background = Image.new("RGB", img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1])
            img = background
        quality = 95
        output = io.BytesIO()
        while quality > 10:
            output.seek(0)
            output.truncate()
            img.save(output, format="JPEG", quality=quality)
            size_kb = output.tell() / 1024
            if size_kb <= max_size_kb:
                break
            quality -= 5
        output.seek(0)
        return output

async def send_photo_with_aiohttp(token, chat_id, photo_path, caption):
    url = f"https://api.telegram.org/bot{token}/sendPhoto"
    try:
        # 檢查檔案是否存在
        if not os.path.exists(photo_path):
            logging.error(f"圖片檔案不存在: {photo_path}")
            return {"ok": False, "error_code": 404, "description": "Image file not found"}
        
        # 檢查檔案大小
        file_size = os.path.getsize(photo_path)
        logging.info(f"圖片檔案大小: {file_size} bytes")
        
        # 不壓縮圖片，直接發送原始高解析度圖片
        with open(photo_path, 'rb') as f:
            photo_data = f.read()
        
        data = aiohttp.FormData()
        data.add_field("chat_id", str(chat_id))
        data.add_field("caption", caption)
        data.add_field("photo", photo_data, filename="chart.png", content_type="image/png")
        
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
            async with session.post(url, data=data) as resp:
                result = await resp.json()
                logging.info(f"Telegram API 回應: {result}")
                return result
    except Exception as e:
        logging.error(f"發送圖片時發生錯誤: {e}", exc_info=True)
        return {"ok": False, "error_code": 500, "description": str(e)}

async def send_photo_with_retry(update, photo_path, caption, max_retries=3):
    for attempt in range(max_retries):
        try:
            logging.info(f"準備發送原始高解析度圖片: {photo_path}")
            result = await send_photo_with_aiohttp(
                TOKEN,
                update.effective_chat.id,
                photo_path,
                caption
            )
            logging.info(f"發送原始高解析度圖片結果: {result}")
            if result.get("ok"):
                # 成功發送後刪除圖片檔案
                try:
                    os.remove(photo_path)
                    logging.info(f"成功刪除圖片檔案: {photo_path}")
                except Exception as e:
                    logging.warning(f"刪除圖片檔案失敗: {photo_path}, 錯誤: {e}")
                return True
            else:
                await update.message.reply_text(f"發送原始高解析度圖片失敗: {result}")
        except Exception as e:
            logging.error(f"發送原始高解析度圖片時發生錯誤: {e}", exc_info=True)
            await update.message.reply_text(f"發送原始高解析度圖片時發生錯誤: {e}")
            raise
    return False

def cleanup_old_images():
    """清理 figure 目錄中的舊圖片檔案"""
    try:
        if os.path.exists('figure'):
            current_time = time.time()
            for filename in os.listdir('figure'):
                if filename.endswith('.png'):
                    file_path = os.path.join('figure', filename)
                    # 刪除超過1小時的圖片檔案
                    if current_time - os.path.getmtime(file_path) > 3600:  # 1小時 = 3600秒
                        try:
                            os.remove(file_path)
                            logging.info(f"清理舊圖片檔案: {filename}")
                        except Exception as e:
                            logging.warning(f"清理圖片檔案失敗: {filename}, 錯誤: {e}")
    except Exception as e:
        logging.error(f"清理圖片檔案時發生錯誤: {e}")

async def start_frequency_task(update: Update, symbol_input: str, interval_minutes: int):
    """啟動頻率傳送任務（強制經過CSV查表）"""
    global task_counter
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name

    # 強制查表
    matches, _ = find_matching_stocks(symbol_input)
    if not matches:
        await update.message.reply_text(f"❌ 查無此標的：{symbol_input}，請確認輸入")
        return
    
    # 使用查到的 yfinance 代碼
    yfinance_symbol, symbol_name = matches[0]
    logging.info(f"查表結果：{symbol_input} -> {yfinance_symbol} ({symbol_name})")

    # 停止現有的任務（如果有的話）- 靜默停止，不顯示訊息
    if user_id in frequency_tasks:
        frequency_tasks[user_id]['is_running'] = False
        del frequency_tasks[user_id]
        logging.info(f"用戶 {user_name} 停止舊的頻率傳送任務")

    # 創建新任務
    task_counter += 1
    task_id = f"task_{task_counter}"

    frequency_tasks[user_id] = {
        'task_id': task_id,
        'symbol': yfinance_symbol,  # 使用查到的 yfinance 代碼
        'interval_minutes': interval_minutes,
        'start_time': datetime.now(),
        'is_running': True
    }

    logging.info(f"用戶 {user_name} 啟動頻率傳送任務: {yfinance_symbol} 每 {interval_minutes} 分鐘")

    # 立即發送第一次圖片
    await send_frequency_image(update, yfinance_symbol)

    # 啟動定時任務
    asyncio.create_task(frequency_task_loop(update, task_id, yfinance_symbol, interval_minutes))

    await update.message.reply_text(
        f"✅ 已啟動頻率傳送任務\n"
        f"📈 股票代碼: {yfinance_symbol} ({symbol_name})\n"
        f"⏰ 傳送間隔: 每 {interval_minutes} 分鐘\n"
        f"🔄 下次傳送: {interval_minutes} 分鐘後\n\n"
        f"使用 /stop 停止頻率傳送"
    )

async def stop_frequency_task(update: Update):
    """停止頻率傳送任務"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name
    
    if user_id in frequency_tasks:
        frequency_tasks[user_id]['is_running'] = False
        del frequency_tasks[user_id]
        logging.info(f"用戶 {user_name} 停止頻率傳送任務")
        await update.message.reply_text("✅ 已停止頻率傳送任務")
    else:
        await update.message.reply_text("❌ 沒有正在運行的頻率傳送任務")

async def frequency_task_loop(update: Update, task_id: str, symbol: str, interval_minutes: int):
    """頻率傳送任務循環"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name
    
    while True:
        try:
            # 等待指定的間隔時間
            await asyncio.sleep(interval_minutes * 60)
            
            # 檢查任務是否還在運行
            if user_id not in frequency_tasks or not frequency_tasks[user_id]['is_running']:
                logging.info(f"用戶 {user_name} 的頻率傳送任務已停止")
                break
            
            # 發送圖片
            await send_frequency_image(update, symbol)
            
            logging.info(f"用戶 {user_name} 的頻率傳送任務: 已發送 {symbol} 圖片")
            
        except Exception as e:
            logging.error(f"頻率傳送任務發生錯誤: {e}")
            break

@monitor_memory
async def send_frequency_image(update: Update, yfinance_symbol: str):
    """發送頻率傳送圖片（使用查到的 yfinance 代碼）"""
    try:
        # 確保 figure 目錄存在
        if not os.path.exists('figure'):
            os.makedirs('figure')
        
        # 生成新的圖片（使用 yfinance 代碼）
        session = get_cffi_session()
        image_path, analysis_data = getMLFlipAdambySymbolWithData(yfinance_symbol, session=session)
        
        # 儲存 analysis_data
        if analysis_data:
            save_analysis_data(yfinance_symbol, analysis_data, "30min")
        
        if image_path and os.path.exists(image_path):
            # 發送圖片
            if await send_photo_with_retry(update, image_path, f"{yfinance_symbol} 的技術分析圖表 (自動更新)"):
                logging.info(f"頻率傳送: 成功發送 {yfinance_symbol} 圖片")
            else:
                await update.message.reply_text(f"❌ 頻率傳送: 發送 {yfinance_symbol} 圖片失敗")
        else:
            await update.message.reply_text(f"❌ 頻率傳送: 無法生成 {yfinance_symbol} 圖片")
            
    except Exception as e:
        logging.error(f"頻率傳送發送圖片時發生錯誤: {e}")
        await update.message.reply_text(f"❌ 頻率傳送發生錯誤: {str(e)}")
    finally:
        # 記憶體清理
        try:
            variables_to_clean = {}
            for var_name in ['session', 'image_path']:
                if var_name in locals():
                    variables_to_clean[var_name] = locals()[var_name]
            
            cleanup_variables(**variables_to_clean)
            log_memory_usage(f"頻率傳送圖片發送完成 - {yfinance_symbol}")
        except Exception as cleanup_error:
            logging.error(f"頻率傳送圖片發送記憶體清理時發生錯誤: {cleanup_error}")

@monitor_memory
async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理一般訊息（強制經過CSV查表）"""
    try:
        # 清理舊圖片檔案
        cleanup_old_images()
        
        user_message = update.message.text.strip()
        user = update.effective_user
        user_name = user.first_name
        logging.info(f"收到來自 {user_name} 的訊息: {user_message}")

        # 檢查是否為頻率傳送指令 (格式: 股票代碼,分鐘數) 或訂閱指令 (格式: 股票代碼,分鐘數,s)
        if ',' in user_message:
            parts = user_message.split(',')
            if len(parts) == 2:
                # 格式: 股票代碼,分鐘數 (頻率傳送)
                symbol_input = parts[0].strip().upper()
                try:
                    interval_minutes = int(parts[1].strip())
                    if interval_minutes > 0 and interval_minutes <= 1440:  # 最大24小時
                        # 強制查表
                        matches, found_via_yfinance = find_matching_stocks(symbol_input)
                        if matches:
                            yfinance_symbol, symbol_name = matches[0]
                            await update.message.reply_text(f"正在啟動頻率傳送任務...\n股票: {yfinance_symbol} ({symbol_name})")
                            await start_frequency_task(update, symbol_input, interval_minutes)
                            
                            # 詢問是否要加入訂閱清單
                            await ask_add_to_subscription(update, context, yfinance_symbol, symbol_name)
                            return
                        else:
                            await update.message.reply_text(f"❌ 查無此標的：{symbol_input}，請確認輸入")
                            return
                    else:
                        await update.message.reply_text("❌ 間隔時間必須在 1-1440 分鐘之間")
                        return
                except ValueError:
                    await update.message.reply_text("❌ 間隔時間必須是數字")
                    return
            elif len(parts) == 3 and parts[2].strip().lower() == 's':
                # 格式: 股票代碼,分鐘數,s (直接訂閱)
                symbol_input = parts[0].strip().upper()
                try:
                    interval_minutes = int(parts[1].strip())
                    if interval_minutes > 0 and interval_minutes <= 1440:  # 最大24小時
                        # 強制查表
                        matches, found_via_yfinance = find_matching_stocks(symbol_input)
                        if matches:
                            yfinance_symbol, symbol_name = matches[0]
                            user_id = update.effective_user.id
                            
                            # 檢查是否已經訂閱
                            existing_subs = get_user_subscriptions(user_id)
                            if any(sub['symbol'] == yfinance_symbol for sub in existing_subs):
                                await update.message.reply_text(f"❌ 您已經訂閱了 {yfinance_symbol} ({symbol_name})")
                                return
                            
                            # 添加訂閱
                            add_subscription(user_id, yfinance_symbol, interval_minutes, symbol_name)
                            start_subscription_task(user_id, yfinance_symbol, interval_minutes)
                            
                            await update.message.reply_text(
                                f"✅ 成功訂閱！\n"
                                f"股票: {yfinance_symbol} ({symbol_name})\n"
                                f"間隔: {interval_minutes} 分鐘\n"
                                f"將自動發送技術分析圖表"
                            )
                            return
                        else:
                            await update.message.reply_text(f"❌ 查無此標的：{symbol_input}，請確認輸入")
                            return
                    else:
                        await update.message.reply_text("❌ 間隔時間必須在 1-1440 分鐘之間")
                        return
                except ValueError:
                    await update.message.reply_text("❌ 間隔時間必須是數字")
                    return

        # 搜尋匹配的股票（強制查表）
        matches, found_via_yfinance = find_matching_stocks(user_message)
        
        if matches:
            for yfinance_symbol, name in matches[:10]:  # 限制最多10個
                response = f"您輸入的是股票代碼：{yfinance_symbol}\n對應的股票名稱是：{name}"
                await update.message.reply_text(response)
                
                # 確保 figure 目錄存在
                if not os.path.exists('figure'):
                    os.makedirs('figure')
                    logging.info("已創建 figure 目錄")
                
                # 重新產生圖片（使用 yfinance 代碼）
                session = get_cffi_session()
                image_path, analysis_data = getMLFlipAdambySymbolWithData(yfinance_symbol, session=session)
                
                # 儲存 analysis_data
                if analysis_data:
                    save_analysis_data(yfinance_symbol, analysis_data, "30min")
                
                if image_path and os.path.exists(image_path):
                    if await send_photo_with_retry(update, image_path, f"{yfinance_symbol} 的技術分析圖表"):
                        logging.info(f"成功發送 {yfinance_symbol} 的技術分析圖表")
                        
                        # 如果是透過 yfinance 找到的新標的，詢問是否加入訂閱
                        if found_via_yfinance:
                            await ask_add_to_subscription(update, context, yfinance_symbol, name)
                    else:
                        await update.message.reply_text("發送圖片失敗，請稍後再試。")
                else:
                    await update.message.reply_text(f"❌ 無法生成 {yfinance_symbol} 的技術分析圖表")
            return
        else:
            await update.message.reply_text(f"❌ 查無此標的：{user_message}，請確認輸入")
    except Exception as e:
        logging.error(f"處理訊息時發生錯誤: {e}", exc_info=True)
        await update.message.reply_text(f"❌ 發生錯誤: {str(e)}")
    finally:
        # 記憶體清理
        try:
            variables_to_clean = {}
            for var_name in ['user_message', 'user', 'user_name', 'matches', 'session', 'image_path', 'yfinance_symbol', 'name', 'response', 'found_via_yfinance']:
                if var_name in locals():
                    variables_to_clean[var_name] = locals()[var_name]
            
            cleanup_variables(**variables_to_clean)
            log_memory_usage(f"訊息處理完成 - {user_message if 'user_message' in locals() else 'unknown'}")
        except Exception as cleanup_error:
            logging.error(f"訊息處理記憶體清理時發生錯誤: {cleanup_error}")

async def stop_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /stop 命令 - 停止頻率傳送"""
    await stop_frequency_task(update)

async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /status 命令 - 查看訂閱狀態"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name
    
    # 檢查訂閱任務
    user_subs = get_user_subscriptions(user_id)
    active_tasks = subscription_tasks.get(user_id, {})
    
    if user_subs and active_tasks:
        # 分析頻率傳送清單狀態
        frequency_lists_status = {}
        individual_subs = []
        
        for symbol, info in user_subs.items():
            source_list = info.get('source_list')
            list_name = info.get('list_name')
            
            if source_list and list_name:
                # 來自頻率傳送清單
                if source_list not in frequency_lists_status:
                    frequency_lists_status[source_list] = {
                        'name': list_name,
                        'symbols': [],
                        'interval': info['interval_minutes']
                    }
                frequency_lists_status[source_list]['symbols'].append({
                    'symbol': symbol,
                    'name': info.get('symbol_name', symbol)
                })
            else:
                # 個別訂閱
                individual_subs.append((symbol, info))
        
        status_text = f"📊 訂閱狀態\n"
        status_text += f"👤 用戶: {user_name}\n"
        status_text += f"📈 訂閱標的: {len(user_subs)} 個\n"
        status_text += f"🔄 運行任務: {len(active_tasks)} 個\n\n"
        
        # 顯示頻率傳送清單狀態
        if frequency_lists_status:
            status_text += "🔄 頻率傳送清單:\n"
            for list_id, list_info in frequency_lists_status.items():
                status_text += f"📋 {list_info['name']}\n"
                status_text += f"⏰ 間隔: {list_info['interval']} 分鐘\n"
                status_text += f"📈 標的: {len(list_info['symbols'])} 個\n"
                for symbol_info in list_info['symbols']:
                    status_text += f"  • {symbol_info['symbol']} ({symbol_info['name']})\n"
                status_text += "\n"
        
        # 顯示個別訂閱
        if individual_subs:
            status_text += "📈 個別訂閱:\n"
            for symbol, info in individual_subs:
                interval = info['interval_minutes']
                name = info.get('symbol_name', symbol)
                added_time = info.get('added_time', '未知')
                
                # 轉換時間格式
                try:
                    dt_obj = datetime.fromisoformat(added_time)
                    added_str = dt_obj.strftime('%Y-%m-%d %H:%M')
                except:
                    added_str = added_time
                
                status_text += f"📈 {symbol} ({name})\n"
                status_text += f"⏰ 間隔: {interval} 分鐘\n"
                status_text += f"📅 新增: {added_str}\n"
                status_text += f"🔄 狀態: {'運行中' if symbol in active_tasks else '已停止'}\n\n"
        
        await update.message.reply_text(status_text)
    elif user_id in frequency_tasks:
        # 檢查舊的頻率傳送任務
        task = frequency_tasks[user_id]
        start_time = task['start_time']
        elapsed_time = datetime.now() - start_time
        
        status_text = (
            f"📊 頻率傳送狀態\n"
            f"📈 股票代碼: {task['symbol']}\n"
            f"⏰ 傳送間隔: 每 {task['interval_minutes']} 分鐘\n"
            f"🕐 開始時間: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"⏱️ 運行時間: {str(elapsed_time).split('.')[0]}\n"
            f"🔄 狀態: 運行中"
        )
        await update.message.reply_text(status_text)
    else:
        await update.message.reply_text("❌ 沒有正在運行的訂閱任務")

async def subscribe_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /subscribe 命令"""
    if not update or not update.message:
        logging.error("subscribe_command: update 或 update.message 為 None")
        return
    
    if not context.args:
        await update.message.reply_text(
            "請提供要訂閱的標的代碼。\n"
            "格式：/subscribe <標的代碼> [間隔分鐘]\n"
            "例如：/subscribe BTC 30\n"
            "間隔選項：5, 10, 15, 30分鐘（預設30分鐘）"
        )
        return
    
    symbol = context.args[0].strip().upper()
    interval_minutes = 30  # 預設30分鐘
    
    if len(context.args) > 1:
        try:
            interval_minutes = int(context.args[1])
            if interval_minutes not in [5, 10, 15, 30]:
                await update.message.reply_text("❌ 間隔時間必須是 5, 10, 15, 或 30 分鐘")
                return
        except ValueError:
            await update.message.reply_text("❌ 間隔時間必須是數字")
            return
    
    user_id = update.effective_user.id
    success, message = add_subscription(user_id, symbol, interval_minutes)
    await update.message.reply_text(message)

async def unsubscribe_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /unsubscribe 命令"""
    if not update or not update.message:
        logging.error("unsubscribe_command: update 或 update.message 為 None")
        return
    
    if not context.args:
        await update.message.reply_text(
            "請提供要取消訂閱的標的代碼。\n"
            "格式：/unsubscribe <標的代碼>\n"
            "例如：/unsubscribe BTC"
        )
        return
    
    symbol = context.args[0].strip().upper()
    user_id = update.effective_user.id
    success, message = remove_subscription(user_id, symbol)
    await update.message.reply_text(message)

async def subscriptions_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /subscriptions 命令"""
    if not update or not update.message:
        logging.error("subscriptions_command: update 或 update.message 為 None")
        return
    
    user_id = update.effective_user.id
    user_subs = get_user_subscriptions(user_id)
    
    if not user_subs:
        await update.message.reply_text("您目前沒有任何訂閱標的。\n使用 /subscribe <標的代碼> 來新增訂閱")
        return
    
    # 格式化訂閱列表
    sub_list = []
    for symbol, info in user_subs.items():
        interval = info['interval_minutes']
        name = info.get('symbol_name', symbol)
        added_time = info.get('added_time', '未知')
        # 轉換時間格式
        try:
            dt_obj = datetime.fromisoformat(added_time)
            added_str = dt_obj.strftime('%Y-%m-%d %H:%M')
        except:
            added_str = added_time
        
        sub_list.append(f"📈 {symbol} ({name})\n⏰ 間隔：{interval}分鐘\n📅 新增：{added_str}")
    
    message = f"您的訂閱標的（共 {len(user_subs)} 個）：\n\n" + "\n\n".join(sub_list)
    message += "\n\n使用 /unsubscribe <標的代碼> 來取消訂閱"
    
    await update.message.reply_text(message)

def get_stock_list_from_csv():
    """從stock_name_hold_stockonly_SortbyValue.csv取得標的清單"""
    try:
        df = pd.read_csv('stock_name_hold_stockonly_SortbyValue.csv')
        stock_list = []
        for _, row in df.iterrows():
            stock_list.append({
                'symbol': row['Symbol'],
                'name': row['Name']
            })
        return stock_list
    except Exception as e:
        logging.error(f"讀取stock_name_hold_stockonly_SortbyValue.csv失敗: {e}")
        return []

def create_stock_selection_keyboard(page=0, items_per_page=8):
    """創建標的選擇鍵盤，每頁8個選項"""
    stock_list = get_stock_list_from_csv()
    total_items = len(stock_list)
    total_pages = (total_items + items_per_page - 1) // items_per_page
    
    keyboard = []
    
    # 計算當前頁面的項目
    start_idx = page * items_per_page
    end_idx = min(start_idx + items_per_page, total_items)
    
    # 添加標的按鈕（每行2個）
    for i in range(start_idx, end_idx, 2):
        row = []
        stock1 = stock_list[i]
        button1_text = f"{stock1['symbol']}\n{stock1['name']}"
        row.append(KeyboardButton(button1_text))
        
        # 如果有第二個項目
        if i + 1 < end_idx:
            stock2 = stock_list[i + 1]
            button2_text = f"{stock2['symbol']}\n{stock2['name']}"
            row.append(KeyboardButton(button2_text))
        
        keyboard.append(row)
    
    # 添加導航按鈕
    nav_row = []
    if page > 0:
        nav_row.append(KeyboardButton("⬅️ 上一頁"))
    if page < total_pages - 1:
        nav_row.append(KeyboardButton("➡️ 下一頁"))
    
    if nav_row:
        keyboard.append(nav_row)
    
    # 添加頁面資訊和返回按鈕
    page_info = f"📄 {page + 1}/{total_pages}"
    keyboard.append([KeyboardButton(page_info)])
    keyboard.append([KeyboardButton("🔙 返回主選單")])
    
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

@monitor_memory
async def send_subscription_ranking(user_id: int, versions: list):
    """發送訂閱標的的排行"""
    try:
        # 獲取用戶的訂閱標的
        user_subs = get_user_subscriptions(user_id)
        if not user_subs:
            return
        
        # 為每個版本生成排行
        for version in versions:
            try:
                # 根據版本選擇對應的函數
                if version == "30分鐘":
                    analysis_func = getMLFlipAdambySymbolWithData
                    time_version = "30分鐘"
                elif version == "1小時":
                    analysis_func = getMLFlipAdambySymbolWithData1HR
                    time_version = "1小時"
                elif version == "4小時":
                    analysis_func = getMLFlipAdambySymbolWithData4HR
                    time_version = "4小時"
                else:
                    continue
                
                # 分析所有訂閱標的
                analysis_results = []
                session = get_cffi_session()
                
                for symbol, subscription_info in user_subs.items():
                    try:
                        # 生成圖片和分析數據
                        image_path, analysis_data = analysis_func(symbol, session=session)
                        
                        if analysis_data:
                            # 添加symbol到分析數據中
                            analysis_data['symbol'] = symbol
                            analysis_results.append(analysis_data)
                            
                            # 儲存 analysis_data
                            save_analysis_data(symbol, analysis_data, time_version.lower().replace('分鐘', 'min').replace('小時', 'hr'))
                        
                        # 清理圖片檔案
                        if image_path and os.path.exists(image_path):
                            try:
                                os.remove(image_path)
                            except:
                                pass
                                
                    except Exception as e:
                        logging.error(f"分析訂閱標的 {symbol} 時發生錯誤: {e}")
                        continue
                
                # 生成排行報告
                if analysis_results:
                    ranking_message = format_ranking_message(analysis_results, time_version)
                    ranking_message += f"\n📊 訂閱標的分析統計:\n"
                    ranking_message += f"✅ 成功分析: {len(analysis_results)} 個標的\n"
                    ranking_message += f"❌ 分析失敗: {len(user_subs) - len(analysis_results)} 個標的\n"
                    ranking_message += f"📈 成功率: {len(analysis_results)/len(user_subs)*100:.1f}%\n"
                    
                    # 發送排行報告
                    if len(ranking_message) > 4000:
                        # 分段發送
                        segments = [ranking_message[i:i+4000] for i in range(0, len(ranking_message), 4000)]
                        for i, segment in enumerate(segments):
                            if i == 0:
                                await send_message_to_user(user_id, segment)
                            else:
                                await send_message_to_user(user_id, f"(續 {i+1}/{len(segments)})\n{segment}")
                    else:
                        await send_message_to_user(user_id, ranking_message)
                        
                else:
                    await send_message_to_user(user_id, f"❌ 無法生成 {time_version} 排行報告，沒有有效的分析數據")
                    
            except Exception as e:
                logging.error(f"生成 {version} 排行時發生錯誤: {e}")
                await send_message_to_user(user_id, f"❌ 生成 {version} 排行時發生錯誤: {str(e)}")
                
    except Exception as e:
        logging.error(f"發送訂閱排行時發生錯誤: {e}")
    finally:
        # 記憶體清理
        try:
            variables_to_clean = {}
            for var_name in ['session', 'analysis_results', 'image_path', 'analysis_data']:
                if var_name in locals():
                    variables_to_clean[var_name] = locals()[var_name]
            
            cleanup_variables(**variables_to_clean)
            log_memory_usage(f"訂閱排行發送完成")
        except Exception as cleanup_error:
            logging.error(f"訂閱排行發送記憶體清理時發生錯誤: {cleanup_error}")

async def send_message_to_user(user_id: int, message: str):
    """發送訊息給用戶"""
    try:
        url = f"https://api.telegram.org/bot{TOKEN}/sendMessage"
        data = {
            "chat_id": user_id,
            "text": message,
            "parse_mode": "HTML"
        }
        
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
            async with session.post(url, json=data) as resp:
                result = await resp.json()
                if result.get("ok"):
                    logging.info(f"成功發送訊息給用戶 {user_id}")
                else:
                    logging.error(f"發送訊息給用戶 {user_id} 失敗: {result}")
                    
    except Exception as e:
        logging.error(f"發送訊息給用戶 {user_id} 時發生錯誤: {e}")

async def ask_add_to_subscription(update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str, symbol_name: str):
    """詢問用戶是否要將標的加入訂閱清單"""
    user_id = update.effective_user.id
    
    # 檢查是否已經訂閱
    user_subs = get_user_subscriptions(user_id)
    if isinstance(user_subs, dict) and symbol in user_subs:
        await update.message.reply_text(
            f"✅ {symbol} ({symbol_name}) 已在您的訂閱清單中",
            reply_markup=create_main_keyboard()
        )
        return
    
    # 儲存詢問狀態
    user_states[user_id] = 'waiting_subscription_decision'
    context.user_data['pending_symbol'] = symbol
    context.user_data['pending_symbol_name'] = symbol_name
    
    # 建立詢問鍵盤
    keyboard = [
        [KeyboardButton("✅ 加入訂閱"), KeyboardButton("❌ 不加入")],
        [KeyboardButton("🔙 返回主選單")]
    ]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    
    await update.message.reply_text(
        f"📈 發現新標的：{symbol} ({symbol_name})\n\n"
        f"是否要將此標的加入您的訂閱清單？\n"
        f"加入後可以設定自動更新間隔。",
        reply_markup=reply_markup
    )

async def handle_add_to_subscription(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理加入訂閱的選擇"""
    user_id = update.effective_user.id
    symbol = context.user_data.get('pending_symbol')
    symbol_name = context.user_data.get('pending_symbol_name')
    
    if not symbol:
        await update.message.reply_text(
            "❌ 發生錯誤，請重新查詢標的",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 儲存選擇的標的
    context.user_data['selected_symbol'] = symbol
    context.user_data['selected_symbol_name'] = symbol_name
    
    await update.message.reply_text(
        f"已選擇：{symbol} ({symbol_name})\n\n請選擇訂閱間隔：",
        reply_markup=create_interval_keyboard()
    )
    
    # 更新狀態
    user_states[user_id] = 'waiting_interval'
    
    # 清理暫存資料
    if 'pending_symbol' in context.user_data:
        del context.user_data['pending_symbol']
    if 'pending_symbol_name' in context.user_data:
        del context.user_data['pending_symbol_name']

async def handle_decline_subscription(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理拒絕加入訂閱的選擇"""
    user_id = update.effective_user.id
    
    await update.message.reply_text(
        "✅ 已取消加入訂閱\n\n您可以隨時使用「📈 訂閱」功能來新增標的",
        reply_markup=create_main_keyboard()
    )
    
    # 清理暫存資料
    if 'pending_symbol' in context.user_data:
        del context.user_data['pending_symbol']
    if 'pending_symbol_name' in context.user_data:
        del context.user_data['pending_symbol_name']
    
    user_states[user_id] = 'main'

def save_analysis_data(symbol: str, analysis_data: dict, time_version: str):
    """儲存 analysis_data 到 data 資料夾"""
    try:
        # 確保 data 目錄存在
        if not os.path.exists('data'):
            os.makedirs('data')
        
        # 生成檔案名稱：symbol_timeversion_timestamp.json
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{symbol}_{time_version}_{timestamp}.json"
        filepath = os.path.join('data', filename)
        
        # 添加額外的元資料
        data_to_save = {
            'symbol': symbol,
            'time_version': time_version,
            'timestamp': timestamp,
            'created_time': datetime.now().isoformat(),
            'analysis_data': analysis_data
        }
        
        # 儲存為 JSON 檔案
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, ensure_ascii=False, indent=2, default=str)
        
        logging.info(f"已儲存 analysis_data: {filepath}")
        return filepath
        
    except Exception as e:
        logging.error(f"儲存 analysis_data 失敗: {e}")
        return None

def cleanup_old_analysis_data(hours: int = 24):
    """清理超過指定小時的舊 analysis_data 檔案"""
    try:
        if not os.path.exists('data'):
            return
        
        current_time = time.time()
        cutoff_time = current_time - (hours * 3600)  # 轉換為秒
        
        deleted_count = 0
        for filename in os.listdir('data'):
            if filename.endswith('.json'):
                filepath = os.path.join('data', filename)
                file_time = os.path.getmtime(filepath)
                
                if file_time < cutoff_time:
                    try:
                        os.remove(filepath)
                        deleted_count += 1
                        logging.info(f"已刪除舊檔案: {filename}")
                    except Exception as e:
                        logging.error(f"刪除檔案失敗 {filename}: {e}")
        
        if deleted_count > 0:
            logging.info(f"清理完成，共刪除 {deleted_count} 個舊檔案")
        
    except Exception as e:
        logging.error(f"清理舊 analysis_data 時發生錯誤: {e}")

def load_analysis_data(symbol: str, time_version: str = None, hours: int = 24):
    """載入指定標的的 analysis_data，可選擇時間版本和時間範圍"""
    try:
        if not os.path.exists('data'):
            return []
        
        current_time = time.time()
        cutoff_time = current_time - (hours * 3600)
        
        loaded_data = []
        
        for filename in os.listdir('data'):
            if not filename.endswith('.json'):
                continue
                
            # 檢查檔案名稱格式：symbol_timeversion_timestamp.json
            parts = filename.replace('.json', '').split('_')
            if len(parts) < 3:
                continue
                
            file_symbol = parts[0]
            file_time_version = parts[1]
            
            # 檢查標的和時間版本
            if file_symbol != symbol:
                continue
            if time_version and file_time_version != time_version:
                continue
            
            filepath = os.path.join('data', filename)
            file_time = os.path.getmtime(filepath)
            
            # 檢查檔案時間
            if file_time < cutoff_time:
                continue
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    loaded_data.append(data)
            except Exception as e:
                logging.error(f"讀取檔案失敗 {filename}: {e}")
        
        # 按時間排序（最新的在前）
        loaded_data.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        return loaded_data
        
    except Exception as e:
        logging.error(f"載入 analysis_data 時發生錯誤: {e}")
        return []

def get_latest_analysis_data(symbol: str, time_version: str = None):
    """獲取指定標的最新 analysis_data"""
    data_list = load_analysis_data(symbol, time_version, hours=24)
    if data_list:
        return data_list[0]  # 返回最新的資料
    return None

def main():
    """主函數"""
    # 設定日誌
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )
    
    # 初始化記憶體管理
    log_memory_usage("機器人啟動 - 記憶體管理初始化")
    
    # 清理舊的 analysis_data（24小時前的資料）
    cleanup_old_analysis_data(hours=24)
    
    # 載入頻率傳送清單
    load_frequency_lists()
    
    # 載入訂閱資料
    load_subscriptions()
    
    # 創建應用程式
    application = Application.builder().token(TOKEN).build()
    
    # 註冊命令處理器
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("notify", notify))
    application.add_handler(CommandHandler("stop", stop_command))
    application.add_handler(CommandHandler("status", status_command))
    application.add_handler(CommandHandler("subscribe", subscribe_command))
    application.add_handler(CommandHandler("unsubscribe", unsubscribe_command))
    application.add_handler(CommandHandler("subscriptions", subscriptions_command))
    
    # 註冊按鈕和訊息處理器
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_button))
    
    # 啟動機器人
    logging.info("啟動 FlipAdam 30分鐘通知機器人...")
    
    # 使用 post_init 來啟動訂閱任務
    async def post_init(application):
        await start_all_subscriptions()
        log_memory_usage("機器人啟動完成 - 所有訂閱任務已啟動")
    
    application.post_init = post_init
    application.run_polling()

if __name__ == '__main__':
    if sys.platform.startswith('win'):
        # Windows 特定設定
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    main() 