"""
訂閱功能插件
提供標的訂閱、定期分析、自動報告等功能
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path

from core.plugins.base_plugin import BasePlugin


class SubscriptionPlugin(BasePlugin):
    """訂閱功能插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "SubscriptionPlugin"
        self.version = "1.0.0"
        self.description = "提供標的訂閱、定期分析、自動報告等功能"
        self.author = "FlipAdam System"
        
        # 訂閱配置
        self.subscriptions_file = "config/subscriptions.json"
        self.subscriptions: Dict[str, Any] = {}
        
        # 分析間隔（秒）
        self.analysis_interval = 1800  # 30分鐘
        
        # 分析任務
        self.analysis_task: Optional[asyncio.Task] = None
        
        # 註冊命令
        self.register_command("subscribe", self._handle_subscribe_command)
        self.register_command("unsubscribe", self._handle_unsubscribe_command)
        self.register_command("subscriptions", self._handle_subscriptions_command)
        self.register_command("analysis", self._handle_analysis_command)
        self.register_command("report", self._handle_report_command)
    
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        try:
            self.logger.info("初始化訂閱插件...")
            
            # 載入配置
            self.config = config
            
            # 載入訂閱設定
            await self._load_subscriptions()
            
            # 註冊事件處理器
            if hasattr(self, 'event_bus'):
                self.event_bus.subscribe("analysis.completed", self._handle_analysis_completed)
                self.event_bus.subscribe("report.generated", self._handle_report_generated)
            
            self.is_initialized = True
            self.logger.info("訂閱插件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"訂閱插件初始化失敗: {e}")
            return False
    
    async def start(self) -> bool:
        """啟動插件"""
        try:
            self.logger.info("啟動訂閱插件...")
            
            # 啟動定期分析任務
            self.analysis_task = asyncio.create_task(self._analysis_loop())
            
            self.is_running = True
            self.logger.info("訂閱插件啟動完成")
            return True
            
        except Exception as e:
            self.logger.error(f"訂閱插件啟動失敗: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止插件"""
        try:
            self.logger.info("停止訂閱插件...")
            
            # 停止分析任務
            if self.analysis_task:
                self.analysis_task.cancel()
                try:
                    await self.analysis_task
                except asyncio.CancelledError:
                    pass
            
            self.is_running = False
            self.logger.info("訂閱插件已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"訂閱插件停止失敗: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """清理插件資源"""
        try:
            self.logger.info("清理訂閱插件資源...")
            
            # 保存訂閱設定
            await self._save_subscriptions()
            
            # 取消事件訂閱
            if hasattr(self, 'event_bus'):
                self.event_bus.unsubscribe("analysis.completed", self._handle_analysis_completed)
                self.event_bus.unsubscribe("report.generated", self._handle_report_generated)
            
            self.logger.info("訂閱插件資源清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"訂閱插件清理失敗: {e}")
            return False
    
    async def _load_subscriptions(self):
        """載入訂閱設定"""
        try:
            subscriptions_path = Path(self.subscriptions_file)
            if subscriptions_path.exists():
                with open(subscriptions_path, 'r', encoding='utf-8') as f:
                    self.subscriptions = json.load(f)
                self.logger.info(f"載入訂閱設定: {len(self.subscriptions)} 個訂閱")
            else:
                self.subscriptions = {}
                self.logger.info("創建新的訂閱設定檔案")
        except Exception as e:
            self.logger.error(f"載入訂閱設定失敗: {e}")
            self.subscriptions = {}
    
    async def _save_subscriptions(self):
        """保存訂閱設定"""
        try:
            subscriptions_path = Path(self.subscriptions_file)
            subscriptions_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(subscriptions_path, 'w', encoding='utf-8') as f:
                json.dump(self.subscriptions, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"保存訂閱設定: {len(self.subscriptions)} 個訂閱")
        except Exception as e:
            self.logger.error(f"保存訂閱設定失敗: {e}")
    
    async def _analysis_loop(self):
        """定期分析循環"""
        while self.is_running:
            try:
                await self._run_analysis()
                await asyncio.sleep(self.analysis_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"定期分析失敗: {e}")
                await asyncio.sleep(300)  # 錯誤時等待5分鐘
    
    async def _run_analysis(self):
        """執行分析"""
        if not self.subscriptions:
            return
        
        self.logger.info(f"開始定期分析 {len(self.subscriptions)} 個訂閱標的...")
        
        for user_id, user_subs in self.subscriptions.items():
            for symbol in user_subs.get('symbols', []):
                try:
                    await self._analyze_symbol(symbol, user_id)
                except Exception as e:
                    self.logger.error(f"分析標的 {symbol} 失敗: {e}")
    
    async def _analyze_symbol(self, symbol: str, user_id: str):
        """分析單個標的"""
        try:
            # 這裡應該調用實際的分析功能
            # 例如：FlipAdamBox、技術分析等
            
            # 發布分析事件
            if hasattr(self, 'event_bus'):
                await self.event_bus.publish("analysis.started", {
                    "symbol": symbol,
                    "user_id": user_id,
                    "timestamp": datetime.now().isoformat()
                })
            
            # 模擬分析過程
            await asyncio.sleep(1)
            
            # 發布分析完成事件
            if hasattr(self, 'event_bus'):
                await self.event_bus.publish("analysis.completed", {
                    "symbol": symbol,
                    "user_id": user_id,
                    "result": "success",
                    "timestamp": datetime.now().isoformat()
                })
            
            self.logger.info(f"完成標的分析: {symbol} (用戶: {user_id})")
            
        except Exception as e:
            self.logger.error(f"分析標的 {symbol} 失敗: {e}")
            
            # 發布分析失敗事件
            if hasattr(self, 'event_bus'):
                await self.event_bus.publish("analysis.failed", {
                    "symbol": symbol,
                    "user_id": user_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
    
    async def _handle_analysis_completed(self, event):
        """處理分析完成事件"""
        try:
            data = event.data
            symbol = data.get('symbol', '')
            user_id = data.get('user_id', '')
            
            self.logger.info(f"分析完成: {symbol} (用戶: {user_id})")
            
            # 這裡可以發送通知給用戶
            # 或者生成報告
            
        except Exception as e:
            self.logger.error(f"處理分析完成事件失敗: {e}")
    
    async def _handle_report_generated(self, event):
        """處理報告生成事件"""
        try:
            data = event.data
            symbol = data.get('symbol', '')
            user_id = data.get('user_id', '')
            report_path = data.get('report_path', '')
            
            self.logger.info(f"報告生成: {symbol} -> {report_path} (用戶: {user_id})")
            
        except Exception as e:
            self.logger.error(f"處理報告生成事件失敗: {e}")
    
    # 命令處理器
    async def _handle_subscribe_command(self, command: str, update, context) -> bool:
        """處理 /subscribe 命令"""
        if not context.args:
            await update.message.reply_text("""
📊 訂閱功能說明

📋 可用命令：
/subscribe <標的> - 訂閱標的
/unsubscribe <標的> - 取消訂閱
/subscriptions - 查看訂閱列表
/analysis <標的> - 立即分析標的
/report <標的> - 生成報告

📝 範例：
/subscribe BTC-USD
/subscribe ETH-USD
/analysis BTC-USD
            """)
            return True
        
        symbol = context.args[0].upper()
        user_id = str(update.effective_user.id)
        
        # 添加訂閱
        if user_id not in self.subscriptions:
            self.subscriptions[user_id] = {
                "user_id": user_id,
                "symbols": [],
                "created_at": datetime.now().isoformat(),
                "last_analysis": None
            }
        
        if symbol not in self.subscriptions[user_id]["symbols"]:
            self.subscriptions[user_id]["symbols"].append(symbol)
            await self._save_subscriptions()
            await update.message.reply_text(f"✅ 成功訂閱: {symbol}")
        else:
            await update.message.reply_text(f"ℹ️ 已經訂閱: {symbol}")
        
        return True
    
    async def _handle_unsubscribe_command(self, command: str, update, context) -> bool:
        """處理 /unsubscribe 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供標的名稱：/unsubscribe <標的>")
            return True
        
        symbol = context.args[0].upper()
        user_id = str(update.effective_user.id)
        
        if user_id in self.subscriptions and symbol in self.subscriptions[user_id]["symbols"]:
            self.subscriptions[user_id]["symbols"].remove(symbol)
            await self._save_subscriptions()
            await update.message.reply_text(f"✅ 取消訂閱: {symbol}")
        else:
            await update.message.reply_text(f"❌ 未找到訂閱: {symbol}")
        
        return True
    
    async def _handle_subscriptions_command(self, command: str, update, context) -> bool:
        """處理 /subscriptions 命令"""
        user_id = str(update.effective_user.id)
        
        if user_id not in self.subscriptions or not self.subscriptions[user_id]["symbols"]:
            await update.message.reply_text("📭 您目前沒有訂閱任何標的")
            return True
        
        symbols = self.subscriptions[user_id]["symbols"]
        subs_text = f"📊 您的訂閱列表 ({len(symbols)} 個標的)：\n\n"
        
        for i, symbol in enumerate(symbols, 1):
            subs_text += f"{i}. {symbol}\n"
        
        subs_text += f"\n💡 使用 /analysis <標的> 進行立即分析"
        await update.message.reply_text(subs_text)
        return True
    
    async def _handle_analysis_command(self, command: str, update, context) -> bool:
        """處理 /analysis 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供標的名稱：/analysis <標的>")
            return True
        
        symbol = context.args[0].upper()
        user_id = str(update.effective_user.id)
        
        # 檢查是否已訂閱
        if user_id not in self.subscriptions or symbol not in self.subscriptions[user_id]["symbols"]:
            await update.message.reply_text(f"❌ 您未訂閱 {symbol}，請先使用 /subscribe {symbol}")
            return True
        
        # 發送分析開始消息
        await update.message.reply_text(f"🔍 開始分析 {symbol}...")
        
        # 執行分析
        try:
            await self._analyze_symbol(symbol, user_id)
            await update.message.reply_text(f"✅ {symbol} 分析完成")
        except Exception as e:
            await update.message.reply_text(f"❌ {symbol} 分析失敗: {e}")
        
        return True
    
    async def _handle_report_command(self, command: str, update, context) -> bool:
        """處理 /report 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供標的名稱：/report <標的>")
            return True
        
        symbol = context.args[0].upper()
        user_id = str(update.effective_user.id)
        
        # 檢查是否已訂閱
        if user_id not in self.subscriptions or symbol not in self.subscriptions[user_id]["symbols"]:
            await update.message.reply_text(f"❌ 您未訂閱 {symbol}，請先使用 /subscribe {symbol}")
            return True
        
        # 發送報告生成消息
        await update.message.reply_text(f"📊 正在生成 {symbol} 報告...")
        
        # 這裡應該實現實際的報告生成邏輯
        # 目前只是示例實現
        
        # 發布報告生成事件
        if hasattr(self, 'event_bus'):
            await self.event_bus.publish("report.generated", {
                "symbol": symbol,
                "user_id": user_id,
                "report_path": f"reports/{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "timestamp": datetime.now().isoformat()
            })
        
        await update.message.reply_text(f"✅ {symbol} 報告已生成")
        return True
    
    def get_help_text(self) -> str:
        """獲取插件說明文字"""
        return """
📊 訂閱功能插件

功能：
• 標的訂閱管理
• 定期自動分析
• 報告生成
• 用戶通知

命令：
/subscribe - 訂閱標的
/unsubscribe - 取消訂閱
/subscriptions - 查看訂閱列表
/analysis - 立即分析
/report - 生成報告
        """
    
    def get_status(self) -> Dict[str, Any]:
        """獲取插件狀態"""
        total_subscriptions = sum(len(user_subs.get('symbols', [])) for user_subs in self.subscriptions.values())
        
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": self.author,
            "initialized": self.is_initialized,
            "running": self.is_running,
            "enabled": self.is_enabled,
            "users_count": len(self.subscriptions),
            "total_subscriptions": total_subscriptions,
            "analysis_interval": self.analysis_interval,
            "commands": list(self.commands.keys()),
            "message_handlers": list(self.message_handlers.keys())
        }


# 插件工廠函數
def create_subscription_plugin() -> SubscriptionPlugin:
    """創建訂閱插件實例"""
    return SubscriptionPlugin()
