import pandas as pd
import numpy as np
import datetime as dt
import yfinance as yf
import matplotlib.pyplot as plt
from pandas.tseries.offsets import BDay

from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense



#==========================#
# 1. 資料獲取 (Data Acquisition)
#==========================#

symbol = "2330.TW"  # 可改成其他股票代碼，例如 "2330.TW"
end_date = dt.date.today()
start_date = end_date - dt.timedelta(days=365)  # 過去一年

# 下載資料（注意：yfinance 現在 auto_adjust 預設為 True）
df = yf.download(symbol, start=start_date, end=end_date, progress=False)

# 若欄位為 MultiIndex，則將欄位扁平化：
if isinstance(df.columns, pd.MultiIndex):
    # 根據觀察，第一層包含正確的欄位名稱（例如 'Open', 'High', ...）
    df.columns = df.columns.get_level_values(0)

# 選擇需要的欄位並移除缺失值
df = df[['Open', 'High', 'Low', 'Close', 'Volume']].dropna()
print(f"Downloaded {len(df)} rows of data for {symbol}")
print(df.tail(5))

#==========================#
# 2. 技術指標計算與數據過濾 (Technical Indicators & Noise Filtering)
#==========================#

# --- RSI 計算 (14日) ---
window_rsi = 14
delta = df['Close'].diff(1)
gain = delta.where(delta > 0, 0.0)
loss = -delta.where(delta < 0, 0.0)

# 使用 Wilder 的方法計算平滑平均
avg_gain = gain.rolling(window_rsi).mean()
avg_loss = loss.rolling(window_rsi).mean()

# 初始化前 window_rsi 天平均值
avg_gain.iloc[:window_rsi] = gain.iloc[:window_rsi].mean()
avg_loss.iloc[:window_rsi] = loss.iloc[:window_rsi].mean()

for i in range(window_rsi, len(df)):
    avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (window_rsi - 1) + gain.iloc[i]) / window_rsi
    avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (window_rsi - 1) + loss.iloc[i]) / window_rsi

RS = avg_gain / avg_loss
df['RSI'] = 100 - (100 / (1 + RS))

# --- 布林通道 (Bollinger Bands, 20日均線, ±2倍標準差) ---
window_bb = 20
df['MA20'] = df['Close'].rolling(window_bb).mean()
df['BB_std'] = df['Close'].rolling(window_bb).std()
df['BB_upper'] = df['MA20'] + 2 * df['BB_std']
df['BB_lower'] = df['MA20'] - 2 * df['BB_std']

# 計算 %B：注意此處確保運算結果為 Series（加上 .squeeze() 以避免 DataFrame 結果）
df['BB_%B'] = ((df['Close'] - df['BB_lower']) / (df['BB_upper'] - df['BB_lower'])).squeeze()

# --- ATR 計算 (14日) ---
window_atr = 14
high_low = df['High'] - df['Low']
high_prevclose = (df['High'] - df['Close'].shift(1)).abs()
low_prevclose  = (df['Low'] - df['Close'].shift(1)).abs()
df['TR'] = np.maximum.reduce([high_low, high_prevclose, low_prevclose])

df['ATR'] = 0.0
df['ATR'].iloc[:window_atr] = df['TR'].iloc[:window_atr].mean()
for i in range(window_atr, len(df)):
    df['ATR'].iat[i] = (df['ATR'].iat[i-1] * (window_atr - 1) + df['TR'].iat[i]) / window_atr

# --- 移動平均線 ---
df['SMA50'] = df['Close'].rolling(50).mean()
df['EMA50'] = df['Close'].ewm(span=50, adjust=False).mean()

# --- 噪音過濾指標 ---
df['Volatility_ratio'] = (df['High'] - df['Low']) / df['ATR']

#==========================#
# 3. LSTM 模型訓練與預測 (LSTM Model Training & Forecasting)
#==========================#

# 使用收盤價作為預測目標
prices = df['Close'].values.reshape(-1, 1)
scaler = MinMaxScaler(feature_range=(0, 1))
scaled_prices = scaler.fit_transform(prices)

window_size = 60  # 使用過去 60 天預測下一天
X = []
y = []
for i in range(window_size, len(scaled_prices)):
    X.append(scaled_prices[i-window_size:i, 0])
    y.append(scaled_prices[i, 0])
X = np.array(X)
y = np.array(y)
X = X.reshape((X.shape[0], X.shape[1], 1))

# 拆分訓練集和測試集（最後 30 天作為測試）
test_size = 30
X_train, X_test = X[:-test_size], X[-test_size:]
y_train, y_test = y[:-test_size], y[-test_size:]

model = Sequential()
model.add(LSTM(50, activation='relu', return_sequences=False, input_shape=(window_size, 1)))
model.add(Dense(1))
model.compile(optimizer='adam', loss='mse')

model.fit(X_train, y_train, epochs=20, batch_size=16, verbose=0)

y_pred = model.predict(X_test)
y_pred_actual = scaler.inverse_transform(y_pred)
y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))

mse = np.mean((y_pred_actual - y_test_actual) ** 2)
rmse = np.sqrt(mse)
print(f"\nLSTM 預測未來 {test_size} 天的 MSE: {mse:.4f}, RMSE: {rmse:.4f}")

# 從訓練集最後一天開始迭代預測未來 30 天
last_sequence = scaled_prices[-window_size:].flatten().tolist()
future_predictions = []
predict_days = 30
for _ in range(predict_days):
    X_input = np.array(last_sequence[-window_size:]).reshape(1, window_size, 1)
    next_pred_scaled = model.predict(X_input, verbose=0)
    future_predictions.append(next_pred_scaled[0, 0])
    last_sequence.append(next_pred_scaled[0, 0])
future_predictions = scaler.inverse_transform(np.array(future_predictions).reshape(-1, 1))
print(f"\n未來 {predict_days} 天預測價格：")
print(future_predictions.flatten())

#==========================#
# 4. 亞當理論應用：識別反射點與潛在進場 (Adam Theory Reflection Points & Entry Signals)
#==========================#

pred_series = future_predictions.flatten()
pred_change = pred_series[-1] - pred_series[0]
threshold = np.std(pred_series) * 0.1

if pred_change > threshold:
    trend_signal = "up"
elif pred_change < -threshold:
    trend_signal = "down"
else:
    trend_signal = "sideways"

print(f"\nBased on LSTM prediction, future trend: {trend_signal}")

pivot_index = None
start_index = None
pivot_price = None
start_price = None

if trend_signal == "up":
    lookback = 60  # Look for the lowest point (trough) in the last 60 days
    recent_window = df['Close'][-lookback:]
    pivot_index = recent_window.idxmin()
    pivot_price = df.loc[pivot_index, 'Close']
    prev_window = df.loc[:pivot_index, 'Close']
    if len(prev_window) > 0:
        start_index = prev_window.idxmax()
        start_price = df.loc[start_index, 'Close']
    else:
        start_index, start_price = pivot_index, pivot_price
elif trend_signal == "down":
    lookback = 60
    recent_window = df['Close'][-lookback:]
    pivot_index = recent_window.idxmax()
    pivot_price = df.loc[pivot_index, 'Close']
    prev_window = df.loc[:pivot_index, 'Close']
    if len(prev_window) > 0:
        start_index = prev_window.idxmin()
        start_price = df.loc[start_index, 'Close']
    else:
        start_index, start_price = pivot_index, pivot_price
else:
    print("No clear reflection points detected; market is sideways. No reflection path will be built.")

if pivot_index:
    print(f"First reflection trend start: {start_index.date()}, Price = {start_price:.2f}")
    print(f"Second reflection trend start: {pivot_index.date()}, Price = {pivot_price:.2f}")

    actual_path = df.loc[start_index:pivot_index, 'Close']
    actual_values = actual_path.values
    duration = len(actual_values) - 1
    future_dates = pd.date_range(start=pivot_index + BDay(1), periods=duration, freq=BDay())
    reflected_values = pivot_price + (pivot_price - actual_values[::-1][1:])
    reflected_path = pd.Series(data=reflected_values, index=future_dates)

    entry_index = pivot_index
    entry_price = pivot_price
    print(f"Potential entry point: {entry_index.date()}, Price = {entry_price:.2f} (Based on Adam theory reflection point)")

    plt.figure(figsize=(10,6))
    plt.plot(df.index, df['Close'], label='Stock Price', color='blue')
    plt.axvline(pivot_index, color='gray', linestyle='--', alpha=0.7)
    plt.plot(actual_path.index, actual_path.values, label='First Reflection Path', color='red', linewidth=2)
    plt.plot(reflected_path.index, reflected_path.values, label='Second Reflection Path', color='green', linestyle='--', linewidth=2)
    plt.scatter(entry_index, entry_price, color='orange', marker='o', s=100, label='Potential Entry Point')
    plt.title(f"{symbol} First/Second Reflection Trend")
    plt.xlabel("Date")
    plt.ylabel("Price")
    plt.legend()
    plt.tight_layout()
    plt.show()  # Uncomment to display the plot

#==========================#
# 5. 交易策略回測與勝率計算 (Trading Backtest & Win Rate)
#==========================#

prices_array = df['Close'].values
local_max_idx = [i for i in range(1, len(prices_array)-1)
                 if prices_array[i] > prices_array[i-1] and prices_array[i] > prices_array[i+1]]
local_min_idx = [i for i in range(1, len(prices_array)-1)
                 if prices_array[i] < prices_array[i-1] and prices_array[i] < prices_array[i+1]]
pivots_idx = sorted(local_max_idx + local_min_idx)

significant_pivots = []
if pivots_idx:
    last_idx = pivots_idx[0]
    last_type = 'min' if last_idx in local_min_idx else 'max'
    significant_pivots.append(last_idx)
    for idx in pivots_idx[1:]:
        current_type = 'min' if idx in local_min_idx else 'max'
        if current_type == last_type:
            continue
        if abs(prices_array[idx] - prices_array[last_idx]) >= df['ATR'].iloc[idx]:
            significant_pivots.append(idx)
            last_idx = idx
            last_type = current_type

wins = 0
trades = 0
for i in range(len(significant_pivots) - 1):
    start_idx = significant_pivots[i]
    end_idx = significant_pivots[i+1]
    if start_idx in local_min_idx and end_idx in local_max_idx:
        entry_price = prices_array[start_idx]
        exit_price = prices_array[end_idx]
        profit = exit_price - entry_price
    elif start_idx in local_max_idx and end_idx in local_min_idx:
        entry_price = prices_array[start_idx]
        exit_price = prices_array[end_idx]
        profit = entry_price - exit_price
    else:
        continue
    trades += 1
    if profit > 0:
        wins += 1

if trades > 0:
    win_rate = wins / trades
    print(f"\n回測結果:")
    print(f"總交易次數: {trades}, 獲利交易次數: {wins}, 勝率: {win_rate*100:.2f}%")
else:
    print("\n未偵測到足夠的趨勢反轉信號進行回測。")
