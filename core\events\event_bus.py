"""
事件總線系統
處理插件間的事件通信和消息傳遞
"""

import asyncio
import logging
from typing import Dict, List, Callable, Any, Optional
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime


@dataclass
class Event:
    """事件數據類別"""
    event_type: str
    data: Dict[str, Any]
    timestamp: datetime
    source: Optional[str] = None


class EventBus:
    """
    事件總線系統
    提供事件訂閱、發布和處理功能
    """
    
    def __init__(self):
        """初始化事件總線"""
        self.logger = logging.getLogger("EventBus")
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.event_history: List[Event] = []
        self.max_history_size = 1000
        self.is_initialized = False
        
        # 統計信息
        self.stats = {
            "total_events": 0,
            "total_subscribers": 0,
            "events_by_type": defaultdict(int)
        }
    
    async def initialize(self):
        """初始化事件總線"""
        try:
            self.logger.info("初始化事件總線...")
            
            # 註冊系統事件處理器
            self._register_system_handlers()
            
            self.is_initialized = True
            self.logger.info("事件總線初始化完成")
            
        except Exception as e:
            self.logger.error(f"事件總線初始化失敗: {e}")
            raise
    
    def _register_system_handlers(self):
        """註冊系統事件處理器"""
        # 註冊事件統計處理器
        self.subscribe("system.stats", self._handle_stats_event)
        
        # 註冊錯誤事件處理器
        self.subscribe("system.error", self._handle_error_event)
        
        # 註冊插件生命週期事件處理器
        self.subscribe("plugin.*", self._handle_plugin_event)
    
    def subscribe(self, event_type: str, handler: Callable):
        """
        訂閱事件
        
        Args:
            event_type: 事件類型（支援通配符 *）
            handler: 事件處理函數
        """
        try:
            self.subscribers[event_type].append(handler)
            self.stats["total_subscribers"] += 1
            
            self.logger.debug(f"訂閱事件: {event_type} -> {handler.__name__}")
            
        except Exception as e:
            self.logger.error(f"訂閱事件失敗: {e}")
    
    def unsubscribe(self, event_type: str, handler: Callable):
        """
        取消訂閱事件
        
        Args:
            event_type: 事件類型
            handler: 事件處理函數
        """
        try:
            if event_type in self.subscribers:
                if handler in self.subscribers[event_type]:
                    self.subscribers[event_type].remove(handler)
                    self.stats["total_subscribers"] -= 1
                    
                    self.logger.debug(f"取消訂閱事件: {event_type} -> {handler.__name__}")
            
        except Exception as e:
            self.logger.error(f"取消訂閱事件失敗: {e}")
    
    async def publish(self, event_type: str, data: Dict[str, Any], source: Optional[str] = None):
        """
        發布事件
        
        Args:
            event_type: 事件類型
            data: 事件數據
            source: 事件來源
        """
        try:
            # 創建事件對象
            event = Event(
                event_type=event_type,
                data=data,
                timestamp=datetime.now(),
                source=source
            )
            
            # 添加到歷史記錄
            self.event_history.append(event)
            if len(self.event_history) > self.max_history_size:
                self.event_history.pop(0)
            
            # 更新統計信息
            self.stats["total_events"] += 1
            self.stats["events_by_type"][event_type] += 1
            
            # 查找匹配的訂閱者
            handlers = []
            
            # 精確匹配
            if event_type in self.subscribers:
                handlers.extend(self.subscribers[event_type])
            
            # 通配符匹配
            for pattern, pattern_handlers in self.subscribers.items():
                if self._match_pattern(event_type, pattern):
                    handlers.extend(pattern_handlers)
            
            # 執行處理器
            if handlers:
                await self._execute_handlers(event, handlers)
            else:
                self.logger.debug(f"事件 {event_type} 沒有訂閱者")
            
        except Exception as e:
            self.logger.error(f"發布事件失敗: {e}")
            # 發布錯誤事件
            await self.publish("system.error", {
                "error": str(e),
                "event_type": event_type,
                "source": source
            })
    
    def _match_pattern(self, event_type: str, pattern: str) -> bool:
        """
        檢查事件類型是否匹配模式
        
        Args:
            event_type: 事件類型
            pattern: 模式（支援通配符 *）
            
        Returns:
            是否匹配
        """
        if pattern == "*":
            return True
        
        if "*" not in pattern:
            return event_type == pattern
        
        # 處理通配符模式
        parts = pattern.split(".")
        event_parts = event_type.split(".")
        
        if len(parts) != len(event_parts):
            return False
        
        for i, part in enumerate(parts):
            if part != "*" and part != event_parts[i]:
                return False
        
        return True
    
    async def _execute_handlers(self, event: Event, handlers: List[Callable]):
        """
        執行事件處理器
        
        Args:
            event: 事件對象
            handlers: 處理器列表
        """
        try:
            # 並行執行所有處理器
            tasks = []
            for handler in handlers:
                task = asyncio.create_task(self._execute_handler(event, handler))
                tasks.append(task)
            
            # 等待所有處理器完成
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"執行事件處理器失敗: {e}")
    
    async def _execute_handler(self, event: Event, handler: Callable):
        """
        執行單個事件處理器
        
        Args:
            event: 事件對象
            handler: 處理器函數
        """
        try:
            if asyncio.iscoroutinefunction(handler):
                await handler(event)
            else:
                handler(event)
                
        except Exception as e:
            self.logger.error(f"事件處理器 {handler.__name__} 執行失敗: {e}")
    
    async def _handle_stats_event(self, event: Event):
        """處理統計事件"""
        self.logger.info(f"統計事件: {event.event_type} - {event.data}")
    
    async def _handle_error_event(self, event: Event):
        """處理錯誤事件"""
        self.logger.error(f"系統錯誤: {event.data}")
    
    async def _handle_plugin_event(self, event: Event):
        """處理插件事件"""
        self.logger.info(f"插件事件: {event.event_type} - {event.data}")
    
    def get_subscribers(self, event_type: str) -> List[Callable]:
        """
        獲取指定事件類型的訂閱者
        
        Args:
            event_type: 事件類型
            
        Returns:
            訂閱者列表
        """
        return self.subscribers.get(event_type, [])
    
    def get_all_subscribers(self) -> Dict[str, List[Callable]]:
        """
        獲取所有訂閱者
        
        Returns:
            訂閱者字典
        """
        return dict(self.subscribers)
    
    def get_event_history(self, event_type: Optional[str] = None, limit: int = 100) -> List[Event]:
        """
        獲取事件歷史
        
        Args:
            event_type: 事件類型（可選）
            limit: 限制數量
            
        Returns:
            事件歷史列表
        """
        if event_type:
            filtered_events = [e for e in self.event_history if e.event_type == event_type]
        else:
            filtered_events = self.event_history
        
        return filtered_events[-limit:]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        獲取統計信息
        
        Returns:
            統計信息字典
        """
        return {
            "total_events": self.stats["total_events"],
            "total_subscribers": self.stats["total_subscribers"],
            "events_by_type": dict(self.stats["events_by_type"]),
            "history_size": len(self.event_history),
            "is_initialized": self.is_initialized
        }
    
    def clear_history(self):
        """清空事件歷史"""
        self.event_history.clear()
        self.logger.info("事件歷史已清空")
    
    def clear_subscribers(self, event_type: Optional[str] = None):
        """
        清空訂閱者
        
        Args:
            event_type: 事件類型（可選，如果為None則清空所有）
        """
        if event_type:
            if event_type in self.subscribers:
                count = len(self.subscribers[event_type])
                self.subscribers[event_type].clear()
                self.stats["total_subscribers"] -= count
                self.logger.info(f"已清空事件 {event_type} 的 {count} 個訂閱者")
        else:
            total_count = sum(len(handlers) for handlers in self.subscribers.values())
            self.subscribers.clear()
            self.stats["total_subscribers"] = 0
            self.logger.info(f"已清空所有 {total_count} 個訂閱者")
    
    async def broadcast(self, data: Dict[str, Any], source: Optional[str] = None):
        """
        廣播事件到所有訂閱者
        
        Args:
            data: 事件數據
            source: 事件來源
        """
        await self.publish("*", data, source)
    
    async def cleanup(self):
        """清理事件總線"""
        try:
            self.logger.info("清理事件總線...")
            
            # 清空訂閱者
            self.clear_subscribers()
            
            # 清空歷史記錄
            self.clear_history()
            
            # 重置統計信息
            self.stats = {
                "total_events": 0,
                "total_subscribers": 0,
                "events_by_type": defaultdict(int)
            }
            
            self.is_initialized = False
            self.logger.info("事件總線清理完成")
            
        except Exception as e:
            self.logger.error(f"清理事件總線失敗: {e}")


# 便捷函數
def create_event_bus() -> EventBus:
    """創建事件總線實例"""
    return EventBus()


async def setup_event_bus() -> EventBus:
    """設置並初始化事件總線"""
    event_bus = create_event_bus()
    await event_bus.initialize()
    return event_bus 