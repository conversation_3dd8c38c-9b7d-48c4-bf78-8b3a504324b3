# FlipAdamMLV5 交易分析系統

## 專案說明
FlipAdamMLV5 是一個基於 Adam Theory 的金融市場分析系統，用於分析股票、加密貨幣、基金和指數的市場走勢。系統結合了技術分析和機器學習方法，提供全面的市場分析報告。

## 主要功能
1. **多市場分析**
   - 股票市場分析
   - 加密貨幣分析
   - 基金分析
   - 指數分析

2. **技術分析指標**
   - Adam Theory 雙重反射分析
   - ATR (Average True Range) 分析
   - 移動平均線分析
   - 趨勢線分析
   - 標準差帶分析

3. **自動化報告生成**
   - 自動生成 HTML 格式分析報告
   - 自動發送郵件通知
   - 支援分批發送大型報告

## 程式邏輯說明
1. **資料獲取與處理**
   - 使用 yfinance 獲取過去一年的歷史數據
   - 資料包含開盤價、最高價、最低價、收盤價和成交量
   - 實現重試機制和延遲策略，確保資料獲取穩定性

2. **技術分析流程**
   - **ATR 計算與最佳化**
     - 計算 14 日 ATR
     - 自動最佳化 ATR 週期（5-30天）和倍數（0.5-3.0）
     - 計算追踪止損價

   - **Adam Theory 分析**
     - 使用最新收盤價作為轉折點
     - 以最近 30 天作為起點
     - 計算歷史路徑和反射路徑
     - 生成雙重反射預測

   - **趨勢分析**
     - 計算 60 日移動平均線
     - 使用回歸分析判斷趨勢方向
     - 計算標準差帶（1-3個標準差）

3. **交易信號生成**
   - **突破信號**
     - 價格突破追踪止損線
     - 向上突破和向下跌破的判斷

   - **趨勢信號**
     - 基於回歸斜率的趨勢判斷
     - 移動平均線交叉信號

   - **跳空信號**
     - 基於 ATR 的跳空判斷
     - 20 天 ATR 的 1.5 倍作為跳空標準

4. **報告生成與發送**
   - **圖表生成**
     - 繪製價格走勢圖
     - 添加技術指標和信號標記
     - 生成分析報告

   - **郵件發送**
     - 自動生成 HTML 格式報告
     - 根據附件大小自動分批發送
     - 支援多種報告類型（股票、加密貨幣、基金、指數）

5. **批次處理機制**
   - 每批處理 5 個標的
   - 批次間延遲 5 秒
   - 自動清理舊的分析圖表

## 系統需求
- Python 3.7+
- 必要的 Python 套件：
  ```
  pandas
  numpy
  yfinance
  matplotlib
  curl_cffi
  ```

## 安裝步驟
1. 克隆專案：
   ```bash
   git clone [專案網址]
   ```

2. 安裝必要套件：
   ```bash
   pip install -r requirements.txt
   ```

3. 安裝字體：
   - 將 `TaipeiSansTCBeta-Regular.ttf` 字體檔案放在專案根目錄

## 使用方法
1. 準備資料檔案：
   - `stock_names_coin.csv`：加密貨幣列表
   - `stock_name_hold_stockonly_SortbyValue.csv`：股票列表
   - `stock_name_hold_fundsonly_SortbyValue.csv`：基金列表
   - `stock_names_watch_index.csv`：指數列表

2. 執行程式：
   ```bash
   python FlipadamMLV5.py
   ```

3. 查看結果：
   - 分析報告將自動生成在 `figure` 目錄
   - 報告將通過電子郵件發送

## 郵件通知
- 收件人：<EMAIL>
- 郵件主旨格式：
  - 股票分析報告(30天) - YYYY-MM-DD
  - 加密貨幣分析報告(30天) - YYYY-MM-DD
  - 基金分析報告(30天) - YYYY-MM-DD
  - 指數分析報告(30天) - YYYY-MM-DD

## 注意事項
1. 確保網路連接穩定
2. 確保有足夠的磁碟空間存儲分析圖表
3. 郵件附件大小限制為 20MB，超過將自動分批發送

## 授權說明
[請在此處添加授權說明]

## 聯絡方式
[請在此處添加聯絡方式] 