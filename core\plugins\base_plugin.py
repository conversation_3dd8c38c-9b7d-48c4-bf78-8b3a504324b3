"""
基礎插件介面
定義所有插件必須實現的方法和屬性
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod


class BasePlugin(ABC):
    """
    基礎插件類別
    所有插件必須繼承此類別並實現必要的方法
    """
    
    def __init__(self):
        """初始化插件"""
        self.name: str = "BasePlugin"
        self.version: str = "1.0.0"
        self.description: str = "基礎插件"
        self.author: str = "System"
        self.dependencies: List[str] = []
        
        # 插件狀態
        self.is_initialized: bool = False
        self.is_running: bool = False
        self.is_enabled: bool = True
        
        # 配置和數據
        self.config: Dict[str, Any] = {}
        self.commands: Dict[str, Any] = {}
        self.message_handlers: Dict[str, Any] = {}
        
        # 日誌
        self.logger = logging.getLogger(f"Plugin.{self.name}")
        
        # 插件路徑（用於重新載入）
        self._plugin_path: Optional[str] = None
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """
        初始化插件
        
        Args:
            config: 插件配置
            
        Returns:
            初始化是否成功
        """
        pass
    
    @abstractmethod
    async def start(self) -> bool:
        """
        啟動插件
        
        Returns:
            啟動是否成功
        """
        pass
    
    @abstractmethod
    async def stop(self) -> bool:
        """
        停止插件
        
        Returns:
            停止是否成功
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> bool:
        """
        清理插件資源
        
        Returns:
            清理是否成功
        """
        pass
    
    def get_help_text(self) -> str:
        """
        獲取插件說明文字
        
        Returns:
            說明文字
        """
        return f"{self.name} v{self.version}: {self.description}"
    
    def get_status(self) -> Dict[str, Any]:
        """
        獲取插件狀態
        
        Returns:
            狀態字典
        """
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": self.author,
            "initialized": self.is_initialized,
            "running": self.is_running,
            "enabled": self.is_enabled,
            "commands": list(self.commands.keys()),
            "message_handlers": list(self.message_handlers.keys()),
            "dependencies": self.dependencies
        }
    
    async def handle_command(self, command: str, update, context) -> bool:
        """
        處理命令
        
        Args:
            command: 命令名稱
            update: Telegram Update 對象
            context: Telegram Context 對象
            
        Returns:
            是否處理成功
        """
        if command in self.commands:
            try:
                handler = self.commands[command]
                if asyncio.iscoroutinefunction(handler):
                    await handler(update, context)
                else:
                    handler(update, context)
                return True
            except Exception as e:
                self.logger.error(f"處理命令 {command} 失敗: {e}")
                return False
        return False
    
    async def handle_message(self, message: str, update, context) -> bool:
        """
        處理消息
        
        Args:
            message: 消息內容
            update: Telegram Update 對象
            context: Telegram Context 對象
            
        Returns:
            是否處理成功
        """
        for pattern, handler in self.message_handlers.items():
            if self._match_message_pattern(message, pattern):
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(message, update, context)
                    else:
                        handler(message, update, context)
                    return True
                except Exception as e:
                    self.logger.error(f"處理消息 {message} 失敗: {e}")
                    return False
        return False
    
    def _match_message_pattern(self, message: str, pattern: str) -> bool:
        """
        檢查消息是否匹配模式
        
        Args:
            message: 消息內容
            pattern: 模式
            
        Returns:
            是否匹配
        """
        # 簡單的字符串匹配，可以擴展為正則表達式
        return pattern in message
    
    def register_command(self, command: str, handler: Any):
        """
        註冊命令處理器
        
        Args:
            command: 命令名稱
            handler: 處理器函數
        """
        self.commands[command] = handler
        self.logger.info(f"註冊命令: {command}")
    
    def register_message_handler(self, pattern: str, handler: Any):
        """
        註冊消息處理器
        
        Args:
            pattern: 消息模式
            handler: 處理器函數
        """
        self.message_handlers[pattern] = handler
        self.logger.info(f"註冊消息處理器: {pattern}")
    
    def unregister_command(self, command: str):
        """
        取消註冊命令
        
        Args:
            command: 命令名稱
        """
        if command in self.commands:
            del self.commands[command]
            self.logger.info(f"取消註冊命令: {command}")
    
    def unregister_message_handler(self, pattern: str):
        """
        取消註冊消息處理器
        
        Args:
            pattern: 消息模式
        """
        if pattern in self.message_handlers:
            del self.message_handlers[pattern]
            self.logger.info(f"取消註冊消息處理器: {pattern}")
    
    def set_plugin_path(self, path: str):
        """
        設置插件路徑
        
        Args:
            path: 插件檔案路徑
        """
        self._plugin_path = path
    
    def get_plugin_path(self) -> Optional[str]:
        """
        獲取插件路徑
        
        Returns:
            插件檔案路徑
        """
        return self._plugin_path
    
    def check_dependencies(self, available_plugins: List[str]) -> bool:
        """
        檢查依賴是否滿足
        
        Args:
            available_plugins: 可用的插件列表
            
        Returns:
            依賴是否滿足
        """
        for dependency in self.dependencies:
            if dependency not in available_plugins:
                self.logger.warning(f"缺少依賴: {dependency}")
                return False
        return True
    
    def get_dependencies(self) -> List[str]:
        """
        獲取依賴列表
        
        Returns:
            依賴列表
        """
        return self.dependencies.copy()
    
    def is_plugin_ready(self) -> bool:
        """
        檢查插件是否準備就緒
        
        Returns:
            是否準備就緒
        """
        return self.is_initialized and self.is_enabled
    
    def enable(self):
        """啟用插件"""
        self.is_enabled = True
        self.logger.info(f"插件 {self.name} 已啟用")
    
    def disable(self):
        """停用插件"""
        self.is_enabled = False
        self.logger.info(f"插件 {self.name} 已停用")
    
    def get_config(self, key: str = None, default: Any = None) -> Any:
        """
        獲取插件配置
        
        Args:
            key: 配置鍵
            default: 預設值
            
        Returns:
            配置值
        """
        if key is None:
            return self.config
        
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set_config(self, key: str, value: Any):
        """
        設置插件配置
        
        Args:
            key: 配置鍵
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.logger.debug(f"設置配置: {key} = {value}")


# 便捷函數
def create_base_plugin(name: str, version: str = "1.0.0", description: str = "") -> BasePlugin:
    """
    創建基礎插件實例
    
    Args:
        name: 插件名稱
        version: 插件版本
        description: 插件描述
        
    Returns:
        基礎插件實例
    """
    plugin = BasePlugin()
    plugin.name = name
    plugin.version = version
    plugin.description = description
    return plugin 