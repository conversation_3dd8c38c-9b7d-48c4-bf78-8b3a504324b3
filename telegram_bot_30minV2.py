import logging
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, ReplyKeyboardRemove
from telegram.ext import Application, CommandHandler, ContextTypes, MessageHandler, filters
import asyncio
import sys
import pandas as pd
import os
import re
import yfinance as yf
from telegram import InputMediaPhoto
from telegram.error import RetryAfter, TimedOut, NetworkError
from FlipAdamBox30minV1 import getMLFlipAdambySymbol, get_cffi_session
import telegram
from telegram.request import HTTPXRequest
import io
from PIL import Image
import requests
import config
import aiohttp
import time
from datetime import datetime, timedelta
import json

# 設定日誌
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

# 您的 Bot Token
TOKEN = "7749365105:AAE448qrt8TNTjgwG9CKg6GM1rx3Xr1uAUo"

# 需要合併查詢的CSV檔案
CSV_FILES = [
    'stock_name_hold_fundsonly_SortbyValue.csv',
    'stock_name_hold_stockonly_SortbyValue.csv',
    'stock_names_Ady.csv',
    'stock_names_coin.csv',
    'stock_names_ETF.csv',
    'stock_names_watch_index.csv',
]

# 頻率傳送任務管理
frequency_tasks = {}  # 用戶ID -> 任務資訊
task_counter = 0

# 訂閱管理
subscriptions = {}  # 用戶ID -> {symbol: interval_minutes}
subscription_tasks = {}  # 用戶ID -> {symbol: task_id}

# 用戶狀態管理
user_states = {}  # 用戶ID -> 當前狀態

# 分頁管理
user_pages = {}  # 用戶ID -> 當前頁面

def create_main_keyboard():
    """創建主鍵盤"""
    keyboard = [
        [KeyboardButton("📈 訂閱"), KeyboardButton("❌ 取消訂閱")],
        [KeyboardButton("📋 檢查訂閱"), KeyboardButton("❓ 幫助")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

def create_interval_keyboard():
    """創建間隔選擇鍵盤"""
    keyboard = [
        [KeyboardButton("5分鐘"), KeyboardButton("10分鐘")],
        [KeyboardButton("15分鐘"), KeyboardButton("30分鐘")],
        [KeyboardButton("🔙 返回主選單")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def create_cancel_keyboard():
    """創建取消鍵盤"""
    keyboard = [
        [KeyboardButton("🔙 返回主選單")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def create_unsubscribe_keyboard(user_subs):
    """創建取消訂閱的動態鍵盤"""
    keyboard = []
    row = []
    
    for i, (symbol, info) in enumerate(user_subs.items()):
        interval = info['interval_minutes']
        name = info.get('symbol_name', symbol)
        # 創建按鈕文字，包含標的代碼和間隔
        button_text = f"❌ {symbol} ({interval}分)"
        row.append(KeyboardButton(button_text))
        
        # 每行最多2個按鈕
        if len(row) == 2:
            keyboard.append(row)
            row = []
    
    # 添加最後一行（如果還有按鈕）
    if row:
        keyboard.append(row)
    
    # 添加返回按鈕
    keyboard.append([KeyboardButton("🔙 返回主選單")])
    
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)

def sanitize_filename(symbol):
    """處理檔案名稱中的特殊符號"""
    # 將特殊符號替換為底線
    return re.sub(r'[<>:"/\\|?*]', '_', symbol)

def load_stock_codes():
    stock_dict = {}
    name_to_symbol = {}  # 新增：名稱到代碼的映射
    for csv_file in CSV_FILES:
        try:
            df = pd.read_csv(csv_file)
            # 避免重複，後面的檔案會覆蓋前面的同名代碼
            stock_dict.update(dict(zip(df['Symbol'], df['Name'])))
            # 建立名稱到代碼的映射（不分大小寫）
            for symbol, name in zip(df['Symbol'], df['Name']):
                name_to_symbol[name.lower()] = symbol
        except Exception as e:
            logging.error(f"讀取 {csv_file} 時發生錯誤: {e}")
    return stock_dict, name_to_symbol

# 載入股票代碼資料
STOCK_CODES, NAME_TO_SYMBOL = load_stock_codes()

def find_matching_stocks(query):
    """搜尋匹配的股票代碼或名稱"""
    query = query.lower()
    matches = []
    
    # 搜尋代碼（不分大小寫）
    for symbol in STOCK_CODES.keys():
        if query in symbol.lower():
            matches.append((symbol, STOCK_CODES[symbol]))
    
    # 搜尋名稱
    for name, symbol in NAME_TO_SYMBOL.items():
        if query in name:
            if symbol not in [m[0] for m in matches]:  # 避免重複
                matches.append((symbol, STOCK_CODES[symbol]))
    
    return matches

def load_subscriptions():
    """載入訂閱資料"""
    global subscriptions
    try:
        if os.path.exists('subscriptions.json'):
            with open('subscriptions.json', 'r', encoding='utf-8') as f:
                subscriptions = json.load(f)
                # 確保用戶ID是整數
                subscriptions = {int(k): v for k, v in subscriptions.items()}
                logging.info(f"載入訂閱資料: {len(subscriptions)} 個用戶")
        else:
            # 如果檔案不存在，創建預設訂閱
            subscriptions = {}
            logging.info("訂閱檔案不存在，將創建預設訂閱")
    except Exception as e:
        logging.error(f"載入訂閱資料失敗: {e}")
        subscriptions = {}
    
    # 檢查是否有預設訂閱，如果沒有則添加
    default_user_id = 1057529499  # 你的用戶ID
    if default_user_id not in subscriptions:
        subscriptions[default_user_id] = {}
    
    # 添加預設訂閱
    default_subscriptions = {
        'BTC-USD': {
            'interval_minutes': 30,
            'symbol_name': 'Bitcoin USD 比特幣',
            'added_time': datetime.now().isoformat()
        },
        'ETH-USD': {
            'interval_minutes': 30,
            'symbol_name': 'Ethereum USD 以太幣',
            'added_time': datetime.now().isoformat()
        }
    }
    
    # 檢查並添加預設訂閱（如果不存在）
    added_defaults = []
    for symbol, info in default_subscriptions.items():
        if symbol not in subscriptions[default_user_id]:
            subscriptions[default_user_id][symbol] = info
            added_defaults.append(symbol)
    
    if added_defaults:
        logging.info(f"已添加預設訂閱: {', '.join(added_defaults)}")
        save_subscriptions()

def save_subscriptions():
    """保存訂閱資料"""
    try:
        with open('subscriptions.json', 'w', encoding='utf-8') as f:
            json.dump(subscriptions, f, ensure_ascii=False, indent=2)
        logging.info("訂閱資料已保存")
    except Exception as e:
        logging.error(f"保存訂閱資料失敗: {e}")

def add_subscription(user_id: int, symbol: str, interval_minutes: int, symbol_name: str = None):
    """新增訂閱"""
    if user_id not in subscriptions:
        subscriptions[user_id] = {}
    
    # 如果沒有提供symbol_name，則查表確認標的存在
    if symbol_name is None:
        matches = find_matching_stocks(symbol)
        if not matches:
            return False, f"查無此標的：{symbol}"
        yfinance_symbol, symbol_name = matches[0]
    else:
        yfinance_symbol = symbol
    
    subscriptions[user_id][yfinance_symbol] = {
        'interval_minutes': interval_minutes,
        'symbol_name': symbol_name,
        'added_time': datetime.now().isoformat()
    }
    save_subscriptions()
    
    # 啟動訂閱任務
    start_subscription_task(user_id, yfinance_symbol, interval_minutes)
    
    return True, f"已新增訂閱：{yfinance_symbol} ({symbol_name}) 每 {interval_minutes} 分鐘"

def remove_subscription(user_id: int, symbol: str):
    """刪除訂閱"""
    if user_id not in subscriptions:
        return False, "您沒有任何訂閱"
    
    # 查表確認標的存在
    matches = find_matching_stocks(symbol)
    if not matches:
        return False, f"查無此標的：{symbol}"
    
    yfinance_symbol, symbol_name = matches[0]
    if yfinance_symbol in subscriptions[user_id]:
        del subscriptions[user_id][yfinance_symbol]
        save_subscriptions()
        
        # 停止訂閱任務
        stop_subscription_task(user_id, yfinance_symbol)
        
        return True, f"已刪除訂閱：{yfinance_symbol} ({symbol_name})"
    else:
        return False, f"您沒有訂閱：{yfinance_symbol}"

def get_user_subscriptions(user_id: int):
    """取得用戶的訂閱列表"""
    if user_id not in subscriptions:
        return []
    return subscriptions[user_id]

def start_subscription_task(user_id: int, symbol: str, interval_minutes: int):
    """啟動訂閱任務"""
    global task_counter
    task_counter += 1
    task_id = f"sub_{task_counter}"
    
    if user_id not in subscription_tasks:
        subscription_tasks[user_id] = {}
    
    subscription_tasks[user_id][symbol] = task_id
    
    # 啟動異步任務
    asyncio.create_task(subscription_task_loop(user_id, task_id, symbol, interval_minutes))
    logging.info(f"啟動訂閱任務: 用戶 {user_id}, 標的 {symbol}, 間隔 {interval_minutes} 分鐘")

def stop_subscription_task(user_id: int, symbol: str):
    """停止訂閱任務"""
    if user_id in subscription_tasks and symbol in subscription_tasks[user_id]:
        del subscription_tasks[user_id][symbol]
        logging.info(f"停止訂閱任務: 用戶 {user_id}, 標的 {symbol}")

async def subscription_task_loop(user_id: int, task_id: str, symbol: str, interval_minutes: int):
    """訂閱任務循環"""
    while True:
        try:
            # 等待指定的間隔時間
            await asyncio.sleep(interval_minutes * 60)
            
            # 檢查訂閱是否還存在
            if (user_id not in subscriptions or 
                symbol not in subscriptions[user_id] or
                user_id not in subscription_tasks or
                symbol not in subscription_tasks[user_id]):
                logging.info(f"訂閱已取消: 用戶 {user_id}, 標的 {symbol}")
                break
            
            # 發送訂閱圖片
            await send_subscription_image(user_id, symbol)
            
            logging.info(f"訂閱任務: 已發送 {symbol} 圖片給用戶 {user_id}")
            
        except Exception as e:
            logging.error(f"訂閱任務發生錯誤: {e}")
            break

async def send_subscription_image(user_id: int, symbol: str):
    """發送訂閱圖片"""
    try:
        # 確保 figure 目錄存在
        if not os.path.exists('figure'):
            os.makedirs('figure')
        
        # 生成新的圖片
        session = get_cffi_session()
        image_path = getMLFlipAdambySymbol(symbol, session=session)
        
        if image_path and os.path.exists(image_path):
            # 發送圖片給指定用戶
            if await send_photo_to_user(user_id, image_path, f"{symbol} 的技術分析圖表 (訂閱更新)"):
                logging.info(f"訂閱: 成功發送 {symbol} 圖片給用戶 {user_id}")
            else:
                logging.error(f"訂閱: 發送 {symbol} 圖片給用戶 {user_id} 失敗")
        else:
            logging.error(f"訂閱: 無法生成 {symbol} 圖片")
            
    except Exception as e:
        logging.error(f"訂閱發送圖片時發生錯誤: {e}")

async def send_photo_to_user(user_id: int, photo_path: str, caption: str):
    """發送圖片給指定用戶"""
    try:
        result = await send_photo_with_aiohttp(TOKEN, user_id, photo_path, caption)
        if result.get("ok"):
            # 成功發送後刪除圖片檔案
            try:
                os.remove(photo_path)
                logging.info(f"成功刪除圖片檔案: {photo_path}")
            except Exception as e:
                logging.warning(f"刪除圖片檔案失敗: {photo_path}, 錯誤: {e}")
            return True
        else:
            logging.error(f"發送圖片給用戶 {user_id} 失敗: {result}")
            return False
    except Exception as e:
        logging.error(f"發送圖片給用戶 {user_id} 時發生錯誤: {e}")
        return False

async def start_all_subscriptions():
    """啟動所有訂閱任務，並先對每個訂閱項目執行一次繪圖並發送"""
    for user_id, user_subs in subscriptions.items():
        for symbol, info in user_subs.items():
            interval_minutes = info['interval_minutes']
            # 啟動訂閱任務前，先執行一次繪圖並發送
            try:
                session = get_cffi_session()
                image_path = getMLFlipAdambySymbol(symbol, session=session)
                if image_path and os.path.exists(image_path):
                    # 發送圖片給用戶
                    await send_photo_to_user(user_id, image_path, f"{symbol} 的技術分析圖表 (啟動自動推播)")
            except Exception as e:
                logging.error(f"啟動時預先繪圖/發送 {symbol} 失敗: {e}")
            start_subscription_task(user_id, symbol, interval_minutes)
    logging.info("已啟動所有訂閱任務，並預先繪圖與發送完成")

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /start 命令"""
    user_id = update.effective_user.id
    user_states[user_id] = 'main'  # 設定用戶狀態為主選單
    
    welcome_text = (
        "您好！我是 FlipAdam 通知機器人 🤖\n\n"
        "📊 功能特色：\n"
        "• 10天/20天30分鐘高解析度技術分析圖表\n"
        "• 支援股票、ETF、加密貨幣、基金、指數\n"
        "• 自動訂閱管理\n"
        "• 頻率傳送功能\n\n"
        "請使用下方按鈕或直接輸入股票代碼開始使用！"
    )
    
    await update.message.reply_text(
        welcome_text,
        reply_markup=create_main_keyboard()
    )

async def handle_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理按鈕點擊"""
    user_id = update.effective_user.id
    user_message = update.message.text
    
    if user_message == "📈 訂閱":
        await handle_subscribe_button(update, context)
    elif user_message == "❌ 取消訂閱":
        await handle_unsubscribe_button(update, context)
    elif user_message == "📋 檢查訂閱":
        await handle_check_subscriptions_button(update, context)
    elif user_message == "❓ 幫助":
        await handle_help_button(update, context)
    elif user_message == "🔙 返回主選單":
        await handle_back_to_main(update, context)
    elif user_message in ["5分鐘", "10分鐘", "15分鐘", "30分鐘"]:
        # 檢查用戶狀態
        current_state = user_states.get(user_id, 'main')
        if current_state == 'waiting_interval':
            await handle_interval_selection(update, context)
        else:
            await update.message.reply_text(
                "❌ 請先選擇標的",
                reply_markup=create_main_keyboard()
            )
            user_states[user_id] = 'main'
    elif user_message == "⬅️ 上一頁":
        await handle_previous_page(update, context)
    elif user_message == "➡️ 下一頁":
        await handle_next_page(update, context)
    elif user_message.startswith("📄"):  # 頁面資訊按鈕，忽略
        return
    else:
        # 檢查是否為狀態相關的輸入
        current_state = user_states.get(user_id, 'main')
        if current_state == 'waiting_symbol':
            await handle_symbol_input(update, context)
        elif current_state == 'waiting_stock_selection':
            await handle_stock_selection(update, context)
        elif current_state == 'waiting_unsubscribe_selection':
            await handle_unsubscribe_selection(update, context)
        else:
            # 如果不是按鈕，當作一般訊息處理
            await handle_message(update, context)

async def handle_subscribe_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理訂閱按鈕"""
    user_id = update.effective_user.id
    user_states[user_id] = 'waiting_stock_selection'
    user_pages[user_id] = 0  # 重置到第一頁
    
    # 顯示標的選擇鍵盤
    keyboard = create_stock_selection_keyboard(page=0)
    
    await update.message.reply_text(
        "📈 請從以下清單中選擇要訂閱的標的：\n"
        "使用 ⬅️ 上一頁 和 ➡️ 下一頁 瀏覽更多選項",
        reply_markup=keyboard
    )

async def handle_unsubscribe_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理取消訂閱按鈕"""
    user_id = update.effective_user.id
    user_states[user_id] = 'waiting_unsubscribe_selection'
    
    # 先顯示用戶當前的訂閱
    user_subs = get_user_subscriptions(user_id)
    if not user_subs:
        await update.message.reply_text(
            "您目前沒有任何訂閱標的。",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 創建動態取消訂閱鍵盤
    unsubscribe_keyboard = create_unsubscribe_keyboard(user_subs)
    
    sub_list = []
    for symbol, info in user_subs.items():
        interval = info['interval_minutes']
        name = info.get('symbol_name', symbol)
        sub_list.append(f"📈 {symbol} ({name}) - {interval}分鐘")
    
    message = "請點選要取消訂閱的標的：\n\n" + "\n".join(sub_list)
    
    await update.message.reply_text(
        message,
        reply_markup=unsubscribe_keyboard
    )

async def handle_check_subscriptions_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理檢查訂閱按鈕"""
    user_id = update.effective_user.id
    user_subs = get_user_subscriptions(user_id)
    
    if not user_subs:
        await update.message.reply_text(
            "您目前沒有任何訂閱標的。\n使用「📈 訂閱」按鈕來新增訂閱！",
            reply_markup=create_main_keyboard()
        )
        return
    
    # 格式化訂閱列表
    sub_list = []
    for symbol, info in user_subs.items():
        interval = info['interval_minutes']
        name = info.get('symbol_name', symbol)
        added_time = info.get('added_time', '未知')
        # 轉換時間格式
        try:
            dt_obj = datetime.fromisoformat(added_time)
            added_str = dt_obj.strftime('%Y-%m-%d %H:%M')
        except:
            added_str = added_time
        
        sub_list.append(f"📈 {symbol} ({name})\n⏰ 間隔：{interval}分鐘\n📅 新增：{added_str}")
    
    message = f"您的訂閱標的（共 {len(user_subs)} 個）：\n\n" + "\n\n".join(sub_list)
    
    await update.message.reply_text(
        message,
        reply_markup=create_main_keyboard()
    )

async def handle_help_button(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理幫助按鈕"""
    help_text = """
🤖 FlipAdam 通知機器人使用說明

📊 主要功能：
• 技術分析圖表生成
• 自動訂閱管理
• 頻率傳送功能

📈 訂閱功能：
• 支援 5、10、15、30 分鐘間隔
• 自動發送最新技術分析圖表
• 可同時管理多個標的

📋 支援標的：
• 股票：AAPL、TSM、台積電
• 加密貨幣：BTC、ETH、比特幣
• ETF、基金、指數

💡 使用方式：
1. 點擊「📈 訂閱」新增訂閱
2. 點擊「📋 檢查訂閱」查看狀態
3. 點擊「❌ 取消訂閱」移除訂閱
4. 直接輸入股票代碼查詢

🔄 頻率傳送：
輸入格式：股票代碼,分鐘數
例如：BTC,30 或 AAPL,60

📞 命令：
/start - 重新開始
/help - 顯示幫助
/stop - 停止頻率傳送
/status - 查看狀態
    """
    
    await update.message.reply_text(
        help_text,
        reply_markup=create_main_keyboard()
    )

async def handle_back_to_main(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理返回主選單"""
    user_id = update.effective_user.id
    user_states[user_id] = 'main'
    
    await update.message.reply_text(
        "已返回主選單，請選擇功能：",
        reply_markup=create_main_keyboard()
    )

async def handle_previous_page(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理上一頁"""
    user_id = update.effective_user.id
    current_page = user_pages.get(user_id, 0)
    
    if current_page > 0:
        user_pages[user_id] = current_page - 1
        keyboard = create_stock_selection_keyboard(page=current_page - 1)
        
        await update.message.reply_text(
            f"📄 第 {current_page} 頁",
            reply_markup=keyboard
        )

async def handle_next_page(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理下一頁"""
    user_id = update.effective_user.id
    current_page = user_pages.get(user_id, 0)
    
    stock_list = get_stock_list_from_csv()
    total_pages = (len(stock_list) + 7) // 8  # 每頁8個項目
    
    if current_page < total_pages - 1:
        user_pages[user_id] = current_page + 1
        keyboard = create_stock_selection_keyboard(page=current_page + 1)
        
        await update.message.reply_text(
            f"📄 第 {current_page + 2} 頁",
            reply_markup=keyboard
        )

async def handle_stock_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理標的選擇"""
    user_id = update.effective_user.id
    user_message = update.message.text
    
    # 解析按鈕文字，格式為 "SYMBOL\nNAME"
    lines = user_message.split('\n')
    if len(lines) >= 2:
        symbol = lines[0].strip()
        name = lines[1].strip()
        
        # 儲存選擇的標的
        context.user_data['selected_symbol'] = symbol
        context.user_data['selected_symbol_name'] = name
        
        await update.message.reply_text(
            f"已選擇：{symbol} ({name})\n\n請選擇訂閱間隔：",
            reply_markup=create_interval_keyboard()
        )
        
        # 更新狀態
        user_states[user_id] = 'waiting_interval'
    else:
        await update.message.reply_text(
            "❌ 選擇無效，請重新選擇標的",
            reply_markup=create_stock_selection_keyboard(page=user_pages.get(user_id, 0))
        )

async def handle_symbol_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理標的代碼輸入"""
    user_id = update.effective_user.id
    symbol_input = update.message.text.strip().upper()
    
    # 查表確認標的存在
    matches = find_matching_stocks(symbol_input)
    if not matches:
        await update.message.reply_text(
            f"❌ 查無此標的：{symbol_input}\n請確認輸入或嘗試其他代碼",
            reply_markup=create_cancel_keyboard()
        )
        return
    
    yfinance_symbol, symbol_name = matches[0]
    
    # 儲存選擇的標的
    context.user_data['selected_symbol'] = yfinance_symbol
    context.user_data['selected_symbol_name'] = symbol_name
    
    await update.message.reply_text(
        f"已選擇：{yfinance_symbol} ({symbol_name})\n\n請選擇訂閱間隔：",
        reply_markup=create_interval_keyboard()
    )

async def handle_interval_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理間隔選擇"""
    user_id = update.effective_user.id
    interval_text = update.message.text
    
    # 檢查用戶狀態
    current_state = user_states.get(user_id, 'main')
    if current_state != 'waiting_interval':
        await update.message.reply_text(
            "❌ 請先選擇標的",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 解析間隔時間
    interval_map = {
        "5分鐘": 5,
        "10分鐘": 10,
        "15分鐘": 15,
        "30分鐘": 30
    }
    
    interval_minutes = interval_map.get(interval_text, 30)
    symbol = context.user_data.get('selected_symbol')
    symbol_name = context.user_data.get('selected_symbol_name')
    
    if not symbol:
        await update.message.reply_text(
            "❌ 發生錯誤，請重新開始訂閱流程",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'
        return
    
    # 新增訂閱
    success, message = add_subscription(user_id, symbol, interval_minutes, symbol_name)
    
    await update.message.reply_text(
        message,
        reply_markup=create_main_keyboard()
    )
    
    # 清理用戶數據
    context.user_data.clear()
    user_states[user_id] = 'main'

async def handle_unsubscribe_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理取消訂閱的選擇"""
    user_id = update.effective_user.id
    selection = update.message.text
    
    # 檢查是否為返回按鈕
    if selection == "🔙 返回主選單":
        await handle_back_to_main(update, context)
        return
    
    # 解析選擇的標的（格式：❌ SYMBOL (間隔分)）
    if selection.startswith("❌ "):
        # 提取標的代碼
        symbol_part = selection[3:]  # 移除 "❌ "
        symbol = symbol_part.split(" (")[0]  # 提取標的代碼部分
        
        # 執行取消訂閱
        success, message = remove_subscription(user_id, symbol)
        
        await update.message.reply_text(
            message,
            reply_markup=create_main_keyboard()
        )
        
        user_states[user_id] = 'main'
    else:
        await update.message.reply_text(
            "❌ 無效的選擇，請重新選擇",
            reply_markup=create_main_keyboard()
        )
        user_states[user_id] = 'main'

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /help 命令"""
    help_text = """
可用命令：
/start - 開始使用機器人
/help - 顯示此幫助訊息
/notify <訊息> - 發送通知訊息
/stop - 停止頻率傳送
/status - 查看頻率傳送狀態

📈 訂閱管理：
/subscribe <標的代碼> [間隔分鐘] - 新增訂閱標的
/unsubscribe <標的代碼> - 刪除訂閱標的
/subscriptions - 查看所有訂閱標的

📊 單次查詢：
直接輸入股票代碼或名稱，我會幫您查詢對應的股票資訊並發送10天30分鐘高解析度技術分析圖表。
支援模糊搜尋，例如：
- 輸入 "台積電" 或 "tsm" 都可以找到台積電
- 輸入 "科技" 會列出所有包含科技的公司

🔄 頻率傳送功能：
輸入格式：股票代碼,分鐘數
例如：
- "TSM,30" - 每30分鐘傳送一次台積電的技術分析圖表
- "BTC-USD,60" - 每60分鐘傳送一次比特幣的技術分析圖表
- "AAPL,120" - 每2小時傳送一次蘋果的技術分析圖表

📋 訂閱間隔選項：
- 5分鐘 (5)
- 10分鐘 (10)
- 15分鐘 (15)
- 30分鐘 (30) - 預設

注意：
- 間隔時間範圍：1-1440分鐘（最大24小時）
- 每個用戶同時只能運行一個頻率傳送任務
- 訂閱功能可以同時管理多個標的
- 使用 /stop 停止頻率傳送
- 使用 /status 查看當前狀態
- 圖片為高解析度格式，適合詳細查看和列印
    """
    await update.message.reply_text(help_text)

async def notify(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /notify 命令"""
    if not context.args:
        await update.message.reply_text('請提供要發送的通知訊息。\n'
                                      '例如：/notify 這是一則測試通知')
        return
    
    message = ' '.join(context.args)
    await update.message.reply_text(f'已發送通知：{message}')

async def compress_image_to_jpeg(image_path, max_size_kb=100):
    """壓縮圖片到指定 KB 以內，回傳 BytesIO（目前不使用，改為發送原始高解析度圖片）"""
    with Image.open(image_path) as img:
        if img.mode in ("RGBA", "LA"):
            background = Image.new("RGB", img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1])
            img = background
        quality = 95
        output = io.BytesIO()
        while quality > 10:
            output.seek(0)
            output.truncate()
            img.save(output, format="JPEG", quality=quality)
            size_kb = output.tell() / 1024
            if size_kb <= max_size_kb:
                break
            quality -= 5
        output.seek(0)
        return output

async def send_photo_with_aiohttp(token, chat_id, photo_path, caption):
    url = f"https://api.telegram.org/bot{token}/sendPhoto"
    # 不壓縮圖片，直接發送原始高解析度圖片
    with open(photo_path, 'rb') as f:
        photo_data = f.read()
    data = aiohttp.FormData()
    data.add_field("chat_id", str(chat_id))
    data.add_field("caption", caption)
    data.add_field("photo", photo_data, filename="chart.png", content_type="image/png")
    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        async with session.post(url, data=data) as resp:
            result = await resp.json()
            return result

async def send_photo_with_retry(update, photo_path, caption, max_retries=3):
    for attempt in range(max_retries):
        try:
            logging.info(f"準備發送原始高解析度圖片: {photo_path}")
            result = await send_photo_with_aiohttp(
                TOKEN,
                update.effective_chat.id,
                photo_path,
                caption
            )
            logging.info(f"發送原始高解析度圖片結果: {result}")
            if result.get("ok"):
                # 成功發送後刪除圖片檔案
                try:
                    os.remove(photo_path)
                    logging.info(f"成功刪除圖片檔案: {photo_path}")
                except Exception as e:
                    logging.warning(f"刪除圖片檔案失敗: {photo_path}, 錯誤: {e}")
                return True
            else:
                await update.message.reply_text(f"發送原始高解析度圖片失敗: {result}")
        except Exception as e:
            logging.error(f"發送原始高解析度圖片時發生錯誤: {e}", exc_info=True)
            await update.message.reply_text(f"發送原始高解析度圖片時發生錯誤: {e}")
            raise
    return False

def cleanup_old_images():
    """清理 figure 目錄中的舊圖片檔案"""
    try:
        if os.path.exists('figure'):
            current_time = time.time()
            for filename in os.listdir('figure'):
                if filename.endswith('.png'):
                    file_path = os.path.join('figure', filename)
                    # 刪除超過1小時的圖片檔案
                    if current_time - os.path.getmtime(file_path) > 3600:  # 1小時 = 3600秒
                        try:
                            os.remove(file_path)
                            logging.info(f"清理舊圖片檔案: {filename}")
                        except Exception as e:
                            logging.warning(f"清理圖片檔案失敗: {filename}, 錯誤: {e}")
    except Exception as e:
        logging.error(f"清理圖片檔案時發生錯誤: {e}")

async def start_frequency_task(update: Update, symbol_input: str, interval_minutes: int):
    """啟動頻率傳送任務（強制經過CSV查表）"""
    global task_counter
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name

    # 強制查表
    matches = find_matching_stocks(symbol_input)
    if not matches:
        await update.message.reply_text(f"❌ 查無此標的：{symbol_input}，請確認輸入")
        return
    
    # 使用查到的 yfinance 代碼
    yfinance_symbol, symbol_name = matches[0]
    logging.info(f"查表結果：{symbol_input} -> {yfinance_symbol} ({symbol_name})")

    # 停止現有的任務（如果有的話）- 靜默停止，不顯示訊息
    if user_id in frequency_tasks:
        frequency_tasks[user_id]['is_running'] = False
        del frequency_tasks[user_id]
        logging.info(f"用戶 {user_name} 停止舊的頻率傳送任務")

    # 創建新任務
    task_counter += 1
    task_id = f"task_{task_counter}"

    frequency_tasks[user_id] = {
        'task_id': task_id,
        'symbol': yfinance_symbol,  # 使用查到的 yfinance 代碼
        'interval_minutes': interval_minutes,
        'start_time': datetime.now(),
        'is_running': True
    }

    logging.info(f"用戶 {user_name} 啟動頻率傳送任務: {yfinance_symbol} 每 {interval_minutes} 分鐘")

    # 立即發送第一次圖片
    await send_frequency_image(update, yfinance_symbol)

    # 啟動定時任務
    asyncio.create_task(frequency_task_loop(update, task_id, yfinance_symbol, interval_minutes))

    await update.message.reply_text(
        f"✅ 已啟動頻率傳送任務\n"
        f"📈 股票代碼: {yfinance_symbol} ({symbol_name})\n"
        f"⏰ 傳送間隔: 每 {interval_minutes} 分鐘\n"
        f"🔄 下次傳送: {interval_minutes} 分鐘後\n\n"
        f"使用 /stop 停止頻率傳送"
    )

async def stop_frequency_task(update: Update):
    """停止頻率傳送任務"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name
    
    if user_id in frequency_tasks:
        frequency_tasks[user_id]['is_running'] = False
        del frequency_tasks[user_id]
        logging.info(f"用戶 {user_name} 停止頻率傳送任務")
        await update.message.reply_text("✅ 已停止頻率傳送任務")
    else:
        await update.message.reply_text("❌ 沒有正在運行的頻率傳送任務")

async def frequency_task_loop(update: Update, task_id: str, symbol: str, interval_minutes: int):
    """頻率傳送任務循環"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name
    
    while True:
        try:
            # 等待指定的間隔時間
            await asyncio.sleep(interval_minutes * 60)
            
            # 檢查任務是否還在運行
            if user_id not in frequency_tasks or not frequency_tasks[user_id]['is_running']:
                logging.info(f"用戶 {user_name} 的頻率傳送任務已停止")
                break
            
            # 發送圖片
            await send_frequency_image(update, symbol)
            
            logging.info(f"用戶 {user_name} 的頻率傳送任務: 已發送 {symbol} 圖片")
            
        except Exception as e:
            logging.error(f"頻率傳送任務發生錯誤: {e}")
            break

async def send_frequency_image(update: Update, yfinance_symbol: str):
    """發送頻率傳送圖片（使用查到的 yfinance 代碼）"""
    try:
        # 確保 figure 目錄存在
        if not os.path.exists('figure'):
            os.makedirs('figure')
        
        # 生成新的圖片（使用 yfinance 代碼）
        session = get_cffi_session()
        image_path = getMLFlipAdambySymbol(yfinance_symbol, session=session)
        
        if image_path and os.path.exists(image_path):
            # 發送圖片
            if await send_photo_with_retry(update, image_path, f"{yfinance_symbol} 的技術分析圖表 (自動更新)"):
                logging.info(f"頻率傳送: 成功發送 {yfinance_symbol} 圖片")
            else:
                await update.message.reply_text(f"❌ 頻率傳送: 發送 {yfinance_symbol} 圖片失敗")
        else:
            await update.message.reply_text(f"❌ 頻率傳送: 無法生成 {yfinance_symbol} 圖片")
            
    except Exception as e:
        logging.error(f"頻率傳送發送圖片時發生錯誤: {e}")
        await update.message.reply_text(f"❌ 頻率傳送發生錯誤: {str(e)}")

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理一般訊息（強制經過CSV查表）"""
    try:
        # 清理舊圖片檔案
        cleanup_old_images()
        
        user_message = update.message.text.strip()
        user = update.effective_user
        user_name = user.first_name
        logging.info(f"收到來自 {user_name} 的訊息: {user_message}")

        # 檢查是否為頻率傳送指令 (格式: 股票代碼,分鐘數)
        if ',' in user_message:
            parts = user_message.split(',')
            if len(parts) == 2:
                symbol_input = parts[0].strip().upper()
                try:
                    interval_minutes = int(parts[1].strip())
                    if interval_minutes > 0 and interval_minutes <= 1440:  # 最大24小時
                        # 強制查表
                        matches = find_matching_stocks(symbol_input)
                        if matches:
                            yfinance_symbol, symbol_name = matches[0]
                            await update.message.reply_text(f"正在啟動頻率傳送任務...\n股票: {yfinance_symbol} ({symbol_name})")
                            await start_frequency_task(update, symbol_input, interval_minutes)
                            return
                        else:
                            await update.message.reply_text(f"❌ 查無此標的：{symbol_input}，請確認輸入")
                            return
                    else:
                        await update.message.reply_text("❌ 間隔時間必須在 1-1440 分鐘之間")
                        return
                except ValueError:
                    await update.message.reply_text("❌ 間隔時間必須是數字")
                    return

        # 搜尋匹配的股票（強制查表）
        matches = find_matching_stocks(user_message)
        
        if matches:
            for yfinance_symbol, name in matches[:10]:  # 限制最多10個
                response = f"您輸入的是股票代碼：{yfinance_symbol}\n對應的股票名稱是：{name}"
                await update.message.reply_text(response)
                
                # 確保 figure 目錄存在
                if not os.path.exists('figure'):
                    os.makedirs('figure')
                    logging.info("已創建 figure 目錄")
                
                # 重新產生圖片（使用 yfinance 代碼）
                session = get_cffi_session()
                image_path = getMLFlipAdambySymbol(yfinance_symbol, session=session)
                
                if image_path and os.path.exists(image_path):
                    if await send_photo_with_retry(update, image_path, f"{yfinance_symbol} 的技術分析圖表"):
                        logging.info(f"成功發送 {yfinance_symbol} 的技術分析圖表")
                    else:
                        await update.message.reply_text("發送圖片失敗，請稍後再試。")
                else:
                    await update.message.reply_text(f"❌ 無法生成 {yfinance_symbol} 的技術分析圖表")
            return
        else:
            await update.message.reply_text(f"❌ 查無此標的：{user_message}，請確認輸入")
    except Exception as e:
        logging.error(f"處理訊息時發生錯誤: {e}", exc_info=True)
        await update.message.reply_text(f"❌ 發生錯誤: {str(e)}")

async def stop_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /stop 命令 - 停止頻率傳送"""
    await stop_frequency_task(update)

async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /status 命令 - 查看頻率傳送狀態"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name
    
    if user_id in frequency_tasks:
        task = frequency_tasks[user_id]
        start_time = task['start_time']
        elapsed_time = datetime.now() - start_time
        
        status_text = (
            f"📊 頻率傳送狀態\n"
            f"📈 股票代碼: {task['symbol']}\n"
            f"⏰ 傳送間隔: 每 {task['interval_minutes']} 分鐘\n"
            f"🕐 開始時間: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"⏱️ 運行時間: {str(elapsed_time).split('.')[0]}\n"
            f"🔄 狀態: 運行中"
        )
        await update.message.reply_text(status_text)
    else:
        await update.message.reply_text("❌ 沒有正在運行的頻率傳送任務")

async def subscribe_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /subscribe 命令"""
    if not update or not update.message:
        logging.error("subscribe_command: update 或 update.message 為 None")
        return
    
    if not context.args:
        await update.message.reply_text(
            "請提供要訂閱的標的代碼。\n"
            "格式：/subscribe <標的代碼> [間隔分鐘]\n"
            "例如：/subscribe BTC 30\n"
            "間隔選項：5, 10, 15, 30分鐘（預設30分鐘）"
        )
        return
    
    symbol = context.args[0].strip().upper()
    interval_minutes = 30  # 預設30分鐘
    
    if len(context.args) > 1:
        try:
            interval_minutes = int(context.args[1])
            if interval_minutes not in [5, 10, 15, 30]:
                await update.message.reply_text("❌ 間隔時間必須是 5, 10, 15, 或 30 分鐘")
                return
        except ValueError:
            await update.message.reply_text("❌ 間隔時間必須是數字")
            return
    
    user_id = update.effective_user.id
    success, message = add_subscription(user_id, symbol, interval_minutes)
    await update.message.reply_text(message)

async def unsubscribe_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /unsubscribe 命令"""
    if not update or not update.message:
        logging.error("unsubscribe_command: update 或 update.message 為 None")
        return
    
    if not context.args:
        await update.message.reply_text(
            "請提供要取消訂閱的標的代碼。\n"
            "格式：/unsubscribe <標的代碼>\n"
            "例如：/unsubscribe BTC"
        )
        return
    
    symbol = context.args[0].strip().upper()
    user_id = update.effective_user.id
    success, message = remove_subscription(user_id, symbol)
    await update.message.reply_text(message)

async def subscriptions_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /subscriptions 命令"""
    if not update or not update.message:
        logging.error("subscriptions_command: update 或 update.message 為 None")
        return
    
    user_id = update.effective_user.id
    user_subs = get_user_subscriptions(user_id)
    
    if not user_subs:
        await update.message.reply_text("您目前沒有任何訂閱標的。\n使用 /subscribe <標的代碼> 來新增訂閱")
        return
    
    # 格式化訂閱列表
    sub_list = []
    for symbol, info in user_subs.items():
        interval = info['interval_minutes']
        name = info.get('symbol_name', symbol)
        added_time = info.get('added_time', '未知')
        # 轉換時間格式
        try:
            dt_obj = datetime.fromisoformat(added_time)
            added_str = dt_obj.strftime('%Y-%m-%d %H:%M')
        except:
            added_str = added_time
        
        sub_list.append(f"📈 {symbol} ({name})\n⏰ 間隔：{interval}分鐘\n📅 新增：{added_str}")
    
    message = f"您的訂閱標的（共 {len(user_subs)} 個）：\n\n" + "\n\n".join(sub_list)
    message += "\n\n使用 /unsubscribe <標的代碼> 來取消訂閱"
    
    await update.message.reply_text(message)

def get_stock_list_from_csv():
    """從stock_name_hold_stockonly_SortbyValue.csv取得標的清單"""
    try:
        df = pd.read_csv('stock_name_hold_stockonly_SortbyValue.csv')
        stock_list = []
        for _, row in df.iterrows():
            stock_list.append({
                'symbol': row['Symbol'],
                'name': row['Name']
            })
        return stock_list
    except Exception as e:
        logging.error(f"讀取stock_name_hold_stockonly_SortbyValue.csv失敗: {e}")
        return []

def create_stock_selection_keyboard(page=0, items_per_page=8):
    """創建標的選擇鍵盤，每頁8個選項"""
    stock_list = get_stock_list_from_csv()
    total_items = len(stock_list)
    total_pages = (total_items + items_per_page - 1) // items_per_page
    
    keyboard = []
    
    # 計算當前頁面的項目
    start_idx = page * items_per_page
    end_idx = min(start_idx + items_per_page, total_items)
    
    # 添加標的按鈕（每行2個）
    for i in range(start_idx, end_idx, 2):
        row = []
        stock1 = stock_list[i]
        button1_text = f"{stock1['symbol']}\n{stock1['name']}"
        row.append(KeyboardButton(button1_text))
        
        # 如果有第二個項目
        if i + 1 < end_idx:
            stock2 = stock_list[i + 1]
            button2_text = f"{stock2['symbol']}\n{stock2['name']}"
            row.append(KeyboardButton(button2_text))
        
        keyboard.append(row)
    
    # 添加導航按鈕
    nav_row = []
    if page > 0:
        nav_row.append(KeyboardButton("⬅️ 上一頁"))
    if page < total_pages - 1:
        nav_row.append(KeyboardButton("➡️ 下一頁"))
    
    if nav_row:
        keyboard.append(nav_row)
    
    # 添加頁面資訊和返回按鈕
    page_info = f"📄 {page + 1}/{total_pages}"
    keyboard.append([KeyboardButton(page_info)])
    keyboard.append([KeyboardButton("🔙 返回主選單")])
    
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

def main():
    """主函數"""
    # 設定日誌
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )
    
    # 載入訂閱資料
    load_subscriptions()
    
    # 創建應用程式
    application = Application.builder().token(TOKEN).build()
    
    # 註冊命令處理器
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("notify", notify))
    application.add_handler(CommandHandler("stop", stop_command))
    application.add_handler(CommandHandler("status", status_command))
    application.add_handler(CommandHandler("subscribe", subscribe_command))
    application.add_handler(CommandHandler("unsubscribe", unsubscribe_command))
    application.add_handler(CommandHandler("subscriptions", subscriptions_command))
    
    # 註冊按鈕和訊息處理器
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_button))
    
    # 啟動機器人
    logging.info("啟動 FlipAdam 30分鐘通知機器人...")
    
    # 使用 post_init 來啟動訂閱任務
    async def post_init(application):
        await start_all_subscriptions()
    
    application.post_init = post_init
    application.run_polling()

if __name__ == '__main__':
    if sys.platform.startswith('win'):
        # Windows 特定設定
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    main() 