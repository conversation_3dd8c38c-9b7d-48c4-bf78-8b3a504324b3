"""
插件註冊表
管理所有已載入的插件
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from collections import defaultdict

from .base_plugin import BasePlugin


class PluginRegistry:
    """
    插件註冊表
    提供插件的註冊、管理和查詢功能
    """
    
    def __init__(self):
        """初始化插件註冊表"""
        self.logger = logging.getLogger("PluginRegistry")
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_by_name: Dict[str, BasePlugin] = {}
        self.plugin_dependencies: Dict[str, List[str]] = {}
        self.is_initialized = False
    
    async def initialize(self):
        """初始化插件註冊表"""
        try:
            self.logger.info("初始化插件註冊表...")
            self.is_initialized = True
            self.logger.info("插件註冊表初始化完成")
        except Exception as e:
            self.logger.error(f"插件註冊表初始化失敗: {e}")
            raise
    
    def register_plugin(self, plugin: BasePlugin) -> bool:
        """
        註冊插件
        
        Args:
            plugin: 插件實例
            
        Returns:
            註冊是否成功
        """
        try:
            self.logger.info(f"註冊插件: {plugin.name}")
            
            # 檢查插件名稱是否已存在
            if plugin.name in self.plugins:
                self.logger.warning(f"插件 {plugin.name} 已存在，將被覆蓋")
            
            # 註冊插件
            self.plugins[plugin.name] = plugin
            self.plugin_by_name[plugin.name] = plugin
            
            # 記錄依賴關係
            self.plugin_dependencies[plugin.name] = plugin.dependencies.copy()
            
            self.logger.info(f"插件 {plugin.name} 註冊成功")
            return True
            
        except Exception as e:
            self.logger.error(f"註冊插件 {plugin.name} 失敗: {e}")
            return False
    
    def unregister_plugin(self, plugin_name: str) -> bool:
        """
        取消註冊插件
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            取消註冊是否成功
        """
        try:
            self.logger.info(f"取消註冊插件: {plugin_name}")
            
            if plugin_name not in self.plugins:
                self.logger.warning(f"插件 {plugin_name} 不存在")
                return False
            
            # 移除插件
            del self.plugins[plugin_name]
            del self.plugin_by_name[plugin_name]
            
            # 移除依賴關係
            if plugin_name in self.plugin_dependencies:
                del self.plugin_dependencies[plugin_name]
            
            self.logger.info(f"插件 {plugin_name} 取消註冊成功")
            return True
            
        except Exception as e:
            self.logger.error(f"取消註冊插件 {plugin_name} 失敗: {e}")
            return False
    
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """
        獲取插件
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            插件實例
        """
        return self.plugins.get(plugin_name)
    
    def list_plugins(self) -> List[str]:
        """
        列出所有插件名稱
        
        Returns:
            插件名稱列表
        """
        return list(self.plugins.keys())
    
    def list_plugins_by_status(self, status: str = None) -> List[str]:
        """
        根據狀態列出插件
        
        Args:
            status: 狀態（'initialized', 'running', 'enabled'）
            
        Returns:
            插件名稱列表
        """
        if status is None:
            return self.list_plugins()
        
        result = []
        for name, plugin in self.plugins.items():
            if status == 'initialized' and plugin.is_initialized:
                result.append(name)
            elif status == 'running' and plugin.is_running:
                result.append(name)
            elif status == 'enabled' and plugin.is_enabled:
                result.append(name)
        
        return result
    
    def get_plugin_dependencies(self, plugin_name: str) -> List[str]:
        """
        獲取插件依賴
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            依賴列表
        """
        return self.plugin_dependencies.get(plugin_name, []).copy()
    
    def get_plugin_dependents(self, plugin_name: str) -> List[str]:
        """
        獲取依賴此插件的插件列表
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            依賴此插件的插件列表
        """
        dependents = []
        for name, dependencies in self.plugin_dependencies.items():
            if plugin_name in dependencies:
                dependents.append(name)
        
        return dependents
    
    def check_plugin_dependencies(self, plugin_name: str) -> bool:
        """
        檢查插件依賴是否滿足
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            依賴是否滿足
        """
        if plugin_name not in self.plugin_dependencies:
            return True
        
        dependencies = self.plugin_dependencies[plugin_name]
        available_plugins = self.list_plugins()
        
        for dependency in dependencies:
            if dependency not in available_plugins:
                self.logger.warning(f"插件 {plugin_name} 缺少依賴: {dependency}")
                return False
        
        return True
    
    def get_plugin_status(self, plugin_name: str) -> Dict[str, Any]:
        """
        獲取插件狀態
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            狀態字典
        """
        plugin = self.get_plugin(plugin_name)
        if not plugin:
            return {"error": f"插件 {plugin_name} 不存在"}
        
        return plugin.get_status()
    
    def get_all_plugin_status(self) -> Dict[str, Dict[str, Any]]:
        """
        獲取所有插件狀態
        
        Returns:
            所有插件狀態字典
        """
        result = {}
        for name in self.list_plugins():
            result[name] = self.get_plugin_status(name)
        
        return result
    
    def get_plugin_stats(self) -> Dict[str, Any]:
        """
        獲取插件統計信息
        
        Returns:
            統計信息字典
        """
        total_plugins = len(self.plugins)
        initialized_plugins = len(self.list_plugins_by_status('initialized'))
        running_plugins = len(self.list_plugins_by_status('running'))
        enabled_plugins = len(self.list_plugins_by_status('enabled'))
        
        return {
            "total_plugins": total_plugins,
            "initialized_plugins": initialized_plugins,
            "running_plugins": running_plugins,
            "enabled_plugins": enabled_plugins,
            "plugin_names": self.list_plugins()
        }
    
    def find_plugins_by_pattern(self, pattern: str) -> List[str]:
        """
        根據模式查找插件
        
        Args:
            pattern: 查找模式
            
        Returns:
            匹配的插件名稱列表
        """
        result = []
        for name in self.list_plugins():
            if pattern.lower() in name.lower():
                result.append(name)
        
        return result
    
    def get_plugins_by_author(self, author: str) -> List[str]:
        """
        根據作者查找插件
        
        Args:
            author: 作者名稱
            
        Returns:
            匹配的插件名稱列表
        """
        result = []
        for name, plugin in self.plugins.items():
            if plugin.author.lower() == author.lower():
                result.append(name)
        
        return result
    
    def get_plugins_by_version(self, version: str) -> List[str]:
        """
        根據版本查找插件
        
        Args:
            version: 版本號
            
        Returns:
            匹配的插件名稱列表
        """
        result = []
        for name, plugin in self.plugins.items():
            if plugin.version == version:
                result.append(name)
        
        return result
    
    def update_plugin(self, plugin_name: str, plugin: BasePlugin) -> bool:
        """
        更新插件
        
        Args:
            plugin_name: 插件名稱
            plugin: 新的插件實例
            
        Returns:
            更新是否成功
        """
        try:
            self.logger.info(f"更新插件: {plugin_name}")
            
            # 檢查插件是否存在
            if plugin_name not in self.plugins:
                self.logger.warning(f"插件 {plugin_name} 不存在，將進行註冊")
                return self.register_plugin(plugin)
            
            # 更新插件
            self.plugins[plugin_name] = plugin
            self.plugin_by_name[plugin_name] = plugin
            
            # 更新依賴關係
            self.plugin_dependencies[plugin_name] = plugin.dependencies.copy()
            
            self.logger.info(f"插件 {plugin_name} 更新成功")
            return True
            
        except Exception as e:
            self.logger.error(f"更新插件 {plugin_name} 失敗: {e}")
            return False
    
    def clear_plugins(self):
        """清空所有插件"""
        try:
            self.logger.info("清空所有插件...")
            
            self.plugins.clear()
            self.plugin_by_name.clear()
            self.plugin_dependencies.clear()
            
            self.logger.info("所有插件已清空")
            
        except Exception as e:
            self.logger.error(f"清空插件失敗: {e}")
    
    def is_plugin_registered(self, plugin_name: str) -> bool:
        """
        檢查插件是否已註冊
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            是否已註冊
        """
        return plugin_name in self.plugins
    
    def get_registry_info(self) -> Dict[str, Any]:
        """
        獲取註冊表信息
        
        Returns:
            註冊表信息字典
        """
        return {
            "is_initialized": self.is_initialized,
            "total_plugins": len(self.plugins),
            "plugin_names": self.list_plugins(),
            "dependencies": dict(self.plugin_dependencies)
        }
    
    async def cleanup(self):
        """清理插件註冊表"""
        try:
            self.logger.info("清理插件註冊表...")
            
            # 清理所有插件
            for plugin in self.plugins.values():
                try:
                    await plugin.cleanup()
                except Exception as e:
                    self.logger.error(f"清理插件 {plugin.name} 失敗: {e}")
            
            # 清空註冊表
            self.clear_plugins()
            
            self.is_initialized = False
            self.logger.info("插件註冊表清理完成")
            
        except Exception as e:
            self.logger.error(f"清理插件註冊表失敗: {e}")


# 便捷函數
def create_plugin_registry() -> PluginRegistry:
    """創建插件註冊表實例"""
    return PluginRegistry()


async def setup_plugin_registry() -> PluginRegistry:
    """設置並初始化插件註冊表"""
    registry = create_plugin_registry()
    await registry.initialize()
    return registry 