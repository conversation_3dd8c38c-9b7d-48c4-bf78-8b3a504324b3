"""
UnifiedBot 啟動腳本
啟動統一多功能機器人系統
"""

import asyncio
import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 載入環境變數
load_dotenv()

from bots.unified_bot import UnifiedBot


async def main():
    """主函數"""
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/unified_bot.log', encoding='utf-8')
        ]
    )
    
    logger = logging.getLogger("UnifiedBot")
    
    try:
        # 檢查環境變數
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        if not bot_token:
            logger.error("❌ 請設置 TELEGRAM_BOT_TOKEN 環境變數")
            return
        
        # 創建日誌目錄
        Path("logs").mkdir(exist_ok=True)
        Path("config").mkdir(exist_ok=True)
        Path("plugins").mkdir(exist_ok=True)
        
        logger.info("🚀 啟動 UnifiedBot 統一多功能機器人...")
        
        # 創建並啟動機器人
        bot = UnifiedBot(bot_token, "FlipAdam UnifiedBot")
        
        # 初始化
        await bot.initialize()
        
        # 啟動
        await bot.start()
        
        logger.info("✅ UnifiedBot 啟動成功！")
        logger.info("📱 在 Telegram 中搜索機器人開始使用")
        logger.info("🔧 使用 /help 查看可用命令")
        
        # 保持運行
        try:
            while bot.is_running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 收到中斷信號，正在停止...")
        
    except Exception as e:
        logger.error(f"❌ 啟動失敗: {e}")
        raise
    finally:
        # 停止機器人
        if 'bot' in locals():
            await bot.stop()
        logger.info("👋 UnifiedBot 已停止")


if __name__ == "__main__":
    # 設置事件循環策略（Windows 兼容性）
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 運行主函數
    asyncio.run(main())
