Collecting aiohttp
  Using cached aiohttp-3.12.15-cp313-cp313-win_amd64.whl.metadata (7.9 kB)
Collecting curl-cffi
  Using cached curl_cffi-0.12.0-cp39-abi3-win_amd64.whl.metadata (15 kB)
Collecting matplotlib
  Using cached matplotlib-3.10.5-cp313-cp313-win_amd64.whl.metadata (11 kB)
Collecting numpy
  Using cached numpy-2.3.2-cp313-cp313-win_amd64.whl.metadata (60 kB)
Collecting pandas
  Using cached pandas-2.3.1-cp313-cp313-win_amd64.whl.metadata (19 kB)
Collecting Pillow
  Using cached pillow-11.3.0-cp313-cp313-win_amd64.whl.metadata (9.2 kB)
Collecting python-telegram-bot
  Using cached python_telegram_bot-22.3-py3-none-any.whl.metadata (17 kB)
Collecting pymysql
  Using cached PyMySQL-1.1.1-py3-none-any.whl.metadata (4.4 kB)
Collecting requests
  Using cached requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
Collecting scikit-learn
  Using cached scikit_learn-1.7.1-cp313-cp313-win_amd64.whl.metadata (11 kB)
Collecting sqlalchemy
  Using cached sqlalchemy-2.0.42-cp313-cp313-win_amd64.whl.metadata (9.8 kB)
Collecting yfinance
  Using cached yfinance-0.2.65-py2.py3-none-any.whl.metadata (5.8 kB)
Collecting aiohappyeyeballs>=2.5.0 (from aiohttp)
  Using cached aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.4.0 (from aiohttp)
  Using cached aiosignal-1.4.0-py3-none-any.whl.metadata (3.7 kB)
Collecting attrs>=17.3.0 (from aiohttp)
  Using cached attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting frozenlist>=1.1.1 (from aiohttp)
  Using cached frozenlist-1.7.0-cp313-cp313-win_amd64.whl.metadata (19 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp)
  Using cached multidict-6.6.3-cp313-cp313-win_amd64.whl.metadata (5.4 kB)
Collecting propcache>=0.2.0 (from aiohttp)
  Using cached propcache-0.3.2-cp313-cp313-win_amd64.whl.metadata (12 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp)
  Using cached yarl-1.20.1-cp313-cp313-win_amd64.whl.metadata (76 kB)
Collecting idna>=2.0 (from yarl<2.0,>=1.17.0->aiohttp)
  Using cached idna-3.10-py3-none-any.whl.metadata (10 kB)
Collecting cffi>=1.12.0 (from curl-cffi)
  Using cached cffi-1.17.1-cp313-cp313-win_amd64.whl.metadata (1.6 kB)
Collecting certifi>=2024.2.2 (from curl-cffi)
  Using cached certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)
Collecting contourpy>=1.0.1 (from matplotlib)
  Downloading contourpy-1.3.3-cp313-cp313-win_amd64.whl.metadata (5.5 kB)
Collecting cycler>=0.10 (from matplotlib)
  Using cached cycler-0.12.1-py3-none-any.whl.metadata (3.8 kB)
Collecting fonttools>=4.22.0 (from matplotlib)
  Downloading fonttools-4.59.0-cp313-cp313-win_amd64.whl.metadata (110 kB)
Collecting kiwisolver>=1.3.1 (from matplotlib)
  Using cached kiwisolver-1.4.8-cp313-cp313-win_amd64.whl.metadata (6.3 kB)
Collecting packaging>=20.0 (from matplotlib)
  Using cached packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting pyparsing>=2.3.1 (from matplotlib)
  Using cached pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)
Collecting python-dateutil>=2.7 (from matplotlib)
  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting pytz>=2020.1 (from pandas)
  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas)
  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting httpx<0.29,>=0.27 (from python-telegram-bot)
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting anyio (from httpx<0.29,>=0.27->python-telegram-bot)
  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Collecting httpcore==1.* (from httpx<0.29,>=0.27->python-telegram-bot)
  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting h11>=0.16 (from httpcore==1.*->httpx<0.29,>=0.27->python-telegram-bot)
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting charset_normalizer<4,>=2 (from requests)
  Using cached charset_normalizer-3.4.2-cp313-cp313-win_amd64.whl.metadata (36 kB)
Collecting urllib3<3,>=1.21.1 (from requests)
  Using cached urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
Collecting scipy>=1.8.0 (from scikit-learn)
  Downloading scipy-1.16.1-cp313-cp313-win_amd64.whl.metadata (60 kB)
Collecting joblib>=1.2.0 (from scikit-learn)
  Using cached joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)
Collecting threadpoolctl>=3.1.0 (from scikit-learn)
  Using cached threadpoolctl-3.6.0-py3-none-any.whl.metadata (13 kB)
Collecting greenlet>=1 (from sqlalchemy)
  Using cached greenlet-3.2.3-cp313-cp313-win_amd64.whl.metadata (4.2 kB)
Collecting typing-extensions>=4.6.0 (from sqlalchemy)
  Using cached typing_extensions-4.14.1-py3-none-any.whl.metadata (3.0 kB)
Collecting multitasking>=0.0.7 (from yfinance)
  Downloading multitasking-0.0.12.tar.gz (19 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting platformdirs>=2.0.0 (from yfinance)
  Using cached platformdirs-4.3.8-py3-none-any.whl.metadata (12 kB)
Collecting frozendict>=2.3.4 (from yfinance)
  Using cached frozendict-2.4.6-py313-none-any.whl.metadata (23 kB)
Collecting peewee>=3.16.2 (from yfinance)
  Using cached peewee-3.18.2-py3-none-any.whl
Collecting beautifulsoup4>=4.11.1 (from yfinance)
  Using cached beautifulsoup4-4.13.4-py3-none-any.whl.metadata (3.8 kB)
Collecting protobuf>=3.19.0 (from yfinance)
  Using cached protobuf-6.31.1-cp310-abi3-win_amd64.whl.metadata (593 bytes)
Collecting websockets>=13.0 (from yfinance)
  Using cached websockets-15.0.1-cp313-cp313-win_amd64.whl.metadata (7.0 kB)
Collecting soupsieve>1.2 (from beautifulsoup4>=4.11.1->yfinance)
  Using cached soupsieve-2.7-py3-none-any.whl.metadata (4.6 kB)
Collecting pycparser (from cffi>=1.12.0->curl-cffi)
  Using cached pycparser-2.22-py3-none-any.whl.metadata (943 bytes)
Collecting six>=1.5 (from python-dateutil>=2.7->matplotlib)
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting sniffio>=1.1 (from anyio->httpx<0.29,>=0.27->python-telegram-bot)
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Downloading aiohttp-3.12.15-cp313-cp313-win_amd64.whl (449 kB)
Using cached multidict-6.6.3-cp313-cp313-win_amd64.whl (45 kB)
Using cached yarl-1.20.1-cp313-cp313-win_amd64.whl (86 kB)
Using cached curl_cffi-0.12.0-cp39-abi3-win_amd64.whl (1.6 MB)
Downloading matplotlib-3.10.5-cp313-cp313-win_amd64.whl (8.1 MB)
   ---------------------------------------- 8.1/8.1 MB 16.8 MB/s  0:00:00
Downloading numpy-2.3.2-cp313-cp313-win_amd64.whl (12.8 MB)
   ---------------------------------------- 12.8/12.8 MB 17.3 MB/s  0:00:00
Using cached pandas-2.3.1-cp313-cp313-win_amd64.whl (11.0 MB)
Using cached pillow-11.3.0-cp313-cp313-win_amd64.whl (7.0 MB)
Downloading python_telegram_bot-22.3-py3-none-any.whl (717 kB)
   ---------------------------------------- 717.1/717.1 kB 12.9 MB/s  0:00:00
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Using cached PyMySQL-1.1.1-py3-none-any.whl (44 kB)
Using cached requests-2.32.4-py3-none-any.whl (64 kB)
Using cached charset_normalizer-3.4.2-cp313-cp313-win_amd64.whl (105 kB)
Using cached idna-3.10-py3-none-any.whl (70 kB)
Using cached urllib3-2.5.0-py3-none-any.whl (129 kB)
Using cached scikit_learn-1.7.1-cp313-cp313-win_amd64.whl (8.7 MB)
Downloading sqlalchemy-2.0.42-cp313-cp313-win_amd64.whl (2.1 MB)
   ---------------------------------------- 2.1/2.1 MB 16.0 MB/s  0:00:00
Using cached yfinance-0.2.65-py2.py3-none-any.whl (119 kB)
Using cached aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Using cached aiosignal-1.4.0-py3-none-any.whl (7.5 kB)
Using cached attrs-25.3.0-py3-none-any.whl (63 kB)
Using cached beautifulsoup4-4.13.4-py3-none-any.whl (187 kB)
Using cached certifi-2025.7.14-py3-none-any.whl (162 kB)
Using cached cffi-1.17.1-cp313-cp313-win_amd64.whl (182 kB)
Downloading contourpy-1.3.3-cp313-cp313-win_amd64.whl (226 kB)
Using cached cycler-0.12.1-py3-none-any.whl (8.3 kB)
Downloading fonttools-4.59.0-cp313-cp313-win_amd64.whl (2.2 MB)
   ---------------------------------------- 2.2/2.2 MB 15.1 MB/s  0:00:00
Using cached frozendict-2.4.6-py313-none-any.whl (16 kB)
Using cached frozenlist-1.7.0-cp313-cp313-win_amd64.whl (43 kB)
Using cached greenlet-3.2.3-cp313-cp313-win_amd64.whl (297 kB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Using cached joblib-1.5.1-py3-none-any.whl (307 kB)
Using cached kiwisolver-1.4.8-cp313-cp313-win_amd64.whl (71 kB)
Using cached packaging-25.0-py3-none-any.whl (66 kB)
Using cached platformdirs-4.3.8-py3-none-any.whl (18 kB)
Using cached propcache-0.3.2-cp313-cp313-win_amd64.whl (40 kB)
Using cached protobuf-6.31.1-cp310-abi3-win_amd64.whl (435 kB)
Using cached pyparsing-3.2.3-py3-none-any.whl (111 kB)
Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Using cached pytz-2025.2-py2.py3-none-any.whl (509 kB)
Downloading scipy-1.16.1-cp313-cp313-win_amd64.whl (38.5 MB)
   ---------------------------------------- 38.5/38.5 MB 18.2 MB/s  0:00:02
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Using cached soupsieve-2.7-py3-none-any.whl (36 kB)
Using cached threadpoolctl-3.6.0-py3-none-any.whl (18 kB)
Using cached typing_extensions-4.14.1-py3-none-any.whl (43 kB)
Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Using cached websockets-15.0.1-cp313-cp313-win_amd64.whl (176 kB)
Using cached anyio-4.9.0-py3-none-any.whl (100 kB)
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Using cached pycparser-2.22-py3-none-any.whl (117 kB)
Building wheels for collected packages: multitasking
  Building wheel for multitasking (pyproject.toml): started
  Building wheel for multitasking (pyproject.toml): finished with status 'done'
  Created wheel for multitasking: filename=multitasking-0.0.12-py3-none-any.whl size=15703 sha256=9c920d2ca1f9018905283ca2ab8ec286ffaf0e27b67cbf4ce3958ed687fe82d4
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\1e\df\0f\e2bbb22d689b30c681feb5410ab64a2523437b34c8ecfc6476
Successfully built multitasking
Installing collected packages: pytz, peewee, multitasking, websockets, urllib3, tzdata, typing-extensions, threadpoolctl, soupsieve, sniffio, six, pyparsing, pymysql, pycparser, protobuf, propcache, platformdirs, Pillow, packaging, numpy, multidict, kiwisolver, joblib, idna, h11, greenlet, frozenlist, frozendict, fonttools, cycler, charset_normalizer, certifi, attrs, aiohappyeyeballs, yarl, sqlalchemy, scipy, requests, python-dateutil, httpcore, contourpy, cffi, beautifulsoup4, anyio, aiosignal, scikit-learn, pandas, matplotlib, httpx, curl-cffi, aiohttp, yfinance, python-telegram-bot

Successfully installed Pillow-11.3.0 aiohappyeyeballs-2.6.1 aiohttp-3.12.15 aiosignal-1.4.0 anyio-4.9.0 attrs-25.3.0 beautifulsoup4-4.13.4 certifi-2025.7.14 cffi-1.17.1 charset_normalizer-3.4.2 contourpy-1.3.3 curl-cffi-0.12.0 cycler-0.12.1 fonttools-4.59.0 frozendict-2.4.6 frozenlist-1.7.0 greenlet-3.2.3 h11-0.16.0 httpcore-1.0.9 httpx-0.28.1 idna-3.10 joblib-1.5.1 kiwisolver-1.4.8 matplotlib-3.10.5 multidict-6.6.3 multitasking-0.0.12 numpy-2.3.2 packaging-25.0 pandas-2.3.1 peewee-3.18.2 platformdirs-4.3.8 propcache-0.3.2 protobuf-6.31.1 pycparser-2.22 pymysql-1.1.1 pyparsing-3.2.3 python-dateutil-2.9.0.post0 python-telegram-bot-22.3 pytz-2025.2 requests-2.32.4 scikit-learn-1.7.1 scipy-1.16.1 six-1.17.0 sniffio-1.3.1 soupsieve-2.7 sqlalchemy-2.0.42 threadpoolctl-3.6.0 typing-extensions-4.14.1 tzdata-2025.2 urllib3-2.5.0 websockets-15.0.1 yarl-1.20.1 yfinance-0.2.65
