import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import Application, CommandHandler, ContextTypes, MessageHandler, filters, CallbackQueryHandler
import asyncio
import sys
import config
import pandas as pd
import os
from FlipAdamBox1D import getMLFlipAdambySymbolWithData, get_cffi_session
from telegram.request import HTTPXRequest
import aiohttp
from PIL import Image
import io
from datetime import datetime, timedelta
import time

# 設定日誌
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

# 您的 Bot Token
TOKEN = "7924689299:AAFXkNS7OMdchhQVNuEMajHCNKEx4VojXbI"

# CSV 檔案對應
CSV_FILES = {
    'fund': 'stock_name_hold_fundsonly_SortbyValue.csv',
    'stock': 'stock_name_hold_stockonly_SortbyValue.csv',
    'crypto': 'stock_names_coin.csv',
    'index': 'stock_names_watch_index.csv'
}

# 全域變數用於儲存分析結果
analysis_results = []

def get_symbol_name(symbol, category):
    """獲取標的名稱"""
    try:
        csv_file = CSV_FILES.get(category)
        if csv_file and os.path.exists(csv_file):
            df = pd.read_csv(csv_file)
            if symbol in df['Symbol'].values:
                return df[df['Symbol'] == symbol]['Name'].iloc[0]
    except Exception as e:
        logging.error(f"獲取 {symbol} 名稱時發生錯誤: {e}")
    return symbol

def format_ranking_message(results, category):
    """格式化排名訊息"""
    if not results:
        return f"❌ 無法取得 {category} 的排名資料"
    
    # 按漲跌幅排序（從大到小）
    sorted_results = sorted(results, key=lambda x: x['percentage_change'], reverse=True)
    
    message = f"📊 {category.upper()} 漲跌幅排行\n"
    message += f"📅 分析時間: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
    message += f"📈 總計分析: {len(results)} 個標的\n"
    message += f"🔄 資料同步: ✅ 與產圖邏輯完全一致\n\n"
    
    # 顯示所有標的的漲跌幅排行
    message += "📈 完整漲跌幅排行:\n"
    for i, result in enumerate(sorted_results, 1):
        name = get_symbol_name(result['symbol'], category)
        direction_emoji = "🚀" if result['direction'] == '漲' else "📉" if result['direction'] == '跌' else "➡️"
        message += f"{i:2d}. {direction_emoji} {result['symbol']} ({name})\n"
        message += f"    漲跌幅: {result['percentage_change']:+.2f}% | 價差: {result['price_difference']:+.2f}\n"
        message += f"    趨勢: {result['trend_signal']} | 計算: {result['reflection_direction']}\n"
        message += f"    📈 趨勢線: {result.get('current_trend_line', 0):.2f} | 🛡️ 停損價: {result.get('current_trailing_stop', 0):.2f}\n"
        message += f"    📊 ATR: {result.get('current_atr', 0):.2f} (週期:{result.get('best_atr_period', 0)} 倍數:{result.get('best_atr_multiple', 0):.1f})\n"
        
        # 顯示目前信號
        latest_signals = result.get('latest_signals', [])
        if latest_signals:
            signal_emojis = []
            for signal in latest_signals:
                if signal == "做多":
                    signal_emojis.append("🟢")
                elif signal == "做空":
                    signal_emojis.append("🔴")
                elif signal == "跳空":
                    signal_emojis.append("🟣")
            message += f"    📊 目前信號: {' '.join(signal_emojis)} {' '.join(latest_signals)}\n"
        else:
            message += f"    📊 目前信號: ⚪ 無信號\n"
        
        # 顯示交易模擬建議
        win_rate = result.get('win_rate', 0.0)
        total_trades = result.get('total_trades', 0)
        if total_trades > 0:
            message += f"    📈 回測勝率: {win_rate*100:.1f}% ({result.get('winning_trades', 0)}/{total_trades})\n"
            
            # 根據勝率和信號給出建議
            if latest_signals:
                if win_rate >= 0.6:
                    message += f"    💡 建議: ✅ 強烈推薦 (勝率優秀)\n"
                elif win_rate >= 0.5:
                    message += f"    💡 建議: ⚠️ 謹慎考慮 (勝率一般)\n"
                else:
                    message += f"    💡 建議: ❌ 不建議 (勝率偏低)\n"
            else:
                message += f"    💡 建議: ⏸️ 等待信號\n"
        else:
            message += f"    📈 回測勝率: 無足夠數據\n"
            message += f"    💡 建議: ⏸️ 等待更多數據\n"
        
        message += "\n"
    
    # 統計資訊
    up_symbols = [r for r in results if r['direction'] == '漲']
    down_symbols = [r for r in results if r['direction'] == '跌']
    up_count = len(up_symbols)
    down_count = len(down_symbols)
    
    message += f"📊 統計摘要:\n"
    message += f"• 預期上漲: {up_count} 個 ({up_count/len(results)*100:.1f}%)\n"
    message += f"• 預期下跌: {down_count} 個 ({down_count/len(results)*100:.1f}%)\n"
    
    # 趨勢分布
    trend_counts = {}
    for result in results:
        trend = result['trend_signal']
        trend_counts[trend] = trend_counts.get(trend, 0) + 1
    
    message += f"\n📈 趨勢分布:\n"
    for trend, count in trend_counts.items():
        percentage = count / len(results) * 100
        message += f"• {trend}: {count} 個 ({percentage:.1f}%)\n"
    
    # 信號統計
    signal_stats = {
        '有做多信號': 0,
        '有做空信號': 0,
        '有跳空信號': 0,
        '無信號': 0
    }
    
    for result in results:
        latest_signals = result.get('latest_signals', [])
        if not latest_signals:
            signal_stats['無信號'] += 1
        else:
            if '做多' in latest_signals:
                signal_stats['有做多信號'] += 1
            if '做空' in latest_signals:
                signal_stats['有做空信號'] += 1
            if '跳空' in latest_signals:
                signal_stats['有跳空信號'] += 1
    
    message += f"\n📊 信號分布:\n"
    for signal_type, count in signal_stats.items():
        percentage = count / len(results) * 100
        message += f"• {signal_type}: {count} 個 ({percentage:.1f}%)\n"
    
    return message

def is_image_outdated(image_path):
    """檢查圖片是否過期（超過1天）"""
    if not os.path.exists(image_path):
        return True
    
    # 獲取圖片的修改時間
    image_mtime = datetime.fromtimestamp(os.path.getmtime(image_path))
    current_time = datetime.now()
    
    # 檢查是否超過1天
    return (current_time - image_mtime) > timedelta(days=1)

async def compress_image_to_jpeg(image_path, max_size_kb=100):
    """壓縮圖片到指定 KB 以內，回傳 BytesIO"""
    with Image.open(image_path) as img:
        if img.mode in ("RGBA", "LA"):
            background = Image.new("RGB", img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1])
            img = background
        quality = 95
        output = io.BytesIO()
        while quality > 10:
            output.seek(0)
            output.truncate()
            img.save(output, format="JPEG", quality=quality)
            size_kb = output.tell() / 1024
            if size_kb <= max_size_kb:
                break
            quality -= 5
        output.seek(0)
        return output

async def send_photo_with_aiohttp(token, chat_id, photo_path, caption):
    url = f"https://api.telegram.org/bot{token}/sendPhoto"
    compressed = await compress_image_to_jpeg(photo_path, max_size_kb=100)
    data = aiohttp.FormData()
    data.add_field("chat_id", str(chat_id))
    data.add_field("caption", caption)
    data.add_field("photo", compressed, filename="chart.jpg", content_type="image/jpeg")
    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        async with session.post(url, data=data) as resp:
            result = await resp.json()
            return result

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /start 命令"""
    # 建立常駐按鈕
    keyboard = [
        [
            KeyboardButton("📊 基金"),
            KeyboardButton("📈 股票")
        ],
        [
            KeyboardButton("💰 數位貨幣"),
            KeyboardButton("📉 指數")
        ],
        [
            KeyboardButton("❓ 幫助")
        ]
    ]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    # 發送歡迎訊息和按鈕
    await update.message.reply_text(
        '👋 您好！我是 FlipAdam 通知機器人。\n\n'
        '📌 請選擇要分析的類別：\n'
        '• 基金 - 查看基金相關圖表\n'
        '• 股票 - 查看股票相關圖表\n'
        '• 數位貨幣 - 查看加密貨幣圖表\n'
        '• 指數 - 查看指數相關圖表\n\n'
        '📊 新功能：\n'
        '• 發送圖表後會自動顯示排名報告\n'
        '• 使用 /ranking 命令可單獨查看排名\n'
        '• 排名基於 Adam Theory 反射路徑計算',
        reply_markup=reply_markup
    )

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理按鈕訊息"""
    text = update.message.text
    
    # 移除表情符號以獲取類別
    category = None
    if "基金" in text:
        category = 'fund'
    elif "股票" in text:
        category = 'stock'
    elif "數位貨幣" in text:
        category = 'crypto'
    elif "指數" in text:
        category = 'index'
    elif "幫助" in text:
        await help_command(update, context)
        return
    
    if not category:
        await update.message.reply_text("請使用按鈕選擇要分析的類別")
        return
    
    csv_file = CSV_FILES.get(category)
    
    if not csv_file or not os.path.exists(csv_file):
        await update.message.reply_text(f"❌ 找不到 {category} 的資料檔案")
        return
    
    await update.message.reply_text(f"⏳ 正在處理 {category} 的資料...")
    
    try:
        df = pd.read_csv(csv_file)
        session = get_cffi_session()
        
        # 重置分析結果
        global analysis_results
        analysis_results = []
        
        # 進度追蹤
        total_symbols = len(df)
        processed_count = 0
        
        for index, row in df.iterrows():
            symbol = row['Symbol']
            name = row['Name']
            
            # 更新進度
            processed_count += 1
            if processed_count % 10 == 0:  # 每處理10個標的更新一次進度
                await update.message.reply_text(f"⏳ 處理進度: {processed_count}/{total_symbols}")
            
            # 確保 figure 目錄存在
            if not os.path.exists('figure'):
                os.makedirs('figure')
            
            # 設定圖片路徑
            image_path = os.path.join('figure', f'{symbol}_FlipTwice.png')
            
            # 檢查圖片是否存在且是否需要更新
            if os.path.exists(image_path) and not is_image_outdated(image_path):
                logging.info(f"使用現有的圖表: {symbol}")
                image_path, analysis_data = None, None
            else:
                logging.info(f"生成新的圖表: {symbol}")
                image_path, analysis_data = getMLFlipAdambySymbolWithData(symbol, session=session)
            
            if image_path and os.path.exists(image_path):
                caption = f"📊 {symbol} - {name}"
                try:
                    result = await send_photo_with_aiohttp(
                        TOKEN,
                        update.message.chat_id,
                        image_path,
                        caption
                    )
                    if not result.get("ok"):
                        await update.message.reply_text(f"❌ 發送 {symbol} 的圖表失敗")
                except Exception as e:
                    logging.error(f"發送圖片時發生錯誤: {e}", exc_info=True)
                    await update.message.reply_text(f"❌ 發送 {symbol} 的圖表時發生錯誤")
            
            # 使用從 FlipAdamBox1D.py 獲得的分析資料
            if analysis_data:
                analysis_results.append(analysis_data)
                logging.info(f"已添加 {symbol} 的分析資料到排名列表")
            else:
                logging.warning(f"無法獲取 {symbol} 的分析資料")
            
            # 每發送一張圖片後等待一下，避免被 Telegram 限制
            await asyncio.sleep(1)
        
        await update.message.reply_text(f"✅ {category} 的所有圖表已發送完成")
        
        # 顯示排名報告
        if analysis_results:
            await update.message.reply_text("📊 正在生成排名報告...")
            ranking_message = format_ranking_message(analysis_results, category)
            
            # 如果訊息太長，分段發送
            if len(ranking_message) > 4000:
                # 分割訊息
                parts = []
                current_part = ""
                lines = ranking_message.split('\n')
                
                for line in lines:
                    if len(current_part + line + '\n') > 4000:
                        parts.append(current_part)
                        current_part = line + '\n'
                    else:
                        current_part += line + '\n'
                
                if current_part:
                    parts.append(current_part)
                
                for i, part in enumerate(parts, 1):
                    if len(parts) > 1:
                        part = f"📊 {category.upper()} 排名報告 (第{i}部分，共{len(parts)}部分)\n\n" + part
                    await update.message.reply_text(part)
                    await asyncio.sleep(1)  # 避免發送太快
            else:
                await update.message.reply_text(ranking_message)
        else:
            await update.message.reply_text(f"❌ 無法生成 {category} 的排名報告")
        
    except Exception as e:
        logging.error(f"處理 {category} 時發生錯誤: {e}", exc_info=True)
        await update.message.reply_text(f"❌ 處理 {category} 時發生錯誤")

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /help 命令"""
    help_text = """
📱 可用命令：
/start - 開始使用機器人，選擇要分析的類別
/help - 顯示此幫助訊息
/ranking - 單獨查看排名報告（可指定類別）

📊 支援的類別：
• 基金 - 查看基金相關圖表
• 股票 - 查看股票相關圖表
• 數位貨幣 - 查看加密貨幣圖表
• 指數 - 查看指數相關圖表

🔄 工作流程：
1. 讀取對應的 CSV 檔案
2. 檢查現有圖表是否過期（超過1天）
3. 依照清單順序生成或使用現有圖表
4. 發送圖表到聊天室
5. 計算並顯示排名報告（漲幅大→小排序）

📈 排名報告功能：
• 預期上漲標的 (前10名) - 價差最大的標的
• 預期下跌標的 (前10名) - 價差最小的標的
• 統計摘要 - 上漲/下跌標的數量及比例
• 基於 Adam Theory 反射路徑計算

💡 使用建議：
• 預期上漲標的可考慮做多或持有
• 預期下跌標的可考慮做空或避開
• 排名僅供參考，投資需謹慎

📋 命令範例：
/ranking - 顯示排名選擇選單
/ranking stock - 直接查看股票排名
/ranking crypto - 直接查看加密貨幣排名
    """
    await update.message.reply_text(help_text)

async def ranking_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /ranking 命令 - 單獨顯示排名報告"""
    # 檢查是否有參數指定類別
    args = context.args
    if args:
        category = args[0].lower()
        if category not in CSV_FILES:
            await update.message.reply_text("❌ 無效的類別。請使用: fund, stock, crypto, index")
            return
    else:
        # 如果沒有參數，顯示選擇選單
        keyboard = [
            [
                InlineKeyboardButton("📊 基金排名", callback_data="ranking_fund"),
                InlineKeyboardButton("📈 股票排名", callback_data="ranking_stock")
            ],
            [
                InlineKeyboardButton("💰 數位貨幣排名", callback_data="ranking_crypto"),
                InlineKeyboardButton("📉 指數排名", callback_data="ranking_index")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(
            "📊 請選擇要查看排名的類別：",
            reply_markup=reply_markup
        )
        return
    
    await generate_ranking_report(update, context, category)

async def generate_ranking_report(update: Update, context: ContextTypes.DEFAULT_TYPE, category):
    """生成排名報告"""
    csv_file = CSV_FILES.get(category)
    
    if not csv_file or not os.path.exists(csv_file):
        await update.message.reply_text(f"❌ 找不到 {category} 的資料檔案")
        return
    
    await update.message.reply_text(f"⏳ 正在計算 {category} 的排名...")
    
    try:
        df = pd.read_csv(csv_file)
        session = get_cffi_session()
        
        # 重置分析結果
        global analysis_results
        analysis_results = []
        
        # 進度追蹤
        total_symbols = len(df)
        processed_count = 0
        
        for index, row in df.iterrows():
            symbol = row['Symbol']
            
            # 更新進度
            processed_count += 1
            if processed_count % 20 == 0:  # 每處理20個標的更新一次進度
                await update.message.reply_text(f"⏳ 計算進度: {processed_count}/{total_symbols}")
            
            # 使用 getMLFlipAdambySymbolWithData 獲取分析資料
            try:
                image_path, analysis_data = getMLFlipAdambySymbolWithData(symbol, session=session)
                if analysis_data:
                    analysis_results.append(analysis_data)
                    logging.info(f"已添加 {symbol} 的分析資料到排名列表")
                else:
                    logging.warning(f"無法獲取 {symbol} 的分析資料")
            except Exception as e:
                logging.error(f"計算 {symbol} 排名資料時發生錯誤: {e}", exc_info=True)
                continue # 跳過此標的的排名計算
            
            # 避免請求過快
            await asyncio.sleep(0.5)
        
        # 顯示排名報告
        if analysis_results:
            ranking_message = format_ranking_message(analysis_results, category)
            
            # 如果訊息太長，分段發送
            if len(ranking_message) > 4000:
                parts = []
                current_part = ""
                lines = ranking_message.split('\n')
                
                for line in lines:
                    if len(current_part + line + '\n') > 4000:
                        parts.append(current_part)
                        current_part = line + '\n'
                    else:
                        current_part += line + '\n'
                
                if current_part:
                    parts.append(current_part)
                
                for i, part in enumerate(parts, 1):
                    if len(parts) > 1:
                        part = f"📊 {category.upper()} 排名報告 (第{i}部分，共{len(parts)}部分)\n\n" + part
                    await update.message.reply_text(part)
                    await asyncio.sleep(1)
            else:
                await update.message.reply_text(ranking_message)
        else:
            await update.message.reply_text(f"❌ 無法生成 {category} 的排名報告")
        
    except Exception as e:
        logging.error(f"生成 {category} 排名報告時發生錯誤: {e}", exc_info=True)
        await update.message.reply_text(f"❌ 生成 {category} 排名報告時發生錯誤")

async def handle_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理回調查詢"""
    query = update.callback_query
    await query.answer()
    
    if query.data.startswith("ranking_"):
        category = query.data.replace("ranking_", "")
        # 使用 query.message 而不是 update.message
        await generate_ranking_report_for_callback(query, context, category)

async def generate_ranking_report_for_callback(query, context: ContextTypes.DEFAULT_TYPE, category):
    """為回調查詢生成排名報告"""
    csv_file = CSV_FILES.get(category)
    
    if not csv_file or not os.path.exists(csv_file):
        await query.message.reply_text(f"❌ 找不到 {category} 的資料檔案")
        return
    
    await query.message.reply_text(f"⏳ 正在計算 {category} 的排名...")
    
    try:
        df = pd.read_csv(csv_file)
        session = get_cffi_session()
        
        # 重置分析結果
        global analysis_results
        analysis_results = []
        
        # 進度追蹤
        total_symbols = len(df)
        processed_count = 0
        
        for index, row in df.iterrows():
            symbol = row['Symbol']
            
            # 更新進度
            processed_count += 1
            if processed_count % 20 == 0:  # 每處理20個標的更新一次進度
                await query.message.reply_text(f"⏳ 計算進度: {processed_count}/{total_symbols}")
            
            # 使用 getMLFlipAdambySymbolWithData 獲取分析資料
            try:
                image_path, analysis_data = getMLFlipAdambySymbolWithData(symbol, session=session)
                if analysis_data:
                    analysis_results.append(analysis_data)
                    logging.info(f"已添加 {symbol} 的分析資料到排名列表")
                else:
                    logging.warning(f"無法獲取 {symbol} 的分析資料")
            except Exception as e:
                logging.error(f"計算 {symbol} 排名資料時發生錯誤: {e}", exc_info=True)
                continue # 跳過此標的的排名計算
            
            # 避免請求過快
            await asyncio.sleep(0.5)
        
        # 顯示排名報告
        if analysis_results:
            ranking_message = format_ranking_message(analysis_results, category)
            
            # 如果訊息太長，分段發送
            if len(ranking_message) > 4000:
                parts = []
                current_part = ""
                lines = ranking_message.split('\n')
                
                for line in lines:
                    if len(current_part + line + '\n') > 4000:
                        parts.append(current_part)
                        current_part = line + '\n'
                    else:
                        current_part += line + '\n'
                
                if current_part:
                    parts.append(current_part)
                
                for i, part in enumerate(parts, 1):
                    if len(parts) > 1:
                        part = f"📊 {category.upper()} 排名報告 (第{i}部分，共{len(parts)}部分)\n\n" + part
                    await query.message.reply_text(part)
                    await asyncio.sleep(1)
            else:
                await query.message.reply_text(ranking_message)
        else:
            await query.message.reply_text(f"❌ 無法生成 {category} 的排名報告")
        
    except Exception as e:
        logging.error(f"生成 {category} 排名報告時發生錯誤: {e}", exc_info=True)
        await query.message.reply_text(f"❌ 生成 {category} 排名報告時發生錯誤")

def main():
    """主函數"""
    # 設定更長的超時時間
    request = HTTPXRequest(
        connect_timeout=30.0,
        read_timeout=30.0,
        write_timeout=30.0,
        pool_timeout=30.0,
    )
    # 建立應用程式
    application = Application.builder().token(TOKEN).request(request).build()

    # 註冊命令處理器
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("ranking", ranking_command))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
    application.add_handler(CallbackQueryHandler(handle_callback))

    # 啟動機器人
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    if sys.platform.startswith('win'):
        # Windows 特定設定
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    main() 