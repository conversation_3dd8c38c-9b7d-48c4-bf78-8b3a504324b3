import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import Application, CommandHandler, ContextTypes, MessageHandler, filters, CallbackQueryHandler
import asyncio
import sys
import config
import pandas as pd
import os
from FlipAdamBox1D import getMLFlipAdambySymbol, get_cffi_session
from telegram.request import HTTPXRequest
import aiohttp
from PIL import Image
import io
from datetime import datetime, timedelta

# 設定日誌
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

# 您的 Bot Token
TOKEN = "7924689299:AAFXkNS7OMdchhQVNuEMajHCNKEx4VojXbI"

# CSV 檔案對應
CSV_FILES = {
    'fund': 'stock_name_hold_fundsonly_SortbyValue.csv',
    'stock': 'stock_name_hold_stockonly_SortbyValue.csv',
    'crypto': 'stock_names_coin.csv',
    'index': 'stock_names_watch_index.csv'
}

def is_image_outdated(image_path):
    """檢查圖片是否過期（超過1天）"""
    if not os.path.exists(image_path):
        return True
    
    # 獲取圖片的修改時間
    image_mtime = datetime.fromtimestamp(os.path.getmtime(image_path))
    current_time = datetime.now()
    
    # 檢查是否超過1天
    return (current_time - image_mtime) > timedelta(days=1)

async def compress_image_to_jpeg(image_path, max_size_kb=100):
    """壓縮圖片到指定 KB 以內，回傳 BytesIO"""
    with Image.open(image_path) as img:
        if img.mode in ("RGBA", "LA"):
            background = Image.new("RGB", img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1])
            img = background
        quality = 95
        output = io.BytesIO()
        while quality > 10:
            output.seek(0)
            output.truncate()
            img.save(output, format="JPEG", quality=quality)
            size_kb = output.tell() / 1024
            if size_kb <= max_size_kb:
                break
            quality -= 5
        output.seek(0)
        return output

async def send_photo_with_aiohttp(token, chat_id, photo_path, caption):
    url = f"https://api.telegram.org/bot{token}/sendPhoto"
    compressed = await compress_image_to_jpeg(photo_path, max_size_kb=100)
    data = aiohttp.FormData()
    data.add_field("chat_id", str(chat_id))
    data.add_field("caption", caption)
    data.add_field("photo", compressed, filename="chart.jpg", content_type="image/jpeg")
    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        async with session.post(url, data=data) as resp:
            result = await resp.json()
            return result

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /start 命令"""
    # 建立常駐按鈕
    keyboard = [
        [
            KeyboardButton("📊 基金"),
            KeyboardButton("📈 股票")
        ],
        [
            KeyboardButton("💰 數位貨幣"),
            KeyboardButton("📉 指數")
        ],
        [
            KeyboardButton("❓ 幫助")
        ]
    ]
    reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    # 發送歡迎訊息和按鈕
    await update.message.reply_text(
        '👋 您好！我是 FlipAdam 通知機器人。\n\n'
        '📌 請選擇要分析的類別：\n'
        '• 基金 - 查看基金相關圖表\n'
        '• 股票 - 查看股票相關圖表\n'
        '• 數位貨幣 - 查看加密貨幣圖表\n'
        '• 指數 - 查看指數相關圖表',
        reply_markup=reply_markup
    )

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理按鈕訊息"""
    text = update.message.text
    
    # 移除表情符號以獲取類別
    category = None
    if "基金" in text:
        category = 'fund'
    elif "股票" in text:
        category = 'stock'
    elif "數位貨幣" in text:
        category = 'crypto'
    elif "指數" in text:
        category = 'index'
    elif "幫助" in text:
        await help_command(update, context)
        return
    
    if not category:
        await update.message.reply_text("請使用按鈕選擇要分析的類別")
        return
    
    csv_file = CSV_FILES.get(category)
    
    if not csv_file or not os.path.exists(csv_file):
        await update.message.reply_text(f"❌ 找不到 {category} 的資料檔案")
        return
    
    await update.message.reply_text(f"⏳ 正在處理 {category} 的資料...")
    
    try:
        df = pd.read_csv(csv_file)
        session = get_cffi_session()
        
        for index, row in df.iterrows():
            symbol = row['Symbol']
            name = row['Name']
            
            # 確保 figure 目錄存在
            if not os.path.exists('figure'):
                os.makedirs('figure')
            
            # 設定圖片路徑
            image_path = os.path.join('figure', f'{symbol}_FlipTwice.png')
            
            # 檢查圖片是否存在且是否需要更新
            if os.path.exists(image_path) and not is_image_outdated(image_path):
                logging.info(f"使用現有的圖表: {symbol}")
            else:
                logging.info(f"生成新的圖表: {symbol}")
                image_path = getMLFlipAdambySymbol(symbol, session=session)
            
            if image_path and os.path.exists(image_path):
                caption = f"📊 {symbol} - {name}"
                try:
                    result = await send_photo_with_aiohttp(
                        TOKEN,
                        update.message.chat_id,
                        image_path,
                        caption
                    )
                    if not result.get("ok"):
                        await update.message.reply_text(f"❌ 發送 {symbol} 的圖表失敗")
                except Exception as e:
                    logging.error(f"發送圖片時發生錯誤: {e}", exc_info=True)
                    await update.message.reply_text(f"❌ 發送 {symbol} 的圖表時發生錯誤")
            
            # 每發送一張圖片後等待一下，避免被 Telegram 限制
            await asyncio.sleep(1)
        
        await update.message.reply_text(f"✅ {category} 的所有圖表已發送完成")
        
    except Exception as e:
        logging.error(f"處理 {category} 時發生錯誤: {e}", exc_info=True)
        await update.message.reply_text(f"❌ 處理 {category} 時發生錯誤")

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """處理 /help 命令"""
    help_text = """
📱 可用命令：
/start - 開始使用機器人，選擇要分析的類別
/help - 顯示此幫助訊息

📊 支援的類別：
• 基金 - 查看基金相關圖表
• 股票 - 查看股票相關圖表
• 數位貨幣 - 查看加密貨幣圖表
• 指數 - 查看指數相關圖表

🔄 工作流程：
1. 讀取對應的 CSV 檔案
2. 檢查現有圖表是否過期（超過1天）
3. 依照清單順序生成或使用現有圖表
4. 發送圖表到聊天室
    """
    await update.message.reply_text(help_text)

def main():
    """主函數"""
    # 設定更長的超時時間
    request = HTTPXRequest(
        connect_timeout=30.0,
        read_timeout=30.0,
        write_timeout=30.0,
        pool_timeout=30.0,
    )
    # 建立應用程式
    application = Application.builder().token(TOKEN).request(request).build()

    # 註冊命令處理器
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

    # 啟動機器人
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    if sys.platform.startswith('win'):
        # Windows 特定設定
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    main() 