"""
插件模組
包含插件系統的基礎組件
"""

from .base_plugin import BasePlugin, create_base_plugin
from .plugin_loader import Plugin<PERSON>oa<PERSON>, create_plugin_loader, setup_plugin_loader
from .plugin_registry import PluginRegistry, create_plugin_registry, setup_plugin_registry

__all__ = [
    'BasePlugin',
    'create_base_plugin',
    'PluginLoader',
    'create_plugin_loader',
    'setup_plugin_loader',
    'PluginRegistry',
    'create_plugin_registry',
    'setup_plugin_registry'
] 