"""
警報功能插件
提供價格警報、成交量警報、技術指標警報等功能
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path

from core.plugins.base_plugin import BasePlugin


class AlertPlugin(BasePlugin):
    """警報功能插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "AlertPlugin"
        self.version = "1.0.0"
        self.description = "提供價格警報、成交量警報、技術指標警報等功能"
        self.author = "FlipAdam System"
        
        # 警報配置
        self.alerts_file = "config/alerts.json"
        self.alerts: Dict[str, Any] = {}
        
        # 警報檢查間隔（秒）
        self.check_interval = 300  # 5分鐘
        
        # 檢查任務
        self.check_task: Optional[asyncio.Task] = None
        
        # 註冊命令
        self.register_command("alert", self._handle_alert_command)
        self.register_command("alerts", self._handle_alerts_command)
        self.register_command("add_alert", self._handle_add_alert_command)
        self.register_command("remove_alert", self._handle_remove_alert_command)
        self.register_command("clear_alerts", self._handle_clear_alerts_command)
    
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        try:
            self.logger.info("初始化警報插件...")
            
            # 載入配置
            self.config = config
            
            # 載入警報設定
            await self._load_alerts()
            
            # 註冊事件處理器
            if hasattr(self, 'event_bus'):
                self.event_bus.subscribe("market.price_update", self._handle_price_update)
                self.event_bus.subscribe("market.volume_update", self._handle_volume_update)
            
            self.is_initialized = True
            self.logger.info("警報插件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"警報插件初始化失敗: {e}")
            return False
    
    async def start(self) -> bool:
        """啟動插件"""
        try:
            self.logger.info("啟動警報插件...")
            
            # 啟動警報檢查任務
            self.check_task = asyncio.create_task(self._alert_check_loop())
            
            self.is_running = True
            self.logger.info("警報插件啟動完成")
            return True
            
        except Exception as e:
            self.logger.error(f"警報插件啟動失敗: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止插件"""
        try:
            self.logger.info("停止警報插件...")
            
            # 停止檢查任務
            if self.check_task:
                self.check_task.cancel()
                try:
                    await self.check_task
                except asyncio.CancelledError:
                    pass
            
            self.is_running = False
            self.logger.info("警報插件已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"警報插件停止失敗: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """清理插件資源"""
        try:
            self.logger.info("清理警報插件資源...")
            
            # 保存警報設定
            await self._save_alerts()
            
            # 取消事件訂閱
            if hasattr(self, 'event_bus'):
                self.event_bus.unsubscribe("market.price_update", self._handle_price_update)
                self.event_bus.unsubscribe("market.volume_update", self._handle_volume_update)
            
            self.logger.info("警報插件資源清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"警報插件清理失敗: {e}")
            return False
    
    async def _load_alerts(self):
        """載入警報設定"""
        try:
            alerts_path = Path(self.alerts_file)
            if alerts_path.exists():
                with open(alerts_path, 'r', encoding='utf-8') as f:
                    self.alerts = json.load(f)
                self.logger.info(f"載入警報設定: {len(self.alerts)} 個警報")
            else:
                self.alerts = {}
                self.logger.info("創建新的警報設定檔案")
        except Exception as e:
            self.logger.error(f"載入警報設定失敗: {e}")
            self.alerts = {}
    
    async def _save_alerts(self):
        """保存警報設定"""
        try:
            alerts_path = Path(self.alerts_file)
            alerts_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(alerts_path, 'w', encoding='utf-8') as f:
                json.dump(self.alerts, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"保存警報設定: {len(self.alerts)} 個警報")
        except Exception as e:
            self.logger.error(f"保存警報設定失敗: {e}")
    
    async def _alert_check_loop(self):
        """警報檢查循環"""
        while self.is_running:
            try:
                await self._check_alerts()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"警報檢查失敗: {e}")
                await asyncio.sleep(60)  # 錯誤時等待1分鐘
    
    async def _check_alerts(self):
        """檢查所有警報"""
        if not self.alerts:
            return
        
        self.logger.debug(f"檢查 {len(self.alerts)} 個警報...")
        
        # 這裡應該實現實際的警報檢查邏輯
        # 例如：檢查價格、成交量、技術指標等
        # 目前只是示例實現
        
        for alert_id, alert in self.alerts.items():
            if alert.get('enabled', True):
                await self._check_single_alert(alert_id, alert)
    
    async def _check_single_alert(self, alert_id: str, alert: Dict[str, Any]):
        """檢查單個警報"""
        try:
            alert_type = alert.get('type', 'price')
            
            if alert_type == 'price':
                await self._check_price_alert(alert_id, alert)
            elif alert_type == 'volume':
                await self._check_volume_alert(alert_id, alert)
            elif alert_type == 'technical':
                await self._check_technical_alert(alert_id, alert)
            
        except Exception as e:
            self.logger.error(f"檢查警報 {alert_id} 失敗: {e}")
    
    async def _check_price_alert(self, alert_id: str, alert: Dict[str, Any]):
        """檢查價格警報"""
        # 示例實現 - 實際應該從市場數據源獲取價格
        symbol = alert.get('symbol', '')
        target_price = alert.get('target_price', 0)
        condition = alert.get('condition', 'above')  # above, below
        
        # 這裡應該獲取實際價格
        current_price = 0  # 從市場數據源獲取
        
        if condition == 'above' and current_price >= target_price:
            await self._trigger_alert(alert_id, alert, f"價格警報: {symbol} 已達到 {target_price}")
        elif condition == 'below' and current_price <= target_price:
            await self._trigger_alert(alert_id, alert, f"價格警報: {symbol} 已降至 {target_price}")
    
    async def _check_volume_alert(self, alert_id: str, alert: Dict[str, Any]):
        """檢查成交量警報"""
        # 示例實現
        symbol = alert.get('symbol', '')
        target_volume = alert.get('target_volume', 0)
        
        # 這裡應該獲取實際成交量
        current_volume = 0  # 從市場數據源獲取
        
        if current_volume >= target_volume:
            await self._trigger_alert(alert_id, alert, f"成交量警報: {symbol} 成交量異常")
    
    async def _check_technical_alert(self, alert_id: str, alert: Dict[str, Any]):
        """檢查技術指標警報"""
        # 示例實現
        symbol = alert.get('symbol', '')
        indicator = alert.get('indicator', '')
        
        # 這裡應該計算實際技術指標
        # 例如：RSI、MACD、布林帶等
        
        await self._trigger_alert(alert_id, alert, f"技術指標警報: {symbol} {indicator} 信號")
    
    async def _trigger_alert(self, alert_id: str, alert: Dict[str, Any], message: str):
        """觸發警報"""
        try:
            # 發布警報事件
            if hasattr(self, 'event_bus'):
                await self.event_bus.publish("alert.triggered", {
                    "alert_id": alert_id,
                    "alert": alert,
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                })
            
            # 記錄警報
            self.logger.info(f"警報觸發: {alert_id} - {message}")
            
            # 如果是一次性警報，則移除
            if alert.get('one_time', False):
                del self.alerts[alert_id]
                await self._save_alerts()
            
        except Exception as e:
            self.logger.error(f"觸發警報失敗: {e}")
    
    async def _handle_price_update(self, event):
        """處理價格更新事件"""
        try:
            data = event.data
            symbol = data.get('symbol', '')
            price = data.get('price', 0)
            
            # 檢查相關警報
            for alert_id, alert in self.alerts.items():
                if alert.get('symbol') == symbol and alert.get('type') == 'price':
                    await self._check_single_alert(alert_id, alert)
                    
        except Exception as e:
            self.logger.error(f"處理價格更新事件失敗: {e}")
    
    async def _handle_volume_update(self, event):
        """處理成交量更新事件"""
        try:
            data = event.data
            symbol = data.get('symbol', '')
            volume = data.get('volume', 0)
            
            # 檢查相關警報
            for alert_id, alert in self.alerts.items():
                if alert.get('symbol') == symbol and alert.get('type') == 'volume':
                    await self._check_single_alert(alert_id, alert)
                    
        except Exception as e:
            self.logger.error(f"處理成交量更新事件失敗: {e}")
    
    # 命令處理器
    async def _handle_alert_command(self, command: str, update, context) -> bool:
        """處理 /alert 命令"""
        if not context.args:
            await update.message.reply_text("""
🚨 警報功能說明

📋 可用命令：
/alerts - 查看所有警報
/add_alert - 添加新警報
/remove_alert <警報ID> - 移除警報
/clear_alerts - 清除所有警報

🔧 警報類型：
• 價格警報 - 監控價格突破
• 成交量警報 - 監控成交量異常
• 技術指標警報 - 監控技術信號
            """)
            return True
        return False
    
    async def _handle_alerts_command(self, command: str, update, context) -> bool:
        """處理 /alerts 命令"""
        if not self.alerts:
            await update.message.reply_text("📭 目前沒有設置任何警報")
            return True
        
        alerts_text = "🚨 當前警報列表：\n\n"
        for i, (alert_id, alert) in enumerate(self.alerts.items(), 1):
            status = "✅ 啟用" if alert.get('enabled', True) else "❌ 停用"
            alerts_text += f"{i}. {alert_id} ({alert.get('type', 'unknown')}) {status}\n"
            alerts_text += f"   標的: {alert.get('symbol', 'N/A')}\n"
            alerts_text += f"   條件: {alert.get('condition', 'N/A')}\n\n"
        
        await update.message.reply_text(alerts_text)
        return True
    
    async def _handle_add_alert_command(self, command: str, update, context) -> bool:
        """處理 /add_alert 命令"""
        if len(context.args) < 4:
            await update.message.reply_text("""
❌ 參數不足，正確格式：
/add_alert <類型> <標的> <條件> <目標值>

📝 範例：
/add_alert price BTC-USD above 50000
/add_alert volume ETH-USD above 1000000
/add_alert technical BTC-USD rsi oversold
            """)
            return True
        
        alert_type = context.args[0]
        symbol = context.args[1]
        condition = context.args[2]
        target_value = context.args[3]
        
        # 創建警報ID
        alert_id = f"{alert_type}_{symbol}_{condition}_{target_value}"
        
        # 創建警報設定
        alert = {
            "type": alert_type,
            "symbol": symbol,
            "condition": condition,
            "target_value": target_value,
            "enabled": True,
            "one_time": False,
            "created_at": datetime.now().isoformat()
        }
        
        self.alerts[alert_id] = alert
        await self._save_alerts()
        
        await update.message.reply_text(f"✅ 警報添加成功：{alert_id}")
        return True
    
    async def _handle_remove_alert_command(self, command: str, update, context) -> bool:
        """處理 /remove_alert 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供警報ID：/remove_alert <警報ID>")
            return True
        
        alert_id = context.args[0]
        
        if alert_id in self.alerts:
            del self.alerts[alert_id]
            await self._save_alerts()
            await update.message.reply_text(f"✅ 警報移除成功：{alert_id}")
        else:
            await update.message.reply_text(f"❌ 找不到警報：{alert_id}")
        
        return True
    
    async def _handle_clear_alerts_command(self, command: str, update, context) -> bool:
        """處理 /clear_alerts 命令"""
        count = len(self.alerts)
        self.alerts.clear()
        await self._save_alerts()
        
        await update.message.reply_text(f"✅ 已清除 {count} 個警報")
        return True
    
    def get_help_text(self) -> str:
        """獲取插件說明文字"""
        return """
🚨 警報功能插件

功能：
• 價格警報 - 監控價格突破
• 成交量警報 - 監控成交量異常  
• 技術指標警報 - 監控技術信號

命令：
/alert - 顯示幫助
/alerts - 查看警報列表
/add_alert - 添加新警報
/remove_alert - 移除警報
/clear_alerts - 清除所有警報
        """
    
    def get_status(self) -> Dict[str, Any]:
        """獲取插件狀態"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": self.author,
            "initialized": self.is_initialized,
            "running": self.is_running,
            "enabled": self.is_enabled,
            "alerts_count": len(self.alerts),
            "active_alerts": len([a for a in self.alerts.values() if a.get('enabled', True)]),
            "commands": list(self.commands.keys()),
            "message_handlers": list(self.message_handlers.keys())
        }


# 插件工廠函數
def create_alert_plugin() -> AlertPlugin:
    """創建警報插件實例"""
    return AlertPlugin()
