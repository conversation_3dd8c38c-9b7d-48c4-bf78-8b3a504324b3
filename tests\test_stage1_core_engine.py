"""
階段一：核心引擎功能測試
測試統一機器人核心、模組管理器、事件總線和配置管理器
"""

import asyncio
import logging
import pytest
import tempfile
import os
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 導入測試的組件
try:
    from bots.unified_bot import UnifiedBot
    from core.management.module_manager import ModuleManager
    from core.events.event_bus import EventBus
    from core.config.config_manager import ConfigManager
except ImportError as e:
    print(f"導入錯誤: {e}")
    print("請確保所有必要的模組都已建立")
    sys.exit(1)


class TestStage1CoreEngine:
    """階段一核心引擎測試類別"""
    
    def setup_method(self):
        """每個測試方法前的設置"""
        # 設置測試日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger("TestStage1")
        
        # 創建臨時目錄
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
        
        # 創建必要的目錄
        Path("config").mkdir(exist_ok=True)
        Path("plugins").mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
    
    def teardown_method(self):
        """每個測試方法後的清理"""
        # 恢復原始工作目錄
        os.chdir(self.original_cwd)
        
        # 清理臨時目錄
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_event_bus_initialization(self):
        """測試事件總線初始化"""
        self.logger.info("測試事件總線初始化...")
        
        # 創建事件總線
        event_bus = EventBus()
        
        # 測試初始化
        await event_bus.initialize()
        
        # 驗證初始化狀態
        assert event_bus.is_initialized == True
        assert event_bus.subscribers is not None
        assert event_bus.event_history is not None
        
        self.logger.info("✅ 事件總線初始化測試通過")
    
    @pytest.mark.asyncio
    async def test_event_bus_subscription(self):
        """測試事件總線訂閱功能"""
        self.logger.info("測試事件總線訂閱功能...")
        
        event_bus = EventBus()
        await event_bus.initialize()
        
        # 測試事件處理器
        received_events = []
        
        def test_handler(event):
            received_events.append(event)
        
        # 訂閱事件
        event_bus.subscribe("test.event", test_handler)
        
        # 發布事件
        test_data = {"message": "Hello World"}
        await event_bus.publish("test.event", test_data)
        
        # 等待事件處理
        await asyncio.sleep(0.1)
        
        # 驗證事件處理
        assert len(received_events) == 1
        assert received_events[0].event_type == "test.event"
        assert received_events[0].data == test_data
        
        self.logger.info("✅ 事件總線訂閱功能測試通過")
    
    @pytest.mark.asyncio
    async def test_event_bus_wildcard_subscription(self):
        """測試事件總線通配符訂閱"""
        self.logger.info("測試事件總線通配符訂閱...")
        
        event_bus = EventBus()
        await event_bus.initialize()
        
        received_events = []
        
        def wildcard_handler(event):
            received_events.append(event)
        
        # 訂閱通配符事件
        event_bus.subscribe("plugin.*", wildcard_handler)
        
        # 發布多個事件
        await event_bus.publish("plugin.loaded", {"name": "test_plugin"})
        await event_bus.publish("plugin.unloaded", {"name": "test_plugin"})
        await event_bus.publish("system.start", {"status": "running"})
        
        # 等待事件處理
        await asyncio.sleep(0.1)
        
        # 驗證只有插件事件被處理
        assert len(received_events) == 2
        assert all(event.event_type.startswith("plugin.") for event in received_events)
        
        self.logger.info("✅ 事件總線通配符訂閱測試通過")
    
    @pytest.mark.asyncio
    async def test_config_manager_initialization(self):
        """測試配置管理器初始化"""
        self.logger.info("測試配置管理器初始化...")
        
        config_manager = ConfigManager()
        
        # 測試初始化
        await config_manager.initialize()
        
        # 驗證初始化狀態
        assert config_manager.is_initialized == True
        assert config_manager.config is not None
        assert "bot" in config_manager.config
        assert "plugins" in config_manager.config
        
        self.logger.info("✅ 配置管理器初始化測試通過")
    
    @pytest.mark.asyncio
    async def test_config_manager_get_set(self):
        """測試配置管理器獲取和設置功能"""
        self.logger.info("測試配置管理器獲取和設置功能...")
        
        config_manager = ConfigManager()
        await config_manager.initialize()
        
        # 測試獲取配置
        bot_name = config_manager.get_config("bot.name")
        assert bot_name == "UnifiedBot"
        
        # 測試設置配置
        await config_manager.update_config("bot.name", "TestBot")
        new_bot_name = config_manager.get_config("bot.name")
        assert new_bot_name == "TestBot"
        
        # 測試嵌套配置
        await config_manager.update_config("bot.settings.debug", True)
        debug_setting = config_manager.get_config("bot.settings.debug")
        assert debug_setting == True
        
        self.logger.info("✅ 配置管理器獲取和設置功能測試通過")
    
    @pytest.mark.asyncio
    async def test_config_manager_plugin_config(self):
        """測試配置管理器插件配置功能"""
        self.logger.info("測試配置管理器插件配置功能...")
        
        config_manager = ConfigManager()
        await config_manager.initialize()
        
        # 測試獲取插件配置
        plugin_config = await config_manager.get_plugin_config("SubscriptionPlugin")
        assert plugin_config is not None
        assert "enabled" in plugin_config
        
        # 測試更新插件配置
        new_config = {
            "enabled": False,
            "config": {
                "update_frequency": "1hour",
                "max_subscriptions": 50
            }
        }
        await config_manager.update_plugin_config("TestPlugin", new_config)
        
        # 驗證配置已更新
        updated_config = await config_manager.get_plugin_config("TestPlugin")
        assert updated_config["enabled"] == False
        assert updated_config["config"]["update_frequency"] == "1hour"
        
        self.logger.info("✅ 配置管理器插件配置功能測試通過")
    
    @pytest.mark.asyncio
    async def test_module_manager_initialization(self):
        """測試模組管理器初始化"""
        self.logger.info("測試模組管理器初始化...")
        
        # 創建依賴組件
        event_bus = EventBus()
        await event_bus.initialize()
        
        config_manager = ConfigManager()
        await config_manager.initialize()
        
        # 創建模組管理器
        module_manager = ModuleManager()
        
        # 測試初始化
        await module_manager.initialize(event_bus, config_manager)
        
        # 驗證初始化狀態
        assert module_manager.is_initialized == True
        assert module_manager.plugin_loader is not None
        assert module_manager.plugin_registry is not None
        
        self.logger.info("✅ 模組管理器初始化測試通過")
    
    @pytest.mark.asyncio
    async def test_unified_bot_initialization(self):
        """測試統一機器人初始化"""
        self.logger.info("測試統一機器人初始化...")
        
        # 創建測試 Bot Token
        test_token = "test_token_12345"
        
        # 創建統一機器人
        bot = UnifiedBot(test_token, "TestUnifiedBot")
        
        # 測試初始化
        await bot.initialize()
        
        # 驗證初始化狀態
        assert bot.is_initialized == True
        assert bot.module_manager is not None
        assert bot.event_bus is not None
        assert bot.config_manager is not None
        assert bot.plugin_registry is not None
        assert len(bot.commands) > 0
        
        self.logger.info("✅ 統一機器人初始化測試通過")
    
    @pytest.mark.asyncio
    async def test_unified_bot_commands(self):
        """測試統一機器人命令功能"""
        self.logger.info("測試統一機器人命令功能...")
        
        test_token = "test_token_12345"
        bot = UnifiedBot(test_token, "TestUnifiedBot")
        await bot.initialize()
        
        # 測試命令註冊
        assert "/start" in bot.commands
        assert "/help" in bot.commands
        assert "/status" in bot.commands
        assert "/plugins" in bot.commands
        
        # 測試插件管理命令
        assert "/load_plugin" in bot.commands
        assert "/unload_plugin" in bot.commands
        assert "/reload_plugin" in bot.commands
        assert "/enable_plugin" in bot.commands
        assert "/disable_plugin" in bot.commands
        
        self.logger.info("✅ 統一機器人命令功能測試通過")
    
    @pytest.mark.asyncio
    async def test_integration_core_components(self):
        """測試核心組件整合"""
        self.logger.info("測試核心組件整合...")
        
        # 創建所有核心組件
        event_bus = EventBus()
        await event_bus.initialize()
        
        config_manager = ConfigManager()
        await config_manager.initialize()
        
        module_manager = ModuleManager()
        await module_manager.initialize(event_bus, config_manager)
        
        # 測試組件間通信
        test_events = []
        
        def test_handler(event):
            test_events.append(event)
        
        event_bus.subscribe("test.integration", test_handler)
        
        # 發布測試事件
        await event_bus.publish("test.integration", {"component": "integration_test"})
        await asyncio.sleep(0.1)
        
        # 驗證事件處理
        assert len(test_events) == 1
        assert test_events[0].data["component"] == "integration_test"
        
        # 測試配置和模組管理器的整合
        plugin_config = await config_manager.get_plugin_config("SubscriptionPlugin")
        assert plugin_config is not None
        
        # 測試統計信息
        event_stats = event_bus.get_stats()
        assert event_stats["total_events"] > 0
        
        config_stats = config_manager.get_config_stats()
        assert config_stats["is_initialized"] == True
        
        self.logger.info("✅ 核心組件整合測試通過")
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """測試錯誤處理"""
        self.logger.info("測試錯誤處理...")
        
        # 測試事件總線錯誤處理
        event_bus = EventBus()
        await event_bus.initialize()
        
        def error_handler(event):
            raise Exception("測試錯誤")
        
        event_bus.subscribe("test.error", error_handler)
        
        # 發布事件，應該不會崩潰
        await event_bus.publish("test.error", {"test": "data"})
        await asyncio.sleep(0.1)
        
        # 驗證系統仍然正常運行
        assert event_bus.is_initialized == True
        
        # 測試配置管理器錯誤處理
        config_manager = ConfigManager()
        await config_manager.initialize()
        
        # 測試無效配置鍵
        invalid_value = config_manager.get_config("invalid.key", "default")
        assert invalid_value == "default"
        
        self.logger.info("✅ 錯誤處理測試通過")
    
    @pytest.mark.asyncio
    async def test_cleanup(self):
        """測試清理功能"""
        self.logger.info("測試清理功能...")
        
        # 創建組件
        event_bus = EventBus()
        await event_bus.initialize()
        
        config_manager = ConfigManager()
        await config_manager.initialize()
        
        module_manager = ModuleManager()
        await module_manager.initialize(event_bus, config_manager)
        
        # 測試清理
        await event_bus.cleanup()
        await config_manager.cleanup()
        await module_manager.cleanup()
        
        # 驗證清理狀態
        assert event_bus.is_initialized == False
        assert config_manager.is_initialized == False
        assert module_manager.is_initialized == False
        
        self.logger.info("✅ 清理功能測試通過")


# 運行測試
if __name__ == "__main__":
    # 設置測試環境
    logging.basicConfig(level=logging.INFO)
    
    # 創建測試實例
    test_instance = TestStage1CoreEngine()
    
    async def run_all_tests():
        """運行所有測試"""
        print("🚀 開始階段一核心引擎功能測試...")
        
        # 運行所有測試方法
        test_methods = [
            test_instance.test_event_bus_initialization,
            test_instance.test_event_bus_subscription,
            test_instance.test_event_bus_wildcard_subscription,
            test_instance.test_config_manager_initialization,
            test_instance.test_config_manager_get_set,
            test_instance.test_config_manager_plugin_config,
            test_instance.test_module_manager_initialization,
            test_instance.test_unified_bot_initialization,
            test_instance.test_unified_bot_commands,
            test_instance.test_integration_core_components,
            test_instance.test_error_handling,
            test_instance.test_cleanup
        ]
        
        passed = 0
        total = len(test_methods)
        
        for test_method in test_methods:
            try:
                test_instance.setup_method()
                await test_method()
                test_instance.teardown_method()
                passed += 1
                print(f"✅ {test_method.__name__} 通過")
            except Exception as e:
                test_instance.teardown_method()
                print(f"❌ {test_method.__name__} 失敗: {e}")
        
        print(f"\n📊 測試結果: {passed}/{total} 通過")
        
        if passed == total:
            print("🎉 階段一核心引擎功能測試全部通過！")
        else:
            print("⚠️ 部分測試失敗，請檢查錯誤信息")
    
    # 運行測試
    asyncio.run(run_all_tests()) 