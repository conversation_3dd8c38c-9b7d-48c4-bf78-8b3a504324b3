import pandas as pd
import numpy as np
import datetime as dt
import yfinance as yf
import matplotlib
matplotlib.use('Agg')  # 使用非交互式後端，避免 Tkinter 錯誤
import matplotlib.pyplot as plt
from pandas.tseries.offsets import BDay
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from email.mime.base import MIMEBase
from email import encoders
import base64
import time
from curl_cffi import requests  # 添加 curl_cffi 支援
from FlipAdamCon2 import calculate_signals
import pymysql
import sqlalchemy
from sqlalchemy import create_engine, text

matplotlib.font_manager.fontManager.addfont('TaipeiSansTCBeta-Regular.ttf')
matplotlib.rc('font', family='Taipei Sans TC Beta')

# ==========================#
# Darvas Box 相關函數
# ==========================#

def calculate_darvas_boxes(df):
    """
    計算 Darvas Box 策略的上下緣
    
    參數:
    df (DataFrame): 股票數據
    
    返回:
    list: Darvas Box 列表，每個元素包含 lower, upper, start_date, end_date
    """
    # 初始化 Box 列表來儲存所有的 Darvas Box 資料
    boxes = []
    
    # 定義 Box 的初始上下緣以及上緣的日期
    upper = None  # 上緣價格
    lower = None  # 下緣價格
    upper_date = None  # 上緣日期
    
    # 記錄高點後的最低價
    recent_low = None
    
    # 檢查每個交易日的價格來決定 Box 的上緣和下緣
    for i in range(1, len(df)):
        # 當前的調整後收盤價和日期
        close_price = df['Close'].iloc[i]
        current_date = df.index[i]
        
        # 如果已經有 Box 上緣和下緣，檢查是否創建新 Box
        if upper is not None and lower is not None:
            # 當價格超過 Box 上緣且上緣的日期在下緣之前，定義新 Box
            if close_price > upper and upper_date is not None and upper_date < current_date:
                lower = recent_low
                # 儲存新 Box 的上下緣和日期範圍
                boxes.append({'lower': lower, 'upper': upper, 'start_date': upper_date, 'end_date': current_date})
                upper = close_price
                upper_date = current_date
                recent_low = None  # 重置最近低點
            
            # 更新最近的低點，若當前價格低於前一低點
            if recent_low is None or close_price < recent_low:
                recent_low = close_price
        
        # 若尚未設定上緣或發現新高，則設定新 Box 的上緣
        elif upper is None or close_price > upper:
            upper = close_price
            upper_date = current_date
            recent_low = None
        
        # 當上緣存在且目前價格低於上緣，尋找回調的最低點
        elif close_price < upper and (recent_low is None or close_price < recent_low):
            recent_low = close_price
        
        # 當最近低點回升，並且尚未設定下緣，決定 Box 的下緣
        if recent_low is not None and close_price > recent_low and lower is None and upper_date is not None and upper_date < current_date:
            lower = recent_low
    
    return boxes

def calculate_trailing_stop_darvas(df, nATRPeriod, nATRMultip):
    """
    計算追踪止損價 (Trailing Stop) - Darvas 版本
    
    參數:
    df (DataFrame): 股票數據
    nATRPeriod (int): ATR計算的週期
    nATRMultip (float): ATR的倍數用於計算止損價
    
    返回:
    Series: 追踪止損價數據
    """
    # 計算 ATR
    high_low = df['High'] - df['Low']
    high_close = abs(df['High'] - df['Close'].shift())
    low_close = abs(df['Low'] - df['Close'].shift())
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    atr = true_range.rolling(nATRPeriod).mean()
    
    nLoss = nATRMultip * atr  # 根據ATR和倍數計算止損價
    
    xATRTrailingStop = pd.Series(index=df.index)  # 創建一個與股票數據索引一致的空Series用於存儲追踪止損價
    for i in range(1, len(df)):
        prev_close = df['Close'].iloc[i - 1]  # 前一天的調整後收盤價
        prev_stop = xATRTrailingStop.iloc[i - 1]  # 前一天的追踪止損價
        close = df['Close'].iloc[i]  # 當天的調整後收盤價
        
        if close > prev_stop and prev_close > prev_stop:
            xATRTrailingStop.iloc[i] = max(prev_stop, close - nLoss.iloc[i])  # 當價格上升時，更新追踪止損價為當前價減去nLoss
        elif close < prev_stop and prev_close < prev_stop:
            xATRTrailingStop.iloc[i] = min(prev_stop, close + nLoss.iloc[i])  # 當價格下降時，更新追踪止損價為當前價加上nLoss
        else:
            xATRTrailingStop.iloc[i] = close - nLoss.iloc[i] if close > prev_stop else close + nLoss.iloc[i]  # 根據價格變化更新追踪止損價
    return xATRTrailingStop

# 設定 curl_cffi session
def get_cffi_session():
    session = requests.Session(impersonate="chrome")
    return session

# 確保 figure 目錄存在
if not os.path.exists('figure'):
    os.makedirs('figure')

# 確保 Report 目錄存在
if not os.path.exists('Report'):
    os.makedirs('Report')

# 全域變數用於儲存分析結果
analysis_results = []

# 分別儲存不同類型的分析結果
stock_results = []
crypto_results = []
funds_results = []
index_results = []

# 資料庫連接設定
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'hB5u32G6!!',
    'database': 'adamflipdb',
    'charset': 'utf8mb4',
    'port': 3307  # 新增這一行
}

def get_database_connection():
    """建立資料庫連接"""
    try:
        engine = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        )
        return engine
    except Exception as e:
        print(f"資料庫連接失敗: {str(e)}")
        return None

def create_tables_if_not_exist():
    """建立資料表（如果不存在）"""
    engine = get_database_connection()
    if engine is None:
        return False
    
    try:
        with engine.connect() as conn:
            # 建立總體分析結果表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS adam_theory_total (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    `current_date` DATE NOT NULL,
                    current_price DECIMAL(15,6) NOT NULL,
                    reflection_date DATE NOT NULL,
                    reflection_price DECIMAL(15,6) NOT NULL,
                    price_difference DECIMAL(15,6) NOT NULL,
                    percentage_change DECIMAL(10,4) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol (symbol),
                    INDEX idx_current_date (`current_date`),
                    INDEX idx_price_difference (price_difference)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # 建立股票分析結果表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS adam_theory_stock (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    `current_date` DATE NOT NULL,
                    current_price DECIMAL(15,6) NOT NULL,
                    reflection_date DATE NOT NULL,
                    reflection_price DECIMAL(15,6) NOT NULL,
                    price_difference DECIMAL(15,6) NOT NULL,
                    percentage_change DECIMAL(10,4) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol (symbol),
                    INDEX idx_current_date (`current_date`),
                    INDEX idx_price_difference (price_difference)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # 建立加密貨幣分析結果表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS adam_theory_crypto (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    `current_date` DATE NOT NULL,
                    current_price DECIMAL(15,6) NOT NULL,
                    reflection_date DATE NOT NULL,
                    reflection_price DECIMAL(15,6) NOT NULL,
                    price_difference DECIMAL(15,6) NOT NULL,
                    percentage_change DECIMAL(10,4) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol (symbol),
                    INDEX idx_current_date (`current_date`),
                    INDEX idx_price_difference (price_difference)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # 建立基金分析結果表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS adam_theory_funds (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    `current_date` DATE NOT NULL,
                    current_price DECIMAL(15,6) NOT NULL,
                    reflection_date DATE NOT NULL,
                    reflection_price DECIMAL(15,6) NOT NULL,
                    price_difference DECIMAL(15,6) NOT NULL,
                    percentage_change DECIMAL(10,4) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol (symbol),
                    INDEX idx_current_date (`current_date`),
                    INDEX idx_price_difference (price_difference)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # 建立指數分析結果表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS adam_theory_index (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    symbol VARCHAR(50) NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    `current_date` DATE NOT NULL,
                    current_price DECIMAL(15,6) NOT NULL,
                    reflection_date DATE NOT NULL,
                    reflection_price DECIMAL(15,6) NOT NULL,
                    price_difference DECIMAL(15,6) NOT NULL,
                    percentage_change DECIMAL(10,4) NOT NULL,
                    direction VARCHAR(10) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_symbol (symbol),
                    INDEX idx_current_date (`current_date`),
                    INDEX idx_price_difference (price_difference)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            conn.commit()
            print("資料表建立成功")
            return True
    except Exception as e:
        print(f"建立資料表失敗: {str(e)}")
        return False

def insert_data_to_database(df, table_name):
    """將資料插入到指定的資料表"""
    engine = get_database_connection()
    if engine is None:
        print(f"資料庫連接失敗，跳過 {table_name} 表的資料插入")
        return False
    
    try:
        # 先清除當天的舊資料
        with engine.connect() as conn:
            today = dt.datetime.now().strftime('%Y-%m-%d')
            conn.execute(text(f"DELETE FROM {table_name} WHERE DATE(created_at) = '{today}'"))
            conn.commit()
            print(f"已清除 {table_name} 表中 {today} 的舊資料")
        
        # 插入新資料
        df.to_sql(table_name, engine, if_exists='append', index=False, method='multi')
        print(f"成功插入 {len(df)} 筆資料到 {table_name} 表")
        return True
    except Exception as e:
        print(f"插入資料到 {table_name} 表失敗: {str(e)}")
        return False

def get_symbol_name(symbol):
    """獲取標的名稱"""
    # 讀取所有CSV檔案來查找標的名稱
    try:
        # 檢查加密貨幣
        crypto_df = pd.read_csv('stock_names_coin.csv')
        if symbol in crypto_df['Symbol'].values:
            return crypto_df[crypto_df['Symbol'] == symbol]['Name'].iloc[0]
    except:
        pass
    
    try:
        # 檢查股票
        stock_df = pd.read_csv('stock_name_hold_stockonly_SortbyValue.csv')
        if symbol in stock_df['Symbol'].values:
            return stock_df[stock_df['Symbol'] == symbol]['Name'].iloc[0]
    except:
        pass
    
    try:
        # 檢查基金
        funds_df = pd.read_csv('stock_name_hold_fundsonly_SortbyValue.csv')
        if symbol in funds_df['Symbol'].values:
            return funds_df[funds_df['Symbol'] == symbol]['Name'].iloc[0]
    except:
        pass
    
    try:
        # 檢查指數
        index_df = pd.read_csv('stock_names_watch_index.csv')
        if symbol in index_df['Symbol'].values:
            return index_df[index_df['Symbol'] == symbol]['Name'].iloc[0]
    except:
        pass
    
    return symbol  # 如果找不到名稱，返回符號本身

def send_email_with_report(html_content, image_files, report_name):
    sender_email = "<EMAIL>"
    sender_name = "Ryan Lin"
    sender_password = "ahxr abjr cpiy lydz"
    receivers = ["<EMAIL>"]
    
    # 根據報告名稱設定郵件主題
    if "crypto" in report_name:
        subject = f"加密貨幣分析報告(1年日線資料) - {dt.datetime.now().strftime('%Y-%m-%d')}"
    elif "stock" in report_name:
        subject = f"股票分析報告(1年日線資料) - {dt.datetime.now().strftime('%Y-%m-%d')}"
    elif "funds" in report_name:
        subject = f"基金分析報告(1年日線資料) - {dt.datetime.now().strftime('%Y-%m-%d')}"
    elif "index" in report_name:
        subject = f"指數分析報告(1年日線資料) - {dt.datetime.now().strftime('%Y-%m-%d')}"
    else:
        subject = f"分析報告(1年日線資料) - {dt.datetime.now().strftime('%Y-%m-%d')}"
    
    # 創建郵件內容
    msg = MIMEMultipart('related')
    msg['Subject'] = subject
    msg['From'] = f'{sender_name} <{sender_email}>'
    msg['To'] = ', '.join(receivers)
    
    # 將 HTML 內容轉換為 MIME 格式
    html_part = MIMEText(html_content, 'html')
    msg.attach(html_part)
    
    # 添加圖片
    for image_file in image_files:
        with open(image_file, 'rb') as f:
            img_data = f.read()
            img = MIMEImage(img_data)
            img.add_header('Content-ID', f'<{os.path.basename(image_file)}>')
            msg.attach(img)
    

    
    try:
        # 使用 Gmail SMTP 伺服器
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        server.login(sender_email, sender_password)
        server.send_message(msg)
        server.quit()
        print(f"{subject} 郵件發送成功！")
    except Exception as e:
        print(f"{subject} 郵件發送失敗：{str(e)}")

def generate_html_report(stock_dict, report_name="analysis_report.html"):
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>分析報告</title>
        <style>
            body { font-family: 'Taipei Sans TC Beta', sans-serif; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 10px; text-align: center; }
            th { background-color: #f2f2f2; }
            img { max-width: 100%; height: auto; }
            .stock-name { font-size: 24px; font-weight: bold; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1>分析報告</h1>
        <table>
    """
    
    image_files = []
    for symbol, name in stock_dict.items():
        plot_filename = os.path.join('figure', f"{symbol}_FlipTwice_1y_1d.png")
        if os.path.exists(plot_filename):
            image_files.append(plot_filename)
            html_content += f"""
            <tr>
                <td class="stock-name">{name}</td>
            </tr>
            <tr>
                <td><img src="cid:{os.path.basename(plot_filename)}" alt="{name} 1年日線分析圖"></td>
            </tr>
            """
    
    html_content += """
        </table>
    </body>
    </html>
    """
    
    # 檢查是否有圖片檔案
    if not image_files:
        print("沒有找到任何圖片檔案，跳過郵件發送")
        return
    
    # 保存 HTML 文件
    with open(report_name, 'w', encoding='utf-8') as f:
        f.write(html_content)
    print(f"HTML 報告已生成：{report_name}")
    print(f"找到 {len(image_files)} 個圖片檔案")
    
    # 計算所有圖片檔案的總大小（MB）
    total_size = sum(os.path.getsize(f) for f in image_files) / (1024 * 1024)
    print(f"總附件大小：{total_size:.2f} MB")
    
    # 如果總大小超過 20MB，分批發送
    if total_size > 20:
        print("附件大小超過 20MB，將分批發送郵件")
        # 計算每批郵件可以包含的圖片數量
        batch_size = int(len(image_files) * (20 / total_size))
        batches = [image_files[i:i + batch_size] for i in range(0, len(image_files), batch_size)]
        
        for i, batch in enumerate(batches, 1):
            # 為每個批次生成對應的 HTML 內容
            batch_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>分析報告</title>
                <style>
                    body {{ font-family: 'Taipei Sans TC Beta', sans-serif; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th, td {{ padding: 10px; text-align: center; }}
                    th {{ background-color: #f2f2f2; }}
                    img {{ max-width: 100%; height: auto; }}
                    .stock-name {{ font-size: 24px; font-weight: bold; margin: 20px 0; }}
                </style>
            </head>
            <body>
                <h1>分析報告 (第 {i} 部分，共 {len(batches)} 部分)</h1>
                <table>
            """
            
            for image_file in batch:
                symbol = os.path.basename(image_file).split('_')[0]
                name = stock_dict.get(symbol, symbol)
                batch_html += f"""
                <tr>
                    <td class="stock-name">{name}</td>
                </tr>
                <tr>
                    <td><img src="cid:{os.path.basename(image_file)}" alt="{name} 1年日線分析圖"></td>
                </tr>
                """
            
            batch_html += """
                </table>
            </body>
            </html>
            """
            
            # 發送每個批次的郵件
            send_email_with_report(batch_html, batch, f"{report_name}_part{i}")
    else:
        # 如果總大小不超過 20MB，直接發送一封郵件
        send_email_with_report(html_content, image_files, report_name)

def getMLFlipAdambySymbol(symbol, session=None):
    # 強制自動載入所有標的字典，避免 NameError
    stock_dict = {}
    crypto_dict = {}
    funds_dict = {}
    index_dict = {}
    try:
        stock_only_df = pd.read_csv('stock_name_hold_stockonly_SortbyValue.csv')
        stock_dict = dict(zip(stock_only_df['Symbol'], stock_only_df['Name']))
    except Exception as e:
        print(f"載入 stock_dict 失敗: {e}")
    try:
        crypto_df = pd.read_csv('stock_names_coin.csv')
        crypto_dict = dict(zip(crypto_df['Symbol'], crypto_df['Name']))
    except Exception as e:
        print(f"載入 crypto_dict 失敗: {e}")
    try:
        funds_only_df = pd.read_csv('stock_name_hold_fundsonly_SortbyValue.csv')
        funds_dict = dict(zip(funds_only_df['Symbol'], funds_only_df['Name']))
    except Exception as e:
        print(f"載入 funds_dict 失敗: {e}")
    try:
        watch_index_df = pd.read_csv('stock_names_watch_index.csv')
        index_dict = dict(zip(watch_index_df['Symbol'], watch_index_df['Name']))
    except Exception as e:
        print(f"載入 index_dict 失敗: {e}")
    # ... existing code ...

    # ==========================#
    # 1. 資料抓取 (Data Acquisition) - 日線資料
    # ==========================#

    symbol = symbol
    end_date = dt.datetime.now()
    
    # 根據標的類型決定時間範圍
    # 檢查是否為加密貨幣（stock_names_coin.csv 中的標的）
    crypto_symbols = []
    try:
        crypto_df = pd.read_csv('stock_names_coin.csv')
        crypto_symbols = crypto_df['Symbol'].tolist()
    except:
        pass
    
    if symbol in crypto_symbols:
        start_date = end_date - dt.timedelta(days=365)  # 加密貨幣使用1年
        days_text = "1年"
    else:
        start_date = end_date - dt.timedelta(days=365)  # 其他標的使用1年
        days_text = "1年"

    print(f"開始下載 {symbol} 的資料...")
    print(f"時間範圍: {start_date} 到 {end_date} ({days_text})")
    print(f"使用 session: {session is not None}")

    # 添加重試機制和延遲
    max_retries = 5  # 增加重試次數
    base_delay = 2  # 增加基礎延遲時間（秒）
    
    for attempt in range(max_retries):
        try:
            print(f"嘗試下載 {symbol} (第 {attempt + 1} 次)...")
            if session:
                df = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False, session=session)
            else:
                df = yf.download(symbol, start=start_date, end=end_date, interval='1d', progress=False)
            
            print(f"下載完成，資料形狀: {df.shape}")
            print(f"資料欄位: {df.columns.tolist()}")
            print(f"資料索引範圍: {df.index[0]} 到 {df.index[-1]}")
            
            time.sleep(base_delay)  # 在每次下載後添加延遲
            break
        except Exception as e:
            print(f"下載 {symbol} 時發生錯誤: {str(e)}")
            print(f"錯誤類型: {type(e).__name__}")
            if attempt < max_retries - 1:
                wait_time = base_delay * (2 ** attempt)  # 指數退避策略
                print(f"下載 {symbol} 日線資料失敗，等待 {wait_time} 秒後重試... ({attempt + 1}/{max_retries})")
                print(f"錯誤訊息: {str(e)}")
                time.sleep(wait_time)
            else:
                print(f"警告：無法取得 {symbol} 的日線資料，跳過此標的")
                return

    # 檢查 DataFrame 是否為空
    if df.empty:
        print(f"警告：{symbol} 的資料為空，跳過此標的")
        return

    # 若欄位為 MultiIndex，扁平化
    if isinstance(df.columns, pd.MultiIndex):
        print(f"檢測到 MultiIndex 欄位，進行扁平化處理")
        print(f"原始欄位: {df.columns.tolist()}")
        # 提取第一層的欄位名稱
        df.columns = df.columns.get_level_values(0)
        print(f"扁平化後欄位: {df.columns.tolist()}")

    # 檢查必要的欄位是否存在
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"警告：{symbol} 缺少必要欄位: {missing_columns}")
        print(f"可用欄位: {df.columns.tolist()}")
        return

    df = df[required_columns].dropna()
    print(f"Downloaded {len(df)} rows of daily data for {symbol}")
    print(f"資料範圍: {df.index[0]} 到 {df.index[-1]}")
    print(f"最後5筆資料:")
    print(df.tail(5))

    # ==========================#
    # 2. 技術指標計算
    # ==========================#

    # --- ATR (14日) ---
    window_atr = 14
    high_low = df['High'] - df['Low']
    high_prevclose = (df['High'] - df['Close'].shift(1)).abs()
    low_prevclose = (df['Low'] - df['Close'].shift(1)).abs()
    df['TR'] = np.maximum.reduce([high_low, high_prevclose, low_prevclose])

    # 計算 ATR 的函數
    def atr(data, period):
        tr = data['TR']
        atr = pd.Series(index=data.index)
        atr.iloc[:period] = tr.iloc[:period].mean()
        for i in range(period, len(data)):
            atr.iloc[i] = (atr.iloc[i - 1] * (period - 1) + tr.iloc[i]) / period
        return atr

    # 計算追踪止損價的函式
    def calculate_trailing_stop(df, nATRPeriod, nATRMultip):
        xATR = atr(df, nATRPeriod)
        nLoss = nATRMultip * xATR

        xATRTrailingStop = pd.Series(index=df.index)
        for i in range(1, len(df)):
            prev_close = df['Close'].iloc[i - 1]
            prev_stop = xATRTrailingStop.iloc[i - 1]
            close = df['Close'].iloc[i]

            if close > prev_stop and prev_close > prev_stop:
                xATRTrailingStop.iloc[i] = max(prev_stop, close - nLoss.iloc[i])
            elif close < prev_stop and prev_close < prev_stop:
                xATRTrailingStop.iloc[i] = min(prev_stop, close + nLoss.iloc[i])
            else:
                xATRTrailingStop.iloc[i] = close - nLoss.iloc[i] if close > prev_stop else close + nLoss.iloc[i]
        return xATRTrailingStop

    # 計算回測報酬的函式
    def calculate_returns(df, nATRPeriod, nATRMultip):
        trailing_stop = calculate_trailing_stop(df, nATRPeriod, nATRMultip)
        position = 0  # 0: 空倉, 1: 多倉, -1: 空倉
        entry_price = 0
        returns = []

        for i in range(1, len(df)):
            close = df['Close'].iloc[i]
            prev_close = df['Close'].iloc[i - 1]

            if position == 0:  # 空倉
                if close > trailing_stop.iloc[i] and prev_close <= trailing_stop.iloc[i - 1]:
                    position = 1
                    entry_price = close
                elif close < trailing_stop.iloc[i] and prev_close >= trailing_stop.iloc[i - 1]:
                    position = -1
                    entry_price = close
            elif position == 1:  # 多倉
                if close < trailing_stop.iloc[i]:
                    returns.append((close - entry_price) / entry_price)
                    position = 0
            else:  # 空倉
                if close > trailing_stop.iloc[i]:
                    returns.append((entry_price - close) / entry_price)
                    position = 0

        return sum(returns) if returns else 0

    # ATR 參數最佳化
    # 對於日線資料，調整週期範圍
    # 原本30分鐘資料使用130-182個週期，現在改為5-30個交易日
    periods = range(5, 31, 5)  # ATR 週期範圍：5-30個交易日
    multiples = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]  # ATR 倍數範圍
    best_return = float('-inf')
    best_period = None
    best_multiple = None

    for period in periods:
        for multiple in multiples:
            returns = calculate_returns(df, period, multiple)
            if returns > best_return:
                best_return = returns
                best_period = period
                best_multiple = multiple

    print(f"最佳 ATR 週期: {best_period} 個交易日")
    print(f"最佳 ATR 倍數: {best_multiple}")
    print(f"最佳累積報酬: {best_return:.2%}")

    # 使用最佳參數計算追踪止損價
    trailing_stop = calculate_trailing_stop(df, best_period, best_multiple)

    # ==========================#
    # 3. Adam Theory：歷史走勢 + 單一反射(左右) + 雙重反射(上下)
    # ==========================#

    # 初始化變數
    pivot_index = None
    start_index = None
    pivot_price = None
    start_price = None

    # 對於日線資料，調整 lookback 參數
    # 原本30分鐘資料使用182個週期（7個交易日），現在改為60個交易日
    lookback = 60  # 60個交易日
    recent_window = df['Close'][-lookback:]

    # 尋找轉折點 - 改為使用最新收盤點
    pivot_index = df.index[-1]  # 使用最新日期
    pivot_price = df['Close'].iloc[-1]  # 使用最新收盤價
    
    # 使用最近60個交易日作為起點
    start_index = df.index[-60] if len(df) >= 60 else df.index[0]  # 60個交易日前的日期
    start_price = df.loc[start_index, 'Close']  # 60個交易日前的收盤價
    
    # 計算歷史路徑和反射路徑
    if pivot_index:
        # (A) 歷史實際走勢：從 start_index 到 pivot_index
        historical_path = df.loc[start_index:pivot_index, 'Close']
        hist_values = historical_path.values
        hist_duration = len(hist_values)

        # (B) 單一反射 (Horizontal Mirror) = 只做時間序列的左右翻轉
        once_values = hist_values[::-1]  # 左右翻轉
        # 對於日線資料，使用日頻率
        once_dates = pd.date_range(start=pivot_index + pd.Timedelta(days=1), periods=hist_duration, freq='D')
        once_reflect_path = pd.Series(data=once_values, index=once_dates)

        # (C) 雙重反射 (Vertical Mirror)
        pivot_for_vertical = pivot_price
        second_reflect_values = 2 * pivot_for_vertical - once_values
        twice_reflect_path = pd.Series(data=second_reflect_values, index=once_dates)

        # 移動平均線
        # 合併歷史價格和反射價格
        combined_prices = pd.concat([df['Close'], twice_reflect_path])
        # 計算合併後的均線（60個交易日）
        combined_sma60 = combined_prices.rolling(60).mean()
        # 分離歷史和反射部分的均線
        df['SMA60'] = combined_sma60[:len(df)]
        reflect_sma60 = combined_sma60[len(df):]

        # 計算原本歷史1年回歸線的斜率
        x_original = np.arange(len(df))
        y_original = df['Close'].values
        coefficients_original = np.polyfit(x_original, y_original, 1)
        slope_original = coefficients_original[0]

        # 合併歷史數據和雙重反射數據用於計算趨勢
        combined_data = pd.concat([historical_path, twice_reflect_path])

        # 計算(歷史1年回歸線+60天反射路徑)的回歸線斜率
        x_combined = np.arange(len(combined_data))
        y_combined = combined_data.values
        coefficients_combined = np.polyfit(x_combined, y_combined, 1)
        slope_combined = coefficients_combined[0]

        # 根據新的判斷邏輯判斷趨勢
        if slope_original > 0 and slope_combined > slope_original:
            trend_signal = "up"
        elif slope_original < 0 and slope_combined < slope_original:
            trend_signal = "down"
        elif slope_original < 0 and slope_combined > slope_original:
            trend_signal = "correction"
        elif slope_original > 0 and slope_combined < slope_original:
            trend_signal = "correction"
        else:
            trend_signal = "sideways"

        print(f"原本歷史1年回歸線斜率: {slope_original:.6f}")
        print(f"歷史+反射路徑回歸線斜率: {slope_combined:.6f}")
        print(f"Based on new trend logic, the trend signal is: {trend_signal}")
    else:
        print("No pivot point found, using default trend signal.")
        trend_signal = "sideways"

    # 計算 ATR 並加入到 DataFrame
    df['ATR'] = atr(df, best_period)

    # 計算重要轉折點
    prices_array = df['Close'].values
    local_max_idx = [i for i in range(1, len(prices_array) - 1)
                     if prices_array[i] > prices_array[i - 1] and prices_array[i] > prices_array[i + 1]]
    local_min_idx = [i for i in range(1, len(prices_array) - 1)
                     if prices_array[i] < prices_array[i - 1] and prices_array[i] < prices_array[i + 1]]
    pivots_idx = sorted(local_max_idx + local_min_idx)

    significant_pivots = []
    if pivots_idx:
        last_idx = pivots_idx[0]
        last_type = 'min' if last_idx in local_min_idx else 'max'
        significant_pivots.append(last_idx)
        for idx in pivots_idx[1:]:
            current_type = 'min' if idx in local_min_idx else 'max'
            if current_type == last_type:
                continue
            if abs(prices_array[idx] - prices_array[last_idx]) >= df['ATR'].iloc[idx]:
                significant_pivots.append(idx)
                last_idx = idx
                last_type = current_type

    # 下面就是繪圖部分
    
    # ==========================#
    # 4. Darvas Box 計算和繪製
    # ==========================#
    

    
    plt.figure(figsize=(24, 13.5), dpi=300)  # 提高解析度：尺寸從16x9改為24x13.5，DPI從預設改為300
    plt.plot(df.index, df['Close'], label='Stock Price', color='black')
    plt.plot(historical_path.index, hist_values, label='Historical Path', color='blue', linewidth=2)
    plt.plot(twice_reflect_path.index, twice_reflect_path.values,
             label='Double Reflection', color='green', linestyle='--', linewidth=2)
    plt.plot(df.index, df['SMA60'], label='60MA', color='brown', linewidth=1.5)  # 歷史部分的均線
    plt.plot(twice_reflect_path.index, reflect_sma60, label='60MA (Reflection)', 
             color='orange', linestyle='--', linewidth=1.5)  # 反射部分的均線



    plt.axvline(pivot_index, color='gray', linestyle='--', alpha=0.7, label='Pivot Line')
    plt.scatter(pivot_index, pivot_price, color='orange', marker='o', s=100, label='Pivot Point')

    # 這裡維持你原本的回歸線與標準差線畫法
    x = np.arange(len(df))
    y = df['Close'].values
    coefficients = np.polyfit(x, y, 1)
    trend_line = np.polyval(coefficients, x)
    std_dev = np.std(y - trend_line)

    # 繪製趨勢線和標準差帶
    plt.plot(df.index, trend_line, color='purple', linestyle='-', label='Trend Line')

    # 添加標準差帶的底色（移除圖例標籤）
    plt.fill_between(df.index, trend_line + std_dev, trend_line - std_dev, color='white', alpha=0.2)
    plt.fill_between(df.index, trend_line + 2 * std_dev, trend_line + std_dev, color='lightgreen', alpha=0.2)
    plt.fill_between(df.index, trend_line - std_dev, trend_line - 2 * std_dev, color='lightcoral', alpha=0.2)
    plt.fill_between(df.index, trend_line + 3 * std_dev, trend_line + 2 * std_dev, color='green', alpha=0.2)
    plt.fill_between(df.index, trend_line - 2 * std_dev, trend_line - 3 * std_dev, color='red', alpha=0.2)

    # 繪製標準差線（移除圖例標籤）
    plt.plot(df.index, trend_line + std_dev, color='lightgreen', linestyle='--')
    plt.plot(df.index, trend_line - std_dev, color='lightcoral', linestyle='--')
    plt.plot(df.index, trend_line + 2 * std_dev, color='green', linestyle='--')
    plt.plot(df.index, trend_line - 2 * std_dev, color='red', linestyle='--')
    plt.plot(df.index, trend_line + 3 * std_dev, color='darkgreen', linestyle='--')
    plt.plot(df.index, trend_line - 3 * std_dev, color='darkred', linestyle='--')

    # 計算延伸到二次反射線圖的追踪止損線
    extended_trailing_stop = pd.Series(index=twice_reflect_path.index)
    last_stop = trailing_stop.iloc[-1]
    last_close = df['Close'].iloc[-1]

    for i in range(len(twice_reflect_path)):
        current_price = twice_reflect_path.iloc[i]
        if i == 0:
            extended_trailing_stop.iloc[i] = last_stop
        else:
            prev_stop = extended_trailing_stop.iloc[i - 1]
            if current_price > prev_stop:
                extended_trailing_stop.iloc[i] = max(prev_stop, current_price - best_multiple * df['ATR'].iloc[-1])
            else:
                extended_trailing_stop.iloc[i] = min(prev_stop, current_price + best_multiple * df['ATR'].iloc[-1])

    # 繪製追踪止損價（包括延伸部分）
    plt.plot(df.index, trailing_stop, color='gray', linestyle='--', label='Trailing Stop')
    plt.plot(twice_reflect_path.index, extended_trailing_stop, color='gray', linestyle='--')

    # 檢測突破和跌破點（包括延伸部分）
    for i in range(1, len(df)):
        prev_close = df['Close'].iloc[i - 1]
        curr_close = df['Close'].iloc[i]
        prev_stop = trailing_stop.iloc[i - 1]
        curr_stop = trailing_stop.iloc[i]

        # 檢測向上突破
        if prev_close <= prev_stop and curr_close > curr_stop:
            plt.scatter(df.index[i], curr_close, color='green', marker='.', s=50, label='突破點' if i == 1 else "")
            plt.annotate(f'{curr_close:.2f}',
                         (df.index[i], curr_close),
                         xytext=(2, 2),
                         textcoords='offset points',
                         color='green',
                         fontsize=10)

        # 檢測向下跌破
        elif prev_close >= prev_stop and curr_close < curr_stop:
            plt.scatter(df.index[i], curr_close, color='red', marker='.', s=50, label='跌破點' if i == 1 else "")
            plt.annotate(f'{curr_close:.2f}',
                         (df.index[i], curr_close),
                         xytext=(2, -8),
                         textcoords='offset points',
                         color='red',
                         fontsize=10)

    # 檢測延伸部分的突破和跌破點
    for i in range(1, len(twice_reflect_path)):
        prev_price = twice_reflect_path.iloc[i - 1]
        curr_price = twice_reflect_path.iloc[i]
        prev_stop = extended_trailing_stop.iloc[i - 1]
        curr_stop = extended_trailing_stop.iloc[i]

        # 檢測向上突破
        if prev_price <= prev_stop and curr_price > curr_stop:
            plt.scatter(twice_reflect_path.index[i], curr_price, color='green', marker='.', s=50)
            plt.annotate(f'{curr_price:.2f}',
                         (twice_reflect_path.index[i], curr_price),
                         xytext=(2, 2),
                         textcoords='offset points',
                         color='green',
                         fontsize=10)

        # 檢測向下跌破
        elif prev_price >= prev_stop and curr_price < curr_stop:
            plt.scatter(twice_reflect_path.index[i], curr_price, color='red', marker='.', s=50)
            plt.annotate(f'{curr_price:.2f}',
                         (twice_reflect_path.index[i], curr_price),
                         xytext=(2, -8),
                         textcoords='offset points',
                         color='red',
                         fontsize=10)

    # 修改圖例位置為左上方
    plt.legend(loc='upper left', bbox_to_anchor=(0.0, 1.0))

    # ==========================#
    # 4. 交易策略回測與勝率計算
    # ==========================#

    wins = 0
    trades = 0
    for i in range(len(significant_pivots) - 1):
        s_idx = significant_pivots[i]
        e_idx = significant_pivots[i + 1]
        if s_idx in local_min_idx and e_idx in local_max_idx:
            entry_price = prices_array[s_idx]
            exit_price = prices_array[e_idx]
            profit = exit_price - entry_price
        elif s_idx in local_max_idx and e_idx in local_min_idx:
            entry_price = prices_array[s_idx]
            exit_price = prices_array[e_idx]
            profit = entry_price - exit_price
        else:
            continue
        trades += 1
        if profit > 0:
            wins += 1

    # 計算反射路徑最後一個價位與目前價位的差異
    current_price = df['Close'].iloc[-1]  # 今日價位
    current_date = df.index[-1]  # 今日日期
    
    # 根據 trend_signal 選擇對應的目標價位
    if trend_signal == "up":
        # 上漲趨勢，使用反射路徑最高價
        reflection_target_price = max(second_reflect_values)
        target_index = np.argmax(second_reflect_values)
        reflection_direction = "上漲趨勢-最高價"
    elif trend_signal == "down":
        # 下跌趨勢，使用反射路徑最低價
        reflection_target_price = min(second_reflect_values)
        target_index = np.argmin(second_reflect_values)
        reflection_direction = "下跌趨勢-最低價"
    elif trend_signal == "correction":
        # 修正趨勢，使用反射路徑最後一個價格
        reflection_target_price = second_reflect_values[-1]
        target_index = len(second_reflect_values) - 1
        reflection_direction = "修正趨勢-最後價"
    else:
        # sideways 或其他情況，使用反射路徑最後一個價格
        reflection_target_price = second_reflect_values[-1]
        target_index = len(second_reflect_values) - 1
        reflection_direction = "橫盤趨勢-最後價"
    
    reflection_target_date = twice_reflect_path.index[target_index]
    
    # 計算價差和百分比
    price_difference = reflection_target_price - current_price
    price_ratio = reflection_target_price / current_price
    percentage_change = (price_ratio - 1) * 100
    
    # 判斷漲跌方向
    if price_difference > 0:
        direction = "漲"
    elif price_difference < 0:
        direction = "跌"
    else:
        direction = "持平"
    
    if trades > 0:
        win_rate = wins / trades
        text_info = [
            f"Based on regression slope, the trend signal is: {trend_signal}",
            f"Historical Path start: {start_index.date()}, price={start_price:.2f}",
            f"Pivot index: {pivot_index.date()}, price={pivot_price:.2f}",
            f"Double Reflection Path: Start={second_reflect_values[0]:.2f}, End={second_reflect_values[-1]:.2f}",
            f"價差計算方式: {reflection_direction}",
            f"今日: {current_date.date()} {current_price:.2f} | 反射目標: {reflection_target_date.date()} {reflection_target_price:.2f} | 價差: {price_difference:.2f} ({direction} {abs(percentage_change):.2f}%)"
        ]
    else:
        text_info = [
            f"Based on regression slope, the trend signal is: {trend_signal}",
            f"Historical Path start: {start_index.date()}, price={start_price:.2f}",
            f"Pivot index: {pivot_index.date()}, price={pivot_price:.2f}",
            f"Double Reflection Path: Start={second_reflect_values[0]:.2f}, End={second_reflect_values[-1]:.2f}",
            "No significant pivot-based signals found for backtesting.",
            f"價差計算方式: {reflection_direction}",
            f"今日: {current_date.date()} {current_price:.2f} | 反射目標: {reflection_target_date.date()} {reflection_target_price:.2f} | 價差: {price_difference:.2f} ({direction} {abs(percentage_change):.2f}%)"
        ]

    # 在圖的左下方顯示文本
    y_text = 0.3
    for info in text_info:
        plt.text(0.02, y_text, info, transform=plt.gca().transAxes, ha='left', va='top', fontsize=12)
        y_text -= 0.05

    # 使用 FlipAdamCon2 的原始判斷條件
    df = calculate_signals(df, N=30)  # 將突破週期改為30天
    
    # 添加調試信息
    print(f"\n=== {symbol} 信號統計 ===")
    print(f"做多訊號數量: {len(df[df['entry_long']])}")
    print(f"做空訊號數量: {len(df[df['entry_short']])}")
    print(f"跳空訊號數量: {len(df[df['entry_gap']])}")
    print("使用原始參數設定：")
    print("- 突破：30天")  # 更新顯示的突破週期
    print("- 趨勢：60天 SMA + 10天新高/新低")
    print("- 跳空：20天 ATR, 1.5倍")
    
    # 檢查跳空訊號的具體數據
    gap_signals = df[df['entry_gap']]
    if not gap_signals.empty:
        print("\n跳空訊號詳情:")
        print(gap_signals[['Open', 'High', 'Low', 'Close', 'ATR20', 'range']].tail())
    else:
        print("\n沒有找到跳空訊號")

    # 標記做多訊號
    # 找出同時出現的訊號
    both_signals = df[df['breakout_long'] & df['trend_long']]
    # 找出只有突破的訊號
    only_breakout = df[df['breakout_long'] & ~df['trend_long']]
    # 找出只有趨勢的訊號
    only_trend = df[~df['breakout_long'] & df['trend_long']]

    # 標記同時出現的訊號（綠色大"B"）
    plt.scatter(both_signals.index, both_signals['Close'], 
               color='green', marker='$B$', s=200, label='上方突破', alpha=0.7)

    # 標記只有突破的訊號（紅色大"B"）
    plt.scatter(only_breakout.index, only_breakout['Close'], 
               color='red', marker='$B$', s=200, label='下方突破', alpha=0.7)

    # 標記趨勢信號
    # 上漲趨勢（綠色大往上"T"）
    trend_up_signals = df[df['trend_long']]
    plt.scatter(trend_up_signals.index, trend_up_signals['Close'], 
               color='green', marker='$T$', s=200, label='上漲趨勢', alpha=0.7)
    
    # 下跌趨勢（紅色大往下"T"）
    trend_down_signals = df[df['trend_short']]
    plt.scatter(trend_down_signals.index, trend_down_signals['Close'], 
               color='red', marker='$T$', s=200, label='下跌趨勢', alpha=0.7)

    # 標記跳空訊號（紫色大猩猩）
    gap_signals = df[df['entry_gap']]
    plt.scatter(gap_signals.index, gap_signals['Close'], 
               color='purple', marker='$G$', s=300, label='跳空信號', alpha=0.7)



    plt.title(f"{symbol} Adam Theory ({days_text}日線資料)", fontsize=16, fontweight='bold')
    plt.xlabel("Date", fontsize=14)
    plt.ylabel("Price", fontsize=14)
    plt.legend(loc='upper left', bbox_to_anchor=(0.0, 1.0), fontsize=12)
    
    # 調整軸刻度標籤的字體大小
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)
    
    # 設定圖片尺寸和 DPI，確保符合 Telegram 限制
    plt.gcf().set_size_inches(12, 8)  # 設定圖片尺寸為 12x8 英寸
    
    plt.tight_layout()
    
    # 記得把圖另存檔案
    plot_filename = os.path.join('figure', f"{symbol}_FlipTwice_1y_1d.png")
    if os.path.exists(plot_filename):
        os.remove(plot_filename)
    plt.savefig(plot_filename, dpi=150, bbox_inches='tight', pad_inches=0.1, 
                facecolor='white', edgecolor='none')
    print(f"Plot saved as {plot_filename}")
    
    # 記錄分析結果到全域變數
    global analysis_results, stock_results, crypto_results, funds_results, index_results
    
    result_data = {
        'symbol': symbol,
        'name': get_symbol_name(symbol),
        'trade_date': current_date.date(),
        'current_price': current_price,
        'reflection_date': reflection_target_date.date(),
        'reflection_price': reflection_target_price,
        'price_difference': price_difference,
        'percentage_change': percentage_change,
        'direction': direction
    }
    
    analysis_results.append(result_data)
    
    # 根據標的類型分別儲存
    if symbol in stock_dict:
        stock_results.append(result_data)
    elif symbol in crypto_dict:
        crypto_results.append(result_data)
    elif symbol in funds_dict:
        funds_results.append(result_data)
    elif symbol in index_dict:
        index_results.append(result_data)
    
    return plot_filename

if __name__ == "__main__":
    # 初始化資料庫
    print("初始化資料庫...")
    db_available = create_tables_if_not_exist()
    if not db_available:
        print("資料庫連接失敗，將跳過資料庫操作，僅生成CSV檔案")
    else:
        print("資料庫連接成功，將同時生成CSV檔案和存入資料庫")
    
    # 清除 figure 目錄中的所有 PNG 檔案
    if os.path.exists('figure'):
        for file in os.listdir('figure'):
            if file.endswith('.png'):
                os.remove(os.path.join('figure', file))
        print("已清除 figure 目錄中的所有 PNG 檔案")
    else:
        os.makedirs('figure')
        print("已建立 figure 目錄")
    
    # 檢查並清除一週前的 Report 檔案
    if os.path.exists('Report'):
        current_time = dt.datetime.now()
        week_ago = current_time - dt.timedelta(days=7)
        
        files_to_remove = []
        for file in os.listdir('Report'):
            if file.endswith('.csv'):
                file_path = os.path.join('Report', file)
                file_time = dt.datetime.fromtimestamp(os.path.getctime(file_path))
                if file_time < week_ago:
                    files_to_remove.append(file_path)
        
        if files_to_remove:
            for file_path in files_to_remove:
                os.remove(file_path)
                print(f"已清除一週前的檔案: {os.path.basename(file_path)}")
            print(f"共清除 {len(files_to_remove)} 個一週前的檔案")
        else:
            print("沒有需要清除的一週前檔案")
    else:
        os.makedirs('Report')
        print("已建立 Report 目錄")
    
    # 重置分析結果
    analysis_results = []
    stock_results = []
    crypto_results = []
    funds_results = []
    index_results = []
    
    # 創建 curl_cffi session
    session = get_cffi_session()
    
    # 讀取所有 CSV 檔案
    stock_df = pd.read_csv('stock_names_coin.csv')
    stock_only_df = pd.read_csv('stock_name_hold_stockonly_SortbyValue.csv')
    funds_only_df = pd.read_csv('stock_name_hold_fundsonly_SortbyValue.csv')
    watch_index_df = pd.read_csv('stock_names_watch_index.csv')
    
    # 設定批次處理參數
    batch_size = 5  # 每批處理的數量
    batch_delay = 5  # 每批之間的延遲時間（秒）
    
    # 處理股票
    print("\n處理股票...")
    stock_dict = dict(zip(stock_only_df['Symbol'], stock_only_df['Name']))
    stock_symbols = list(stock_dict.keys())
    stock_batches = [stock_symbols[i:i + batch_size] for i in range(0, len(stock_symbols), batch_size)]
    
    for batch_idx, batch in enumerate(stock_batches, 1):
        print(f"\n處理第 {batch_idx} 批（共 {len(stock_batches)} 批）...")
        for symbol in batch:
            print(f"處理 {symbol}...")
            getMLFlipAdambySymbol(symbol, session=session)
        
        if batch_idx < len(stock_batches):
            print(f"等待 {batch_delay} 秒後處理下一批...")
            time.sleep(batch_delay)
    
    # 檢查哪些股票成功生成了圖片
    successful_stocks = {}
    for symbol, name in stock_dict.items():
        plot_filename = os.path.join('figure', f"{symbol}_FlipTwice_1y_1d.png")
        if os.path.exists(plot_filename):
            successful_stocks[symbol] = name
    
    if successful_stocks:
        generate_html_report(successful_stocks,'stock')
    else:
        print("沒有股票圖片可發送郵件")
    
    # 處理加密貨幣
    print("處理加密貨幣...")
    crypto_dict = dict(zip(stock_df['Symbol'], stock_df['Name']))
    
    # 將股票列表分成多個批次
    crypto_symbols = list(crypto_dict.keys())
    crypto_batches = [crypto_symbols[i:i + batch_size] for i in range(0, len(crypto_symbols), batch_size)]
    
    for batch_idx, batch in enumerate(crypto_batches, 1):
        print(f"\n處理第 {batch_idx} 批（共 {len(crypto_batches)} 批）...")
        for symbol in batch:
            print(f"處理 {symbol}...")
            getMLFlipAdambySymbol(symbol, session=session)
        
        # 如果不是最後一批，則等待
        if batch_idx < len(crypto_batches):
            print(f"等待 {batch_delay} 秒後處理下一批...")
            time.sleep(batch_delay)
    
    # 檢查哪些加密貨幣成功生成了圖片
    successful_crypto = {}
    for symbol, name in crypto_dict.items():
        plot_filename = os.path.join('figure', f"{symbol}_FlipTwice_1y_1d.png")
        if os.path.exists(plot_filename):
            successful_crypto[symbol] = name
    
    if successful_crypto:
        generate_html_report(successful_crypto,'crypto')
    else:
        print("沒有加密貨幣圖片可發送郵件")
    
    # 處理基金
    print("\n處理基金...")
    funds_dict = dict(zip(funds_only_df['Symbol'], funds_only_df['Name']))
    funds_symbols = list(funds_dict.keys())
    funds_batches = [funds_symbols[i:i + batch_size] for i in range(0, len(funds_symbols), batch_size)]
    
    for batch_idx, batch in enumerate(funds_batches, 1):
        print(f"\n處理第 {batch_idx} 批（共 {len(funds_batches)} 批）...")
        for symbol in batch:
            print(f"處理 {symbol}...")
            getMLFlipAdambySymbol(symbol, session=session)
        
        if batch_idx < len(funds_batches):
            print(f"等待 {batch_delay} 秒後處理下一批...")
            time.sleep(batch_delay)
    
    # 檢查哪些基金成功生成了圖片
    successful_funds = {}
    for symbol, name in funds_dict.items():
        plot_filename = os.path.join('figure', f"{symbol}_FlipTwice_1y_1d.png")
        if os.path.exists(plot_filename):
            successful_funds[symbol] = name
    
    if successful_funds:
        generate_html_report(successful_funds,'funds')
    else:
        print("沒有基金圖片可發送郵件")
    
    # 處理指數
    print("\n處理指數...")
    index_dict = dict(zip(watch_index_df['Symbol'], watch_index_df['Name']))
    index_symbols = list(index_dict.keys())
    index_batches = [index_symbols[i:i + batch_size] for i in range(0, len(index_symbols), batch_size)]
    
    for batch_idx, batch in enumerate(index_batches, 1):
        print(f"\n處理第 {batch_idx} 批（共 {len(index_batches)} 批）...")
        for symbol in batch:
            print(f"處理 {symbol}...")
            getMLFlipAdambySymbol(symbol, session=session)
        
        if batch_idx < len(index_batches):
            print(f"等待 {batch_delay} 秒後處理下一批...")
            time.sleep(batch_delay)
    
    # 檢查哪些指數成功生成了圖片
    successful_index = {}
    for symbol, name in index_dict.items():
        plot_filename = os.path.join('figure', f"{symbol}_FlipTwice_1y_1d.png")
        if os.path.exists(plot_filename):
            successful_index[symbol] = name
    
    if successful_index:
        generate_html_report(successful_index,'index')
    else:
        print("沒有指數圖片可發送郵件")
    
    # 生成分別的CSV檔案
    today = dt.datetime.now().strftime('%Y-%m-%d')
    
    # 生成股票CSV檔案
    if stock_results:
        df_stock = pd.DataFrame(stock_results)
        df_stock = df_stock.sort_values('price_difference', ascending=False)
        df_stock = df_stock[['symbol', 'name', 'trade_date', 'current_price', 
                            'reflection_date', 'reflection_price', 'price_difference', 
                            'percentage_change', 'direction']]
        
        # 準備資料庫插入用的資料（保持英文欄位名稱）
        df_stock_db = df_stock.copy()
        
        # 生成CSV檔案（中文欄位名稱）
        df_stock_csv = df_stock.copy()
        df_stock_csv.columns = ['標的代碼', '標的名稱', '今日日期', '今日價位', 
                               '反射目標日期', '反射目標價位', '價差', '漲跌幅(%)', '方向']
        stock_csv_filename = os.path.join('Report', f"Adam_Theory_Stock_{today}.csv")
        df_stock_csv.to_csv(stock_csv_filename, index=False, encoding='utf-8-sig')
        print(f"股票分析結果已儲存至: {stock_csv_filename}")
        print(f"共分析 {len(df_stock)} 個股票")
        
        # 插入資料到資料庫
        if db_available:
            if insert_data_to_database(df_stock_db, 'adam_theory_stock'):
                print("股票分析結果已成功存入資料庫")
            else:
                print("股票分析結果存入資料庫失敗")
        else:
            print("資料庫不可用，跳過股票分析結果的資料庫操作")
        
        # 顯示股票前10名和後10名
        print("\n=== 股票價差最大的前10名 ===")
        print(df_stock_csv.head(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
        print("\n=== 股票價差最小的後10名 ===")
        print(df_stock_csv.tail(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
    
    # 生成加密貨幣CSV檔案
    if crypto_results:
        df_crypto = pd.DataFrame(crypto_results)
        df_crypto = df_crypto.sort_values('price_difference', ascending=False)
        df_crypto = df_crypto[['symbol', 'name', 'trade_date', 'current_price', 
                              'reflection_date', 'reflection_price', 'price_difference', 
                              'percentage_change', 'direction']]
        
        # 準備資料庫插入用的資料（保持英文欄位名稱）
        df_crypto_db = df_crypto.copy()
        
        # 生成CSV檔案（中文欄位名稱）
        df_crypto_csv = df_crypto.copy()
        df_crypto_csv.columns = ['標的代碼', '標的名稱', '今日日期', '今日價位', 
                                '反射目標日期', '反射目標價位', '價差', '漲跌幅(%)', '方向']
        crypto_csv_filename = os.path.join('Report', f"Adam_Theory_Crypto_{today}.csv")
        df_crypto_csv.to_csv(crypto_csv_filename, index=False, encoding='utf-8-sig')
        print(f"加密貨幣分析結果已儲存至: {crypto_csv_filename}")
        print(f"共分析 {len(df_crypto)} 個加密貨幣")
        
        # 插入資料到資料庫
        if db_available:
            if insert_data_to_database(df_crypto_db, 'adam_theory_crypto'):
                print("加密貨幣分析結果已成功存入資料庫")
            else:
                print("加密貨幣分析結果存入資料庫失敗")
        else:
            print("資料庫不可用，跳過加密貨幣分析結果的資料庫操作")
        
        # 顯示加密貨幣前10名和後10名
        print("\n=== 加密貨幣價差最大的前10名 ===")
        print(df_crypto_csv.head(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
        print("\n=== 加密貨幣價差最小的後10名 ===")
        print(df_crypto_csv.tail(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
    
    # 生成基金CSV檔案
    if funds_results:
        df_funds = pd.DataFrame(funds_results)
        df_funds = df_funds.sort_values('price_difference', ascending=False)
        df_funds = df_funds[['symbol', 'name', 'trade_date', 'current_price', 
                            'reflection_date', 'reflection_price', 'price_difference', 
                            'percentage_change', 'direction']]
        
        # 準備資料庫插入用的資料（保持英文欄位名稱）
        df_funds_db = df_funds.copy()
        
        # 生成CSV檔案（中文欄位名稱）
        df_funds_csv = df_funds.copy()
        df_funds_csv.columns = ['標的代碼', '標的名稱', '今日日期', '今日價位', 
                               '反射目標日期', '反射目標價位', '價差', '漲跌幅(%)', '方向']
        funds_csv_filename = os.path.join('Report', f"Adam_Theory_Funds_{today}.csv")
        df_funds_csv.to_csv(funds_csv_filename, index=False, encoding='utf-8-sig')
        print(f"基金分析結果已儲存至: {funds_csv_filename}")
        print(f"共分析 {len(df_funds)} 個基金")
        
        # 插入資料到資料庫
        if db_available:
            if insert_data_to_database(df_funds_db, 'adam_theory_funds'):
                print("基金分析結果已成功存入資料庫")
            else:
                print("基金分析結果存入資料庫失敗")
        else:
            print("資料庫不可用，跳過基金分析結果的資料庫操作")
        
        # 顯示基金前10名和後10名
        print("\n=== 基金價差最大的前10名 ===")
        print(df_funds_csv.head(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
        print("\n=== 基金價差最小的後10名 ===")
        print(df_funds_csv.tail(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
    
    # 生成指數CSV檔案
    if index_results:
        df_index = pd.DataFrame(index_results)
        df_index = df_index.sort_values('price_difference', ascending=False)
        df_index = df_index[['symbol', 'name', 'trade_date', 'current_price', 
                            'reflection_date', 'reflection_price', 'price_difference', 
                            'percentage_change', 'direction']]
        
        # 準備資料庫插入用的資料（保持英文欄位名稱）
        df_index_db = df_index.copy()
        
        # 生成CSV檔案（中文欄位名稱）
        df_index_csv = df_index.copy()
        df_index_csv.columns = ['標的代碼', '標的名稱', '今日日期', '今日價位', 
                               '反射目標日期', '反射目標價位', '價差', '漲跌幅(%)', '方向']
        index_csv_filename = os.path.join('Report', f"Adam_Theory_Index_{today}.csv")
        df_index_csv.to_csv(index_csv_filename, index=False, encoding='utf-8-sig')
        print(f"指數分析結果已儲存至: {index_csv_filename}")
        print(f"共分析 {len(df_index)} 個指數")
        
        # 插入資料到資料庫
        if db_available:
            if insert_data_to_database(df_index_db, 'adam_theory_index'):
                print("指數分析結果已成功存入資料庫")
            else:
                print("指數分析結果存入資料庫失敗")
        else:
            print("資料庫不可用，跳過指數分析結果的資料庫操作")
        
        # 顯示指數前10名和後10名
        print("\n=== 指數價差最大的前10名 ===")
        print(df_index_csv.head(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
        print("\n=== 指數價差最小的後10名 ===")
        print(df_index_csv.tail(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
    
    # 生成總體CSV檔案
    if analysis_results:
        df_total = pd.DataFrame(analysis_results)
        df_total = df_total.sort_values('price_difference', ascending=False)
        df_total = df_total[['symbol', 'name', 'trade_date', 'current_price', 
                            'reflection_date', 'reflection_price', 'price_difference', 
                            'percentage_change', 'direction']]
        
        # 準備資料庫插入用的資料（保持英文欄位名稱）
        df_total_db = df_total.copy()
        
        # 生成CSV檔案（中文欄位名稱）
        df_total_csv = df_total.copy()
        df_total_csv.columns = ['標的代碼', '標的名稱', '今日日期', '今日價位', 
                               '反射目標日期', '反射目標價位', '價差', '漲跌幅(%)', '方向']
        total_csv_filename = os.path.join('Report', f"Adam_Theory_Total_{today}.csv")
        df_total_csv.to_csv(total_csv_filename, index=False, encoding='utf-8-sig')
        print(f"總體分析結果已儲存至: {total_csv_filename}")
        print(f"共分析 {len(df_total)} 個標的")
        
        # 插入資料到資料庫
        if db_available:
            if insert_data_to_database(df_total_db, 'adam_theory_total'):
                print("總體分析結果已成功存入資料庫")
            else:
                print("總體分析結果存入資料庫失敗")
        else:
            print("資料庫不可用，跳過總體分析結果的資料庫操作")
        
        # 顯示總體前10名和後10名
        print("\n=== 總體價差最大的前10名 ===")
        print(df_total_csv.head(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
        print("\n=== 總體價差最小的後10名 ===")
        print(df_total_csv.tail(10)[['標的代碼', '標的名稱', '價差', '漲跌幅(%)', '方向']])
    
    else:
        print("沒有分析結果可生成CSV檔案")