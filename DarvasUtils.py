import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from email.utils import formataddr
from io import BytesIO
import matplotlib
import Utils.Stock as us
import Utils.Stock_Names as stockNames

matplotlib.use('Agg')  # 使用非交互式後端，避免 Tkinter 錯誤

# 股票代碼與名稱對照表
stock_names = {
    'GC=F': '黃金期貨',
    'BTC-USD':'',
    'BNB-USD':'',
    'SOL-USD':'',
    '^GSPC': '標普500',
    'NQ=F': '那斯達克100',
    #'^IXIC': 'NASDAQ',
    'FNG=F': 'NYSE尖牙',
    '^DJI': '道瓊工業',
    '^RUT': '羅素2000',
    'MGK': '美國成長股ETF',
    #'MGV': '美國價值股ETF',
    #'VYM': '美高股息ETF',
    'TSLA': 'TESLA特斯拉公司',
    'AAPL': 'APPLE蘋果公司',
    'META': 'Meta平台公司',
    'NFLX': 'NETFLIX網飛公司',
    'AMZN': 'AMAZON亞馬遜公司',
    'MSFT': 'MICROSOFT微軟公司',
    'GOOGL': 'Alphabet公司',
    'SNOW': 'Snowflake公司',
    #'AVGO': 'BROADCOM LTD Broadcom公司',
    #'TXN': 'Texas Instruments Inc 德州儀器',
    #'INTC': 'Intel 英特爾',
    'NVDA': 'NVIDIA CORP 輝達公司',
    #'AMD': 'ADV MICRO DEVICES 超微半導體',
    #'QCOM': 'Qualcomm 高通',
    #'KLAC': 'KLA CP CMN STK 柯磊',
    #'ADI': 'ANALOG DEVICES 亞德諾',
    'TSM': 'Taiwan Semicond Ads 台積電',
    #'ASML': 'ASML HLDG NY REG 艾司摩爾',
    '^SOX': '費城半導體',
    '^TWII': '台灣加權',
    #'^KS11': '南韓指數',
    #'^N225': '日經225',
    #'^BSESN': '印度',
    '^VIX': '恐慌指數',
    #'^FTSE': '英國FTSE100指數',
    #'^GDAXI': '德國DAX指數',
    #'^FCHI': '法國CAC指數',
    #'^HSI': '恒生指數',
    #'000001.SS': '上證綜合指數',
    'TLT': '20年期以上美國公債ETF',
    'LQD':'投資等級公司債ETF',
    'HYG': '高收益債ETF',
    #'CWB': '可轉債ETF',
    'EMB': '新興市場美元債ETF',
    #'LEMB': '新興市場本地貨幣債ETF',
    '00679B.TWO': '元大美債20年',
    #'00933B.TWO': '國泰10Y+金融債',
    #'00937B.TWO': '群益ESG投等債20+',
    '00647L.TW': '元大S&P500正2',
    '00631L.TW': '元大台灣50正2',
    '00675L.TW': '富邦臺灣加權正2',
    #'00924.TW': '復華S&P500成長',
    '00646.TW': '元大S&P500',
    '00757.TW': '統一FANG+',
    '00662.TW': '富邦NASDAQ',
    '00830.TW': '國泰費城半導體',
    #'00733.TW': '富邦臺灣中小',
    #'2881.TW': '富邦金',
    '2891.TW': '中信金',
    '2330.TW': '台積電',
    #'8099.TWO': '大世科',
    #'2371.TW': '大同',
    '0050.TW': '元大台灣50',
    '00878.TW': '國泰永續高股息',
    '0056.TW': '元大高股息',
    '00713.TW': '元大台灣高息低波',
    '0P00006A9V.TW': '統一黑馬基金',
    '0P00006AA2.TW': '統一奔騰基金',
    '0P00009PAQ.TW': '統一台灣動力基金',
    '0P00006AII.TW': '復華高成長基金',
    '0P00017WW7.TW': '統一全球新科技基金(新台幣)',
    '0P0001F93E.TW': '富邦AI智能新趨勢多重資產型基金-A類型(新臺幣)',
    '0P00006ACG.TW': '元大台灣加權股價指數基金-新台幣A類型',
    '0P0000TIQ9.TW': '群益印度中小基金-新台幣',
    '0P00006AKS.TW': '野村優質基金',
    '0P00006AKV.TW': '野村中小基金',
    '0P00006AKU.TW': '野村平衡基金',
    '0P00006AHA.TW': '野村高科技基金',
    '0P00006AGC.TW': '野村高股息',
    '0P00006AD2.TW': '野村鴻運基金',
    '0P00006AL0.TW': '安聯台灣大壩基金-A類型-新臺幣',
    '0P00006A9Z.TW': '安聯台灣科技基金',
    '0P00000I49': '聯博-國際科技基金 A股美元',
    '0P00000M7O': '聯博-美國成長基金 A股美元',
    '0P0000UQR6': '聯博-精選美國股票基金A美元',
    '0P0000YSWD': '法巴美國增長股票基金C(美元)',
    '0P0000YSO8': '法巴科技創新股票基金C(美元)',
    '0P000019CB': '摩根基金-JPM美國企業成長(美元)-A股(累計)',
    '0P000019AY': '摩根基金-JPM美國價值(美元)-A股(累計)',
    '0P000019D5': '摩根基金-JPM美國(美元)-A股(累計)',
    '0P000019C5': '摩根基金-JPM美國科技(美元)-A股(累計)',
    '0P00000AWU': '貝萊德世界科技基金A2美元',
    '0P00000AWD': '貝萊德美國增長型基金A2美元',
    '0P0000Z39P': '貝萊德日本靈活股票基金 Hedged A2 美元',
    '0P00000EBQ': '駿利亨德森遠見全球科技領先基金',
    '0P00000LVZ': '鋒裕匯理美國鋒裕股票A美元',
    '0P00000CQF': '駿利亨德森美國價值中小基金 A2 美元',
    '0P00013N57': '路博邁投資基金 - NB美國房地產基金T累積類股(美元)',
    '0P00000S71': '普徠仕美國大型成長股票基金A級別(美元)',
    '0P000090OL':'安聯全球高成長科技基金股(美元)',
    #'0P0001AOJZ.JO':'鋒裕匯理基金新興市場債券A南非幣(穩定月配息)'
}

def get_stock_name(stock_ticker):
    """
    獲取股票名稱

    參數:
    stock_ticker (str): 股票代碼

    返回:
    str: 股票名稱
    """
    print("========== get_stock_name ==========")

    stock_name = stockNames.get_dbstock_name(stock_ticker)
    #stock_info = yf.Ticker(stock_ticker).info
    #stock_name = stock_info.get('longName', stock_ticker)
    return stock_name

# 計算ATR的函式
def atr(df, period):
    """
    計算真實波幅(ATR)

    參數:
    df (DataFrame): 股票數據
    period (int): ATR的週期

    返回:
    Series: ATR數據
    """
    print("========== atr ==========")

    high_low = df['High'] - df['Low']
    high_close = abs(df['High'] - df['Close'].shift())
    low_close = abs(df['Low'] - df['Close'].shift())
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    atr = true_range.rolling(period).mean()
    return atr

# 計算追踪止損價的函式
def calculate_trailing_stop(df, nATRPeriod=None, nATRMultip=None):
    """
    計算追踪止損價 (Trailing Stop)

    參數:
    df (DataFrame): 股票數據
    nATRPeriod (int): ATR計算的週期
    nATRMultip (float): ATR的倍數用於計算止損價

    返回:
    Series: 追踪止損價數據
    """
    print("========== calculate_trailing_stop ==========")

    print(f"最佳 ATR multiple 参数 calculate_trailing_stop 为 {nATRMultip:.2f}/window 参数为 {nATRPeriod:.2f}")
    xATR = atr(df, nATRPeriod)  # 計算ATR
    nLoss = nATRMultip * xATR  # 根據ATR和倍數計算止損價

    xATRTrailingStop = pd.Series(index=df.index)  # 創建一個與股票數據索引一致的空Series用於存儲追踪止損價
    for i in range(1, len(df)):
        prev_close = df['Close'].iloc[i - 1]  # 前一天的調整後收盤價
        prev_stop = xATRTrailingStop.iloc[i - 1]  # 前一天的追踪止損價
        close = df['Close'].iloc[i]  # 當天的調整後收盤價

        if close > prev_stop and prev_close > prev_stop:
            xATRTrailingStop.iloc[i] = max(prev_stop, close - nLoss.iloc[i])  # 當價格上升時，更新追踪止損價為當前價減去nLoss
        elif close < prev_stop and prev_close < prev_stop:
            xATRTrailingStop.iloc[i] = min(prev_stop, close + nLoss.iloc[i])  # 當價格下降時，更新追踪止損價為當前價加上nLoss
        else:
            xATRTrailingStop.iloc[i] = close - nLoss.iloc[i] if close > prev_stop else close + nLoss.iloc[i]  # 根據價格變化更新追踪止損價
    return xATRTrailingStop



def analyze_stock_with_trailing_stop(stock_ticker, stock_name,nATRPeriod=None, nATRMultip=None,total_value=None):
    """
    分析單支股票的 Darvas Box 策略並繪製結果，包含追踪止損價

    參數:
    stock_ticker (str): 股票代碼
    stock_name (str): 股票名稱
    """
    print("========== analyze_stock_with_trailing_stop ==========")

    # 下載股票的交易紀錄
    stock_data = us.download(stock_ticker,duration_years=1)
    print(f"最佳 ATR multiple 参数 analyze_stock_with_trailing_stop 为 {nATRMultip:.2f}/window 参数为 {nATRPeriod}")
    # 計算追踪止損價
    stock_data['Trailing Stop'] = calculate_trailing_stop(stock_data, nATRPeriod, nATRMultip)

    # 初始化 Box 列表來儲存所有的 Darvas Box 資料
    boxes = []

    # 定義 Box 的初始上下緣以及上緣的日期
    upper = None  # 上緣價格
    lower = None  # 下緣價格
    upper_date = None  # 上緣日期

    # 記錄高點後的最低價
    recent_low = None

    # 檢查每個交易日的價格來決定 Box 的上緣和下緣
    for i in range(1, len(stock_data)):
        # 當前的調整後收盤價和日期
        close_price = stock_data['Close'][i]
        current_date = stock_data.index[i]

        # 如果已經有 Box 上緣和下緣，檢查是否創建新 Box
        if upper is not None and lower is not None:
            # 當價格超過 Box 上緣且上緣的日期在下緣之前，定義新 Box
            if close_price > upper and upper_date is not None and upper_date < current_date:
                lower = recent_low
                # 儲存新 Box 的上下緣和日期範圍
                boxes.append({'lower': lower, 'upper': upper, 'start_date': upper_date, 'end_date': current_date})
                upper = close_price
                upper_date = current_date
                recent_low = None  # 重置最近低點

            # 更新最近的低點，若當前價格低於前一低點
            if recent_low is None or close_price < recent_low:
                recent_low = close_price

        # 若尚未設定上緣或發現新高，則設定新 Box 的上緣
        elif upper is None or close_price > upper:
            upper = close_price
            upper_date = current_date
            recent_low = None

        # 當上緣存在且目前價格低於上緣，尋找回調的最低點
        elif close_price < upper and (recent_low is None or close_price < recent_low):
            recent_low = close_price

        # 當最近低點回升，並且尚未設定下緣，決定 Box 的下緣
        if recent_low is not None and close_price > recent_low and lower is None and upper_date is not None and upper_date < current_date:
            lower = recent_low
            # 當下緣線段第一次產生時，添加紅色正三角形標記
            # plt.scatter(current_date, lower, color='red', marker='^', s=100)

    # 繪製圖形，設置圖形大小
    plt.figure(figsize=(16, 9))
    plt.plot(stock_data.index, stock_data['Close'], label='Close Price', color='blue', alpha=0.6)

    # 畫出每個 Box 的線段表示
    first_upper = True  # 用於控制圖例的顯示，避免重複
    first_lower = True

    for box in boxes:
        # 繪製上緣線段，僅在第一次顯示圖例
        if first_upper:
            plt.plot([box['start_date'], box['end_date']], [box['upper'], box['upper']], color='green', linestyle='dashed', label='Upper Bound')
            first_upper = False
        else:
            plt.plot([box['start_date'], box['end_date']], [box['upper'], box['upper']], color='green', linestyle='dashed')

        # 繪製下緣線段，僅在第一次顯示圖例
        if first_lower:
            plt.plot([box['start_date'], box['end_date']], [box['lower'], box['lower']], color='red', linestyle='dashed', label='Lower Bound')
            first_lower = False
        else:
            plt.plot([box['start_date'], box['end_date']], [box['lower'], box['lower']], color='red', linestyle='dashed')

    # 繪製追踪止損價
    plt.step(stock_data.index, stock_data['Trailing Stop'], where='post', label='Trailing Stop', color='grey', linestyle='dotted')

    # 設置圖形標題和標籤
    plt.title(stock_ticker + ' Price with Darvas Box Strategy and Trailing Stop')
    plt.xlabel('Date')
    plt.ylabel('Price')
    plt.legend()

    # 取得最後一個收盤價和追踪止損價
    last_close_price = stock_data['Close'].iloc[-1]
    last_trailing_stop = stock_data['Trailing Stop'].iloc[-1]

    # 取得最後一個上緣價和下緣價
    if boxes:
        last_upper = boxes[-1]['upper']
        last_lower = boxes[-1]['lower']
    else:
        last_upper = None
        last_lower = None

    # 獲取圖形的邊界值
    x_min, x_max = plt.xlim()
    y_min, y_max = plt.ylim()

    # 調整 x 軸範圍以便有空間顯示文字
    plt.xlim(x_min, x_max + (x_max - x_min) * 0.2)  # 右側增加 20% 的空間

    # 計算 y 軸中央位置
    y_center = y_min + (y_max - y_min)*0.8
    #y_center = y_max*0.8

    # 計算每行文字的位置
    line_spacing = (y_max - y_min) * 0.05  # 每行之間的間隔
    y_positions = [
        y_center + line_spacing * 1.5,
        y_center + line_spacing * 0.5,
        y_center - line_spacing * 0.5,
        y_center - line_spacing * 1.5,
        y_center - line_spacing * 3.5,
    ]

    # 設置字體大小
    font_size = 10

    # x 軸文字位置
    x_text = x_max + (x_max - x_min) * 0.01  # x 軸右側偏移 1%

    # 最後一個收盤價（藍色）
    plt.text(x_text, y_positions[0], f'Close: {last_close_price:.2f}', color='blue', fontsize=font_size,
             verticalalignment='center', horizontalalignment='left')

    # 最後一個上緣價（綠色）
    if last_upper is not None:
        plt.text(x_text, y_positions[1], f'Upper: {last_upper:.2f}', color='green', fontsize=font_size,
                 verticalalignment='center', horizontalalignment='left')

    # 最後一個下緣價（紅色）
    if last_lower is not None:
        plt.text(x_text, y_positions[2], f'Lower: {last_lower:.2f}', color='red', fontsize=font_size,
                 verticalalignment='center', horizontalalignment='left')

    # 最後一個追踪止損價（灰色）
    plt.text(x_text, y_positions[3], f'trailing_stop: {last_trailing_stop:.2f}', color='grey', fontsize=font_size,
             verticalalignment='center', horizontalalignment='left')

    # 最後一個追踪止損價（灰色）
    plt.text(x_text, y_positions[4], f'nATRPeriod: {nATRPeriod:.2f}\nnATRMultip: {nATRMultip:.2f}\ntotal value:{int(total_value):,}', color='grey', fontsize=font_size,
             verticalalignment='center', horizontalalignment='left')

    # 保存圖形到內存中
    buf = BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight')  # 使用 bbox_inches='tight' 以確保文字不被裁剪
    buf.seek(0)
    img_data = buf.read()
    #plt.show()
    plt.close()

    # 構造圖片信息字典
    return {'id': stock_ticker, 'data': img_data, 'name': stock_name}



def send_email_with_images(recipient_emails, images):
    """
    發送包含圖形的電子郵件，根據容量限制 (8MB) 和圖片大小限制 (250KB) 分批發送。

    參數:
    recipient_emails (list): 收件人的電子郵件地址列表
    images (list): 圖形的數據列表（每個元素包含文件名和數據）
    """
    print("========== send_email_with_images ==========")

    sender_email = "<EMAIL>"
    sender_name = "Ryan Lin"
    sender_password = "gipg ulxr sncd zhct"
    max_email_size = 8 * 1024 * 1024  # 8MB
    max_image_size = 250 * 1024  # 250KB

    # 分批次處理圖片
    current_batch = []
    current_size = 0
    batches = []

    for img in images:
        image_size = len(img['data'])  # 獲取圖片大小
        if image_size > max_image_size:
            print(f"圖片 {img['name']} 超過 250KB，跳過此圖片。")
            continue

        if current_size + image_size > max_email_size:
            batches.append(current_batch)
            current_batch = []
            current_size = 0

        current_batch.append(img)
        current_size += image_size

    if current_batch:
        batches.append(current_batch)

    # 發送每一批次的郵件
    for batch_idx, batch in enumerate(batches):
        msg = MIMEMultipart('related')
        msg['From'] = formataddr((sender_name, sender_email))
        msg['To'] = ", ".join(recipient_emails)
        msg['Subject'] = f'Darvas Box wt ATR(20) tuned Multi V3.0 股票回測結果 - 批次 {batch_idx + 1}'

        # 構建HTML內容
        html = """
        <html>
        <body>
            {tables}
        </body>
        </html>
        """
        table_entries = ''.join([
            f"""
            <table border="1" cellpadding="10" cellspacing="0" style="border-collapse: collapse;">
                <tr>
                    <th>{img["name"]}</th>
                </tr>
                <tr>
                    <td><img src="cid:{img["id"]}" /></td>
                </tr>
            </table>
            <br>
            """ for img in batch
        ])

        msg.attach(MIMEText(html.format(tables=table_entries), 'html'))

        # 添加每個圖像到電子郵件
        for img in batch:
            image_mime = MIMEImage(img['data'], name=img['name'])
            image_mime.add_header('Content-ID', f'<{img["id"]}>')
            msg.attach(image_mime)

        # 發送郵件
        try:
            server = smtplib.SMTP('smtp.gmail.com', 587)
            server.starttls()
            server.login(sender_email, sender_password)
            server.sendmail(sender_email, recipient_emails, msg.as_string())
            server.quit()
            print(f"批次 {batch_idx + 1} 的郵件已成功發送")
        except Exception as e:
            print(f"批次 {batch_idx + 1} 的郵件發送失敗: {e}")


def getDarvasByStockSymbol(ticker, name,nATRPeriod=None, nATRMultip=None,total_value=None):
    # 您需要根据实际情况修改
    print("========== plot_actual_vs_predicted ==========")
    print(f"getDarvasByStockSymbol 最佳 ATR multiple 参数为 {nATRMultip:2f}/window 参数为 {nATRPeriod}/total_value={total_value:,}")
    image_info = analyze_stock_with_trailing_stop(ticker, name,nATRPeriod, nATRMultip,total_value)
    return image_info #return {'id': stock_ticker, 'data': img_data, 'name': stock_name}

# 依次分析每支股票並收集圖形
recipient_emails = ["<EMAIL>"]
images = []
"""
for ticker, name in stock_names.items():
    image_info = analyze_stock_with_trailing_stop(ticker, name)
    images.append(image_info)

# 發送電子郵件
send_email_with_images(recipient_emails, images)
from OsTools import delete_csv_files
delete_csv_files('C:/Dropbox/PycharmProjects/Darvas/pythonProject')
"""