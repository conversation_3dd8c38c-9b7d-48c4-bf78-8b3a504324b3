# 🤖 Telegram 30分鐘機器人功能說明

## 📋 專案概述

`telegram_bot_30minV2.py` 是一個功能完整的 Telegram 機器人，專門用於金融市場監控和技術分析圖表自動發送。機器人整合了 FlipAdam 技術分析系統，提供高解析度的 10天/20天 30分鐘圖表，支援多種金融標的的訂閱管理和自動化通知服務。

## 🎯 主要功能

### 📊 技術分析圖表生成
- **高解析度圖表**：10天/20天 30分鐘技術分析圖表
- **多標的支援**：股票、ETF、加密貨幣、基金、指數
- **即時更新**：使用 yfinance 資料源，確保資料即時性
- **自動清理**：發送後自動刪除圖片檔案，節省儲存空間

### 📈 訂閱管理系統
- **多間隔支援**：5、10、15、30分鐘自動發送
- **多標的管理**：可同時訂閱多個不同標的
- **自動化發送**：根據設定的間隔自動發送最新圖表
- **持久化儲存**：訂閱資料自動保存到 JSON 檔案

### 🔄 頻率傳送功能
- **手動啟動**：輸入格式 `股票代碼,分鐘數`
- **即時發送**：立即發送第一張圖表
- **循環更新**：按指定間隔持續發送
- **狀態監控**：可查看運行狀態和停止任務

### 🔍 智能搜尋系統
- **模糊搜尋**：支援股票代碼和名稱搜尋
- **多檔案整合**：整合 6 個 CSV 檔案資料源
- **自動查表**：強制經過 CSV 查表確認標的存在
- **分頁瀏覽**：每頁 8 個選項，支援分頁導航

## 🛠️ 技術架構

### 核心模組
```python
# 主要依賴套件
from FlipAdamBox30minV1 import getMLFlipAdambySymbol, get_cffi_session
import yfinance as yf
import pandas as pd
import asyncio
import aiohttp
```

### 資料來源
```python
CSV_FILES = [
    'stock_name_hold_fundsonly_SortbyValue.csv',  # 基金
    'stock_name_hold_stockonly_SortbyValue.csv',  # 股票
    'stock_names_Ady.csv',                        # 其他標的
    'stock_names_coin.csv',                       # 加密貨幣
    'stock_names_ETF.csv',                        # ETF
    'stock_names_watch_index.csv',                # 指數
]
```

### 記憶體管理
```python
# 全域變數管理
frequency_tasks = {}      # 頻率傳送任務
subscriptions = {}        # 訂閱資料
subscription_tasks = {}   # 訂閱任務
user_states = {}         # 用戶狀態
user_pages = {}          # 分頁管理
```

## 📱 用戶介面

### 主選單鍵盤
```
┌─────────────┬─────────────┐
│   📈 訂閱   │ ❌ 取消訂閱 │
├─────────────┼─────────────┤
│ 📋 檢查訂閱 │   ❓ 幫助   │
└─────────────┴─────────────┘
```

### 間隔選擇鍵盤
```
┌─────────────┬─────────────┐
│   5分鐘     │   10分鐘    │
├─────────────┼─────────────┤
│   15分鐘    │   30分鐘    │
├─────────────┴─────────────┤
│      🔙 返回主選單        │
└───────────────────────────┘
```

### 標的選擇鍵盤（分頁）
```
┌─────────────┬─────────────┐
│   TSM       │   AAPL      │
│  台積電     │  蘋果公司   │
├─────────────┼─────────────┤
│   BTC-USD   │   ETH-USD   │
│  比特幣     │  以太幣     │
├─────────────┼─────────────┤
│ ⬅️ 上一頁   │ ➡️ 下一頁   │
├─────────────┴─────────────┤
│        📄 1/5             │
├─────────────┴─────────────┤
│      🔙 返回主選單        │
└───────────────────────────┘
```

## 🔧 核心功能詳解

### 1. 訂閱管理功能

#### 新增訂閱流程
1. **按鈕操作**：
   - 點擊「📈 訂閱」→ 顯示標的清單
   - 選擇標的 → 顯示間隔選擇
   - 選擇間隔 → 完成訂閱

2. **命令操作**：
   ```bash
   /subscribe BTC 30
   /subscribe TSM 15
   ```

#### 訂閱資料結構
```python
subscriptions[user_id][symbol] = {
    'interval_minutes': 30,
    'symbol_name': 'Bitcoin USD 比特幣',
    'added_time': '2024-01-01T12:00:00'
}
```

### 2. 頻率傳送功能

#### 啟動方式
```bash
# 格式：股票代碼,分鐘數
BTC,30      # 每30分鐘發送比特幣圖表
TSM,60      # 每60分鐘發送台積電圖表
AAPL,120    # 每2小時發送蘋果圖表
```

#### 任務管理
```python
frequency_tasks[user_id] = {
    'task_id': 'task_123',
    'symbol': 'BTC-USD',
    'interval_minutes': 30,
    'start_time': datetime.now(),
    'is_running': True
}
```

### 3. 智能搜尋系統

#### 搜尋邏輯
```python
def find_matching_stocks(query):
    # 1. 搜尋股票代碼（不分大小寫）
    # 2. 搜尋股票名稱（模糊匹配）
    # 3. 避免重複結果
    # 4. 返回匹配結果
```

#### 支援的搜尋方式
- **代碼搜尋**：`TSM`、`AAPL`、`BTC`
- **名稱搜尋**：`台積電`、`蘋果`、`比特幣`
- **部分匹配**：`科技`、`金融`、`半導體`

### 4. 圖片處理系統

#### 圖片生成
```python
# 使用 FlipAdam 系統生成圖表
session = get_cffi_session()
image_path = getMLFlipAdambySymbol(symbol, session=session)
```

#### 圖片發送
```python
# 高解析度圖片發送
async def send_photo_with_aiohttp(token, chat_id, photo_path, caption):
    # 不壓縮圖片，保持高解析度
    # 支援重試機制
    # 自動清理檔案
```

## 📋 命令系統

### 基本命令
| 命令 | 功能 | 範例 |
|------|------|------|
| `/start` | 重新開始機器人 | `/start` |
| `/help` | 顯示幫助訊息 | `/help` |
| `/stop` | 停止頻率傳送 | `/stop` |
| `/status` | 查看傳送狀態 | `/status` |

### 訂閱管理命令
| 命令 | 功能 | 範例 |
|------|------|------|
| `/subscribe` | 新增訂閱 | `/subscribe BTC 30` |
| `/unsubscribe` | 取消訂閱 | `/unsubscribe BTC` |
| `/subscriptions` | 查看所有訂閱 | `/subscriptions` |

### 其他命令
| 命令 | 功能 | 範例 |
|------|------|------|
| `/notify` | 發送通知 | `/notify 測試訊息` |

## 🔄 自動化功能

### 程式啟動自動化
```python
async def start_all_subscriptions():
    # 自動載入所有已存在的訂閱
    # 重新啟動所有訂閱任務
    # 恢復程式重啟前的狀態
```

### 預設訂閱設定
```python
default_subscriptions = {
    'BTC-USD': {
        'interval_minutes': 30,
        'symbol_name': 'Bitcoin USD 比特幣'
    },
    'ETH-USD': {
        'interval_minutes': 30,
        'symbol_name': 'Ethereum USD 以太幣'
    }
}
```

### 檔案清理自動化
```python
def cleanup_old_images():
    # 自動清理超過1小時的舊圖片
    # 發送後立即刪除圖片檔案
    # 避免磁碟空間浪費
```

## 🛡️ 錯誤處理

### 異常處理機制
- **網路錯誤**：自動重試機制
- **檔案錯誤**：優雅降級處理
- **用戶輸入錯誤**：友善錯誤提示
- **系統錯誤**：完整日誌記錄

### 狀態管理
```python
user_states = {
    user_id: 'main' | 'waiting_stock_selection' | 'waiting_interval'
}
```

### 日誌系統
```python
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
```

## 📊 效能優化

### 異步處理
- 使用 `asyncio` 進行非阻塞操作
- 支援多用戶同時使用
- 高效的任務管理

### 記憶體管理
- 及時清理圖片檔案
- 避免記憶體洩漏
- 優化的資料結構

### 網路優化
- 使用 `aiohttp` 進行高效網路請求
- 支援 SSL 連接
- 自動重試機制

## 🔧 部署與維護

### 系統需求
- Python 3.7+
- Windows/Linux/macOS
- 網路連接
- 足夠的磁碟空間

### 依賴套件
```txt
python-telegram-bot
pandas
yfinance
aiohttp
Pillow
requests
```

### 啟動方式
```bash
python telegram_bot_30minV2.py
```

### 監控與維護
- 定期檢查日誌檔案
- 監控記憶體使用量
- 備份訂閱資料
- 更新 CSV 資料源

## 📈 使用範例

### 基本使用流程
1. **啟動機器人**：發送 `/start`
2. **新增訂閱**：點擊「📈 訂閱」或使用 `/subscribe BTC 30`
3. **查看訂閱**：點擊「📋 檢查訂閱」或使用 `/subscriptions`
4. **取消訂閱**：點擊「❌ 取消訂閱」或使用 `/unsubscribe BTC`

### 頻率傳送範例
```
用戶輸入：BTC,30
機器人回應：正在啟動頻率傳送任務...
股票: BTC-USD (Bitcoin USD 比特幣)
✅ 已啟動頻率傳送任務
📈 股票代碼: BTC-USD
⏰ 傳送間隔: 每 30 分鐘
🔄 下次傳送: 30 分鐘後
```

### 單次查詢範例
```
用戶輸入：TSM
機器人回應：您輸入的是股票代碼：TSM
對應的股票名稱是：台積電
[發送技術分析圖表]
```

## 🎯 特色功能

### 1. 高解析度圖表
- 10天/20天 30分鐘技術分析
- 適合詳細查看和列印
- 不壓縮保持原始品質

### 2. 智能標的識別
- 支援多種輸入方式
- 自動查表確認
- 避免錯誤輸入

### 3. 靈活的訂閱管理
- 多間隔選擇
- 多標的同時管理
- 即時狀態查看

### 4. 自動化運作
- 程式重啟自動恢復
- 預設訂閱設定
- 自動檔案清理

### 5. 友善用戶介面
- 直觀的按鈕操作
- 分頁瀏覽支援
- 完整的錯誤提示

---

這個 Telegram 30分鐘機器人是一個功能完整、設計精良的金融監控工具，適合投資者進行市場追蹤和技術分析，提供了良好的用戶體驗和穩定的服務品質。 