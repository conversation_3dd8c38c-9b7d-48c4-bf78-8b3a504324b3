# 專案文件資料夾

此資料夾包含 FlipAdam 專案的所有文件檔案。

## 檔案說明

### 📋 功能說明文件
- **新增訂閱功能說明.md** - 新增訂閱功能的詳細說明
- **Telegram30分鐘機器人功能說明.md** - Telegram 機器人的完整功能說明

### 📊 技術文件
- **Readme-ML5.md** - ML5 版本的說明文件
- **create_tables.sql** - 資料庫表格建立腳本

### ⚙️ 配置檔案
- **FlipAdamTelegramBot.xml** - Telegram Bot 配置檔案
- **指令檔.txt** - 常用指令說明

### 📄 授權文件
- **LICENSE** - 專案授權條款

### 📈 分析報告
- **crypto_analysis_report.html** - 加密貨幣分析報告
- **analysis_report.html** - 一般分析報告
- **correlation_matrix.xlsx** - 相關性矩陣分析

## 文件分類

### 功能說明
這些文件詳細說明了專案中各個功能的實作方式和使用方法。

### 技術文件
包含技術實作細節、資料庫結構和配置說明。

### 配置檔案
系統配置和部署相關的檔案。

## 注意事項

1. 所有文件都使用 Markdown 格式，方便閱讀和維護
2. 技術文件會隨著專案更新而更新
3. 配置檔案請謹慎修改，建議先備份
4. 如有疑問請參考對應的功能說明文件 