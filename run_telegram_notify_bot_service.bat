@echo off
chcp 65001 >nul
title FlipAdam 批次機器人

echo ========================================
echo    FlipAdam 批次機器人啟動中...
echo ========================================
echo.

:: 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤：未找到 Python，請先安裝 Python
    pause
    exit /b 1
)

:: 檢查虛擬環境是否存在
if not exist "venv312\Scripts\activate.bat" (
    echo 警告：未找到虛擬環境，將使用系統 Python
    echo.
    echo 正在啟動 FlipAdam 批次機器人...
    python telegram_bot_notify.py
) else (
    echo 找到虛擬環境，正在啟動...
    echo.
    call venv312\Scripts\activate.bat
    echo 正在啟動 FlipAdam 批次機器人...
    python telegram_bot_notify.py
)

echo.
echo 機器人已停止運行
pause 