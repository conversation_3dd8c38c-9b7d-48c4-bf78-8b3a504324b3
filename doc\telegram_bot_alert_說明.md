# FlipAdam 警報機器人說明

## 功能概述

`telegram_bot_alert.py` 是一個專門用於監控和分析標的資料的 Telegram 機器人，具有以下主要功能：

### 1. 定時分析
- **檢查間隔**：每30分鐘自動檢查一次
- **資料來源**：分析 `data/` 目錄中的標的紀錄
- **時間範圍**：近24小時的資料

### 2. 趨勢圖生成
- **圖表類型**：24小時價格趨勢圖和漲跌幅趨勢圖
- **儲存位置**：`figure/` 目錄
- **檔案格式**：PNG 格式，高解析度 (300 DPI)

### 3. 警報檢測

#### 3.1 交易信號變化警報
- **觸發條件**：前一筆交易信號為「無信號」，最新一筆有信號（不是無信號）
- **警報內容**：顯示信號變化過程和當前技術分析資料

#### 3.2 漲跌幅方向變化警報
- **翻空警報**：前一筆漲跌幅為正數，最新一筆為負數
- **翻多警報**：前一筆漲跌幅為負數，最新一筆為正數
- **警報內容**：顯示方向變化和當前技術分析資料

### 4. 重複警報防護
- **防護機制**：同一標的的相同類型警報在30分鐘內只發送一次
- **避免干擾**：防止過於頻繁的警報訊息

## 設定參數

```python
TOKEN = "7958647961:AAGfrXBz81UuDsrecBzYY55CewAcUk4VNts"
USER_ID = 1057529499
CHECK_INTERVAL = 30 * 60  # 30分鐘檢查一次
```

## 使用方式

### 1. 啟動機器人
```bash
# 使用批次檔案
run_telegram_bot_alert.bat

# 或直接執行
python telegram_bot_alert.py
```

### 2. Telegram 命令
- `/start` - 啟動機器人
- `/status` - 查看系統狀態（監控標的數量、檢查間隔等）
- `/check` - 手動觸發檢查

### 3. 警報訊息格式

#### 信號變化警報
```
🚨 **AAPL 警報**
⏰ 時間: 2025-01-01 10:30:00
💰 當前價格: 150.25
📊 漲跌幅: 2.50%
🎯 交易信號: 做多

🔔 **信號變化警報**
前次信號: 無信號
最新信號: 做多
```

#### 方向變化警報
```
🚨 **TSLA 警報**
⏰ 時間: 2025-01-01 10:30:00
💰 當前價格: 250.75
📊 漲跌幅: -1.25%
🎯 交易信號: 做空

🔄 **方向變化警報**
變化: 翻空
前次漲跌幅: 2.50%
最新漲跌幅: -1.25%
```

## 檔案結構

```
telegram_bot_alert.py          # 主程式檔案
run_telegram_bot_alert.bat     # 啟動腳本
data/                          # 分析資料目錄
├── symbol_timeversion_timestamp.json
figure/                        # 趨勢圖目錄
├── symbol_trend_timestamp.png
```

## 技術特點

### 1. 資料處理
- **自動載入**：從 JSON 檔案自動載入分析資料
- **時間排序**：按時間戳記自動排序
- **資料驗證**：檢查資料完整性和有效性

### 2. 圖表生成
- **雙圖表**：價格趨勢圖 + 漲跌幅趨勢圖
- **中文支援**：完整的中文字體支援
- **時間軸**：自動格式化時間軸標籤

### 3. 錯誤處理
- **異常捕獲**：完整的錯誤處理機制
- **日誌記錄**：詳細的日誌記錄
- **備用機制**：發送失敗時的備用顯示

## 注意事項

1. **資料依賴**：機器人依賴 `data/` 目錄中的分析資料
2. **網路連線**：需要穩定的網路連線來發送 Telegram 訊息
3. **磁碟空間**：趨勢圖會佔用一定的磁碟空間
4. **權限設定**：確保機器人有發送訊息的權限

## 故障排除

### 1. 無法啟動
- 檢查 Python 環境和依賴套件
- 確認 Telegram Token 是否有效
- 檢查網路連線

### 2. 沒有警報
- 確認 `data/` 目錄中有分析資料
- 檢查資料格式是否正確
- 確認警報條件是否滿足

### 3. 圖表生成失敗
- 檢查 matplotlib 和字體設定
- 確認 `figure/` 目錄權限
- 檢查記憶體使用情況

## 更新日誌

- **v1.0** - 初始版本，包含基本警報功能
- 支援信號變化和方向變化檢測
- 整合趨勢圖生成功能
- 實作重複警報防護機制 