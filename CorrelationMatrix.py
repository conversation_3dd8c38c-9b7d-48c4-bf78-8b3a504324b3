import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt
# 定義符號列表
symbols = ['LQD','00724B.TWO', 'TLT', 'HYG', 'TLH', 'EMB', 'DX-Y.NYB', '^GSPC', '^IXIC']

# 定義日期範圍
start_date = '2020-01-01'
end_date = '2025-03-05'

# 獲取數據
data = yf.download(symbols, start=start_date, end=end_date)

# 提取收盤價
close_prices = data['Close']

# 計算每日回報，指定 fill_method=None 以避免警告
returns = close_prices.pct_change(fill_method=None).dropna()

# 計算相關性矩陣
correlation_matrix = returns.corr()

correlation_matrix.to_excel('correlation_matrix.xlsx')

# Prepare cell text for the table
cellText = correlation_matrix.applymap(lambda x: f"{x:.4f}").values

# Create figure and axis
fig, ax = plt.subplots()
ax.axis('off')

# Create table
table = ax.table(cellText=cellText,
                 rowLabels=correlation_matrix.index,
                 colLabels=correlation_matrix.columns,
                 loc='center')

# Set font size directly (replacing auto_set_fontsize)
table.set_fontsize(8)

# Set alignments for cells
for (row, col), cell in table._cells.items():
    if row == -1:  # Column headers
        cell.set_text_props(horizontalalignment='center', verticalalignment='center')
    elif col == -1:  # Row labels
        cell.set_text_props(horizontalalignment='left', verticalalignment='center')
    else:  # Data cells
        cell.set_text_props(horizontalalignment='right', verticalalignment='center')

# Bold headers and labels
for key, cell in table._cells.items():
    if key[0] == -1 or key[1] == -1:
        cell.set_text_props(weight='bold')

# Set figure size
fig.set_size_inches(12, 12)

# Save the figure
fig.savefig('correlation_matrix.png')

# Optional: Display the plot (uncomment if needed)
# plt.show()