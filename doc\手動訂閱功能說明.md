# 手動訂閱功能說明

## 📋 功能概述

在 `telegram_bot_30minV2.py` 中新增了手動訂閱功能，用戶可以直接輸入特定格式來快速訂閱股票，無需使用按鈕介面。

## 🎯 輸入格式

**格式：** `股票代碼,分鐘數,s`

**說明：**
- `股票代碼`：股票代碼或名稱（支援模糊搜尋）
- `分鐘數`：訂閱間隔時間（1-1440分鐘）
- `s`：訂閱標識符（必須小寫）

## 📝 使用範例

### 基本訂閱
```
TSM,30,s          # 訂閱台積電，每30分鐘發送
BTC-USD,60,s      # 訂閱比特幣，每60分鐘發送
AAPL,120,s        # 訂閱蘋果，每2小時發送
```

### 支援的股票類型
- **台股**：TSM（台積電）、2330（台積電代碼）
- **美股**：AAPL（蘋果）、GOOGL（谷歌）
- **加密貨幣**：BTC-USD（比特幣）、ETH-USD（以太幣）
- **其他**：支援所有 yfinance 可查詢的標的

## ⚙️ 功能特點

### ✅ 自動驗證
- 自動查表確認標的存在
- 支援模糊搜尋（如輸入「台積電」會找到 TSM）
- 如果本地清單找不到，會自動使用 yfinance 線上搜尋

### ✅ 重複檢查
- 自動檢查是否已經訂閱相同標的
- 避免重複訂閱

### ✅ 智能提示
- 成功訂閱時顯示詳細資訊
- 錯誤時提供明確的錯誤訊息

## 🔄 與現有功能的區別

| 功能 | 格式 | 用途 |
|------|------|------|
| 頻率傳送 | `股票代碼,分鐘數` | 一次性傳送，需要手動停止 |
| 手動訂閱 | `股票代碼,分鐘數,s` | 持續訂閱，自動發送 |

## 📊 訂閱管理

### 查看訂閱
- 使用 `/subscriptions` 命令查看所有訂閱
- 使用 `/status` 命令查看系統狀態

### 取消訂閱
- 使用 `/unsubscribe <股票代碼>` 取消特定訂閱
- 使用按鈕介面取消訂閱

## 🛠️ 技術實現

### 核心函數
- `handle_message()` - 處理訊息輸入
- `add_subscription()` - 添加訂閱
- `start_subscription_task()` - 啟動訂閱任務
- `find_matching_stocks()` - 股票搜尋

### 資料儲存
- 訂閱資料儲存在 `config/subscriptions.json`
- 支援多用戶同時訂閱
- 自動保存和載入訂閱設定

## 📈 使用建議

### 推薦間隔時間
- **短線交易**：5-15分鐘
- **中線交易**：30-60分鐘
- **長線投資**：120-240分鐘

### 注意事項
- 間隔時間範圍：1-1440分鐘（最大24小時）
- 每個用戶可以同時訂閱多個標的
- 訂閱會持續到用戶手動取消
- 圖片為高解析度格式，適合詳細分析

## 🎉 測試結果

功能已通過完整測試：
- ✅ 台積電 (TSM) 30分鐘訂閱
- ✅ 比特幣 (BTC-USD) 60分鐘訂閱  
- ✅ 蘋果 (AAPL) 120分鐘訂閱
- ✅ 重複訂閱檢查
- ✅ 錯誤格式處理

## 📞 支援

如有問題，請使用 `/help` 命令查看完整說明，或聯繫開發者。 