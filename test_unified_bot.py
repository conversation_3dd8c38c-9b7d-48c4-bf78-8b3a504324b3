"""
UnifiedBot 測試腳本
測試統一多功能機器人的核心功能
"""

import asyncio
import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 載入環境變數
load_dotenv()

from bots.unified_bot import UnifiedBot
from core.management.module_manager import ModuleManager
from core.events.event_bus import EventBus
from core.config.config_manager import ConfigManager
from core.plugins.plugin_registry import PluginRegistry


class UnifiedBotTester:
    """UnifiedBot 測試器"""
    
    def __init__(self):
        self.logger = logging.getLogger("UnifiedBotTester")
        self.test_results = []
    
    async def run_all_tests(self):
        """運行所有測試"""
        self.logger.info("🧪 開始 UnifiedBot 測試...")
        
        # 測試核心組件
        await self.test_core_components()
        
        # 測試插件系統
        await self.test_plugin_system()
        
        # 測試事件系統
        await self.test_event_system()
        
        # 測試配置管理
        await self.test_config_management()
        
        # 測試機器人初始化
        await self.test_bot_initialization()
        
        # 輸出測試結果
        self.print_test_results()
    
    async def test_core_components(self):
        """測試核心組件"""
        self.logger.info("🔧 測試核心組件...")
        
        try:
            # 測試模組管理器
            module_manager = ModuleManager()
            self.add_test_result("ModuleManager 創建", True, "成功創建模組管理器")
            
            # 測試事件總線
            event_bus = EventBus()
            await event_bus.initialize()
            self.add_test_result("EventBus 初始化", True, "事件總線初始化成功")
            
            # 測試配置管理器
            config_manager = ConfigManager()
            await config_manager.initialize()
            self.add_test_result("ConfigManager 初始化", True, "配置管理器初始化成功")
            
            # 測試插件註冊表
            plugin_registry = PluginRegistry()
            await plugin_registry.initialize()
            self.add_test_result("PluginRegistry 初始化", True, "插件註冊表初始化成功")
            
        except Exception as e:
            self.add_test_result("核心組件測試", False, f"測試失敗: {e}")
    
    async def test_plugin_system(self):
        """測試插件系統"""
        self.logger.info("🔌 測試插件系統...")
        
        try:
            # 測試插件載入器
            from core.plugins.plugin_loader import PluginLoader
            plugin_loader = PluginLoader()
            await plugin_loader.initialize()
            self.add_test_result("PluginLoader 初始化", True, "插件載入器初始化成功")
            
            # 測試基礎插件
            from core.plugins.base_plugin import BasePlugin
            base_plugin = BasePlugin()
            self.add_test_result("BasePlugin 創建", True, "基礎插件創建成功")
            
            # 測試插件註冊
            plugin_registry = PluginRegistry()
            await plugin_registry.initialize()
            
            # 註冊測試插件
            test_plugin = BasePlugin()
            test_plugin.name = "TestPlugin"
            plugin_registry.register_plugin("TestPlugin", test_plugin)
            
            registered_plugins = plugin_registry.list_plugins()
            if "TestPlugin" in registered_plugins:
                self.add_test_result("插件註冊", True, "插件註冊成功")
            else:
                self.add_test_result("插件註冊", False, "插件註冊失敗")
            
        except Exception as e:
            self.add_test_result("插件系統測試", False, f"測試失敗: {e}")
    
    async def test_event_system(self):
        """測試事件系統"""
        self.logger.info("📡 測試事件系統...")
        
        try:
            event_bus = EventBus()
            await event_bus.initialize()
            
            # 測試事件訂閱
            test_event_received = False
            
            def test_handler(event):
                nonlocal test_event_received
                test_event_received = True
            
            event_bus.subscribe("test.event", test_handler)
            self.add_test_result("事件訂閱", True, "事件訂閱成功")
            
            # 測試事件發布
            await event_bus.publish("test.event", {"data": "test"})
            
            # 等待事件處理
            await asyncio.sleep(0.1)
            
            if test_event_received:
                self.add_test_result("事件發布", True, "事件發布成功")
            else:
                self.add_test_result("事件發布", False, "事件發布失敗")
            
        except Exception as e:
            self.add_test_result("事件系統測試", False, f"測試失敗: {e}")
    
    async def test_config_management(self):
        """測試配置管理"""
        self.logger.info("⚙️ 測試配置管理...")
        
        try:
            config_manager = ConfigManager()
            await config_manager.initialize()
            
            # 測試配置獲取
            config = config_manager.get_config()
            if isinstance(config, dict):
                self.add_test_result("配置獲取", True, "配置獲取成功")
            else:
                self.add_test_result("配置獲取", False, "配置獲取失敗")
            
            # 測試配置更新
            await config_manager.update_config("test_key", "test_value")
            test_value = config_manager.get_config("test_key")
            
            if test_value == "test_value":
                self.add_test_result("配置更新", True, "配置更新成功")
            else:
                self.add_test_result("配置更新", False, "配置更新失敗")
            
        except Exception as e:
            self.add_test_result("配置管理測試", False, f"測試失敗: {e}")
    
    async def test_bot_initialization(self):
        """測試機器人初始化"""
        self.logger.info("🤖 測試機器人初始化...")
        
        try:
            # 檢查環境變數
            bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
            if not bot_token:
                self.add_test_result("環境變數檢查", False, "TELEGRAM_BOT_TOKEN 未設置")
                return
            
            # 創建機器人（不啟動）
            bot = UnifiedBot(bot_token, "TestBot")
            self.add_test_result("機器人創建", True, "機器人創建成功")
            
            # 測試初始化
            await bot.initialize()
            if bot.is_initialized:
                self.add_test_result("機器人初始化", True, "機器人初始化成功")
            else:
                self.add_test_result("機器人初始化", False, "機器人初始化失敗")
            
            # 清理
            await bot.stop()
            
        except Exception as e:
            self.add_test_result("機器人初始化測試", False, f"測試失敗: {e}")
    
    def add_test_result(self, test_name: str, success: bool, message: str):
        """添加測試結果"""
        self.test_results.append({
            "name": test_name,
            "success": success,
            "message": message
        })
        
        status = "✅" if success else "❌"
        self.logger.info(f"{status} {test_name}: {message}")
    
    def print_test_results(self):
        """輸出測試結果"""
        print("\n" + "="*50)
        print("🧪 UnifiedBot 測試結果")
        print("="*50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 總測試數: {total_tests}")
        print(f"✅ 通過: {passed_tests}")
        print(f"❌ 失敗: {failed_tests}")
        print(f"📈 成功率: {(passed_tests/total_tests*100):.1f}%")
        
        print("\n📋 詳細結果:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            print(f"  {status} {result['name']}: {result['message']}")
        
        if failed_tests == 0:
            print("\n🎉 所有測試通過！UnifiedBot 準備就緒。")
        else:
            print(f"\n⚠️ 有 {failed_tests} 個測試失敗，請檢查相關功能。")
        
        print("="*50)


async def main():
    """主函數"""
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 創建測試器
    tester = UnifiedBotTester()
    
    # 運行測試
    await tester.run_all_tests()


if __name__ == "__main__":
    # 設置事件循環策略（Windows 兼容性）
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 運行測試
    asyncio.run(main())
