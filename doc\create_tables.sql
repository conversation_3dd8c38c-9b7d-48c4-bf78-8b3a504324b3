-- 建立總體分析結果表
CREATE TABLE IF NOT EXISTS adam_theory_total (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    trade_date DATE NOT NULL,
    current_price DECIMAL(15,6) NOT NULL,
    reflection_date DATE NOT NULL,
    reflection_price DECIMAL(15,6) NOT NULL,
    price_difference DECIMAL(15,6) NOT NULL,
    percentage_change DECIMAL(10,4) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol),
    INDEX idx_trade_date (trade_date),
    INDEX idx_price_difference (price_difference)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 建立股票分析結果表
CREATE TABLE IF NOT EXISTS adam_theory_stock (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    trade_date DATE NOT NULL,
    current_price DECIMAL(15,6) NOT NULL,
    reflection_date DATE NOT NULL,
    reflection_price DECIMAL(15,6) NOT NULL,
    price_difference DECIMAL(15,6) NOT NULL,
    percentage_change DECIMAL(10,4) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol),
    INDEX idx_trade_date (trade_date),
    INDEX idx_price_difference (price_difference)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 建立加密貨幣分析結果表
CREATE TABLE IF NOT EXISTS adam_theory_crypto (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    trade_date DATE NOT NULL,
    current_price DECIMAL(15,6) NOT NULL,
    reflection_date DATE NOT NULL,
    reflection_price DECIMAL(15,6) NOT NULL,
    price_difference DECIMAL(15,6) NOT NULL,
    percentage_change DECIMAL(10,4) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol),
    INDEX idx_trade_date (trade_date),
    INDEX idx_price_difference (price_difference)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 建立基金分析結果表
CREATE TABLE IF NOT EXISTS adam_theory_funds (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    trade_date DATE NOT NULL,
    current_price DECIMAL(15,6) NOT NULL,
    reflection_date DATE NOT NULL,
    reflection_price DECIMAL(15,6) NOT NULL,
    price_difference DECIMAL(15,6) NOT NULL,
    percentage_change DECIMAL(10,4) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol),
    INDEX idx_trade_date (trade_date),
    INDEX idx_price_difference (price_difference)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 建立指數分析結果表
CREATE TABLE IF NOT EXISTS adam_theory_index (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    trade_date DATE NOT NULL,
    current_price DECIMAL(15,6) NOT NULL,
    reflection_date DATE NOT NULL,
    reflection_price DECIMAL(15,6) NOT NULL,
    price_difference DECIMAL(15,6) NOT NULL,
    percentage_change DECIMAL(10,4) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol),
    INDEX idx_trade_date (trade_date),
    INDEX idx_price_difference (price_difference)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 