"""
模組管理器
負責管理插件的載入、卸載、重新載入和生命週期管理
"""

import asyncio
import logging
import importlib.util
import os
from typing import Dict, List, Optional, Any
from pathlib import Path

# 導入相關組件
from core.plugins.base_plugin import BasePlugin
from core.plugins.plugin_loader import Plugin<PERSON>oader
from core.plugins.plugin_registry import PluginRegistry


class ModuleManager:
    """
    模組管理器
    負責管理所有插件的生命週期
    """
    
    def __init__(self):
        """初始化模組管理器"""
        self.logger = logging.getLogger("ModuleManager")
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_loader: Optional[PluginLoader] = None
        self.plugin_registry: Optional[PluginRegistry] = None
        self.event_bus = None
        self.config_manager = None
        self.is_initialized = False
    
    async def initialize(self, event_bus, config_manager):
        """
        初始化模組管理器
        
        Args:
            event_bus: 事件總線實例
            config_manager: 配置管理器實例
        """
        try:
            self.logger.info("初始化模組管理器...")
            
            self.event_bus = event_bus
            self.config_manager = config_manager
            
            # 初始化插件載入器
            self.plugin_loader = PluginLoader()
            await self.plugin_loader.initialize()
            
            # 初始化插件註冊表
            self.plugin_registry = PluginRegistry()
            await self.plugin_registry.initialize()
            
            # 載入預設插件
            await self._load_default_plugins()
            
            self.is_initialized = True
            self.logger.info("模組管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"模組管理器初始化失敗: {e}")
            raise
    
    async def _load_default_plugins(self):
        """載入預設插件"""
        try:
            # 檢查預設插件目錄
            default_plugins_dir = Path("plugins")
            if default_plugins_dir.exists():
                for plugin_file in default_plugins_dir.glob("*.py"):
                    if plugin_file.name != "__init__.py":
                        try:
                            await self.load_plugin(str(plugin_file))
                        except Exception as e:
                            self.logger.warning(f"載入預設插件 {plugin_file.name} 失敗: {e}")
            
            self.logger.info(f"載入預設插件完成，共 {len(self.plugins)} 個插件")
            
        except Exception as e:
            self.logger.error(f"載入預設插件失敗: {e}")
    
    async def load_plugin(self, plugin_path: str) -> str:
        """
        載入插件
        
        Args:
            plugin_path: 插件檔案路徑
            
        Returns:
            插件名稱
        """
        try:
            self.logger.info(f"載入插件: {plugin_path}")
            
            # 使用插件載入器載入插件
            plugin = await self.plugin_loader.load_plugin_file(plugin_path)
            
            # 驗證插件
            await self.plugin_loader.validate_plugin(plugin)
            
            # 獲取插件配置
            plugin_config = await self.config_manager.get_plugin_config(plugin.name)
            
            # 初始化插件
            await self.plugin_loader.initialize_plugin(plugin, plugin_config)
            
            # 註冊插件
            self.plugin_registry.register_plugin(plugin)
            self.plugins[plugin.name] = plugin
            
            # 啟動插件
            await plugin.start()
            
            # 發布插件載入事件
            if self.event_bus:
                await self.event_bus.publish("plugin_loaded", {
                    "plugin_name": plugin.name,
                    "plugin_path": plugin_path
                })
            
            self.logger.info(f"插件 {plugin.name} 載入成功")
            return plugin.name
            
        except Exception as e:
            self.logger.error(f"載入插件 {plugin_path} 失敗: {e}")
            raise
    
    async def unload_plugin(self, plugin_name: str):
        """
        卸載插件
        
        Args:
            plugin_name: 插件名稱
        """
        try:
            self.logger.info(f"卸載插件: {plugin_name}")
            
            plugin = self.plugin_registry.get_plugin(plugin_name)
            if not plugin:
                raise ValueError(f"找不到插件: {plugin_name}")
            
            # 停止插件
            await plugin.stop()
            
            # 清理插件
            await plugin.cleanup()
            
            # 從註冊表移除
            self.plugin_registry.unregister_plugin(plugin_name)
            
            # 從插件列表移除
            if plugin_name in self.plugins:
                del self.plugins[plugin_name]
            
            # 發布插件卸載事件
            if self.event_bus:
                await self.event_bus.publish("plugin_unloaded", {
                    "plugin_name": plugin_name
                })
            
            self.logger.info(f"插件 {plugin_name} 卸載成功")
            
        except Exception as e:
            self.logger.error(f"卸載插件 {plugin_name} 失敗: {e}")
            raise
    
    async def reload_plugin(self, plugin_name: str):
        """
        重新載入插件
        
        Args:
            plugin_name: 插件名稱
        """
        try:
            self.logger.info(f"重新載入插件: {plugin_name}")
            
            plugin = self.plugin_registry.get_plugin(plugin_name)
            if not plugin:
                raise ValueError(f"找不到插件: {plugin_name}")
            
            # 備份插件路徑
            plugin_path = getattr(plugin, '_plugin_path', None)
            
            # 卸載插件
            await self.unload_plugin(plugin_name)
            
            # 重新載入插件
            if plugin_path:
                await self.load_plugin(plugin_path)
            else:
                raise ValueError(f"無法重新載入插件 {plugin_name}，缺少插件路徑")
            
            self.logger.info(f"插件 {plugin_name} 重新載入成功")
            
        except Exception as e:
            self.logger.error(f"重新載入插件 {plugin_name} 失敗: {e}")
            raise
    
    async def enable_plugin(self, plugin_name: str):
        """
        啟用插件
        
        Args:
            plugin_name: 插件名稱
        """
        try:
            self.logger.info(f"啟用插件: {plugin_name}")
            
            plugin = self.plugin_registry.get_plugin(plugin_name)
            if not plugin:
                raise ValueError(f"找不到插件: {plugin_name}")
            
            if not plugin.is_running:
                await plugin.start()
                
                # 發布插件啟用事件
                if self.event_bus:
                    await self.event_bus.publish("plugin_enabled", {
                        "plugin_name": plugin_name
                    })
                
                self.logger.info(f"插件 {plugin_name} 啟用成功")
            else:
                self.logger.info(f"插件 {plugin_name} 已經在運行中")
            
        except Exception as e:
            self.logger.error(f"啟用插件 {plugin_name} 失敗: {e}")
            raise
    
    async def disable_plugin(self, plugin_name: str):
        """
        停用插件
        
        Args:
            plugin_name: 插件名稱
        """
        try:
            self.logger.info(f"停用插件: {plugin_name}")
            
            plugin = self.plugin_registry.get_plugin(plugin_name)
            if not plugin:
                raise ValueError(f"找不到插件: {plugin_name}")
            
            if plugin.is_running:
                await plugin.stop()
                
                # 發布插件停用事件
                if self.event_bus:
                    await self.event_bus.publish("plugin_disabled", {
                        "plugin_name": plugin_name
                    })
                
                self.logger.info(f"插件 {plugin_name} 停用成功")
            else:
                self.logger.info(f"插件 {plugin_name} 已經停止")
            
        except Exception as e:
            self.logger.error(f"停用插件 {plugin_name} 失敗: {e}")
            raise
    
    async def get_plugin_status(self, plugin_name: str) -> Dict[str, Any]:
        """
        獲取插件狀態
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            插件狀態字典
        """
        try:
            plugin = self.plugin_registry.get_plugin(plugin_name)
            if not plugin:
                return {"error": f"找不到插件: {plugin_name}"}
            
            return plugin.get_status()
            
        except Exception as e:
            self.logger.error(f"獲取插件 {plugin_name} 狀態失敗: {e}")
            return {"error": str(e)}
    
    async def list_plugins(self) -> List[Dict[str, Any]]:
        """
        列出所有插件
        
        Returns:
            插件列表
        """
        try:
            plugins = []
            for plugin_name in self.plugin_registry.list_plugins():
                plugin = self.plugin_registry.get_plugin(plugin_name)
                if plugin:
                    status = plugin.get_status()
                    plugins.append({
                        "name": plugin_name,
                        "status": status
                    })
            
            return plugins
            
        except Exception as e:
            self.logger.error(f"列出插件失敗: {e}")
            return []
    
    async def stop_all_plugins(self):
        """停止所有插件"""
        try:
            self.logger.info("停止所有插件...")
            
            for plugin_name in list(self.plugins.keys()):
                try:
                    await self.disable_plugin(plugin_name)
                except Exception as e:
                    self.logger.error(f"停止插件 {plugin_name} 失敗: {e}")
            
            self.logger.info("所有插件已停止")
            
        except Exception as e:
            self.logger.error(f"停止所有插件失敗: {e}")
    
    async def start_all_plugins(self):
        """啟動所有插件"""
        try:
            self.logger.info("啟動所有插件...")
            
            for plugin_name in self.plugin_registry.list_plugins():
                try:
                    await self.enable_plugin(plugin_name)
                except Exception as e:
                    self.logger.error(f"啟動插件 {plugin_name} 失敗: {e}")
            
            self.logger.info("所有插件已啟動")
            
        except Exception as e:
            self.logger.error(f"啟動所有插件失敗: {e}")
    
    async def get_plugin_dependencies(self, plugin_name: str) -> List[str]:
        """
        獲取插件依賴
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            依賴列表
        """
        try:
            plugin = self.plugin_registry.get_plugin(plugin_name)
            if not plugin:
                return []
            
            return getattr(plugin, 'dependencies', [])
            
        except Exception as e:
            self.logger.error(f"獲取插件 {plugin_name} 依賴失敗: {e}")
            return []
    
    async def check_plugin_dependencies(self, plugin_name: str) -> bool:
        """
        檢查插件依賴是否滿足
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            依賴是否滿足
        """
        try:
            dependencies = await self.get_plugin_dependencies(plugin_name)
            
            for dep in dependencies:
                if not self.plugin_registry.get_plugin(dep):
                    self.logger.warning(f"插件 {plugin_name} 缺少依賴: {dep}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"檢查插件 {plugin_name} 依賴失敗: {e}")
            return False
    
    async def cleanup(self):
        """清理模組管理器"""
        try:
            self.logger.info("清理模組管理器...")
            
            # 停止所有插件
            await self.stop_all_plugins()
            
            # 清理插件載入器
            if self.plugin_loader:
                await self.plugin_loader.cleanup()
            
            # 清理插件註冊表
            if self.plugin_registry:
                await self.plugin_registry.cleanup()
            
            # 清空插件列表
            self.plugins.clear()
            
            self.logger.info("模組管理器清理完成")
            
        except Exception as e:
            self.logger.error(f"清理模組管理器失敗: {e}")
    
    def get_plugin_count(self) -> int:
        """獲取插件數量"""
        return len(self.plugins)
    
    def get_running_plugin_count(self) -> int:
        """獲取運行中的插件數量"""
        return len([p for p in self.plugins.values() if p.is_running])
    
    def is_plugin_loaded(self, plugin_name: str) -> bool:
        """檢查插件是否已載入"""
        return plugin_name in self.plugins
    
    def is_plugin_running(self, plugin_name: str) -> bool:
        """檢查插件是否正在運行"""
        plugin = self.plugins.get(plugin_name)
        return plugin.is_running if plugin else False 