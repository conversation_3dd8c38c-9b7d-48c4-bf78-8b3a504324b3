# UnifiedBot 統一多功能機器人

## 📋 概述

UnifiedBot 是一個基於插件系統的統一多功能 Telegram 機器人，提供可擴充的架構設計。通過模組化和插件系統，可以輕鬆添加新功能或修改現有功能，而不需要重寫整個機器人。

## 🏗️ 架構設計

### 核心組件

- **UnifiedBot**: 統一機器人核心，管理所有插件和功能
- **ModuleManager**: 模組管理器，處理插件生命週期
- **EventBus**: 事件總線，處理插件間通信
- **ConfigManager**: 配置管理器，管理系統和插件配置
- **PluginRegistry**: 插件註冊表，管理已載入的插件

### 插件系統

- **BasePlugin**: 基礎插件介面，所有插件必須繼承
- **PluginLoader**: 插件載入器，動態載入插件檔案
- **PluginRegistry**: 插件註冊和管理
- 支援熱插拔、依賴管理、配置熱更新

## 🚀 快速開始

### 1. 環境設置

```bash
# 安裝依賴
pip install -r requirements.txt

# 設置環境變數
cp env.example .env
# 編輯 .env 檔案，設置 TELEGRAM_BOT_TOKEN
```

### 2. 啟動機器人

```bash
# 測試系統
python test_unified_bot.py

# 啟動機器人
python run_unified_bot.py
```

### 3. 在 Telegram 中使用

1. 搜索您的機器人
2. 發送 `/start` 開始使用
3. 使用 `/help` 查看可用命令

## 📦 插件系統

### 現有插件

#### AlertPlugin (警報插件)
- **功能**: 價格警報、成交量警報、技術指標警報
- **命令**: 
  - `/alert` - 顯示幫助
  - `/alerts` - 查看警報列表
  - `/add_alert` - 添加新警報
  - `/remove_alert` - 移除警報
  - `/clear_alerts` - 清除所有警報

#### SubscriptionPlugin (訂閱插件)
- **功能**: 標的訂閱、定期分析、自動報告
- **命令**:
  - `/subscribe` - 訂閱標的
  - `/unsubscribe` - 取消訂閱
  - `/subscriptions` - 查看訂閱列表
  - `/analysis` - 立即分析
  - `/report` - 生成報告

### 開發新插件

#### 1. 創建插件檔案

```python
# plugins/my_plugin.py
from core.plugins.base_plugin import BasePlugin

class MyPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.name = "MyPlugin"
        self.version = "1.0.0"
        self.description = "我的插件描述"
        self.author = "作者名稱"
        
        # 註冊命令
        self.register_command("mycommand", self._handle_my_command)
    
    async def initialize(self, config):
        # 初始化邏輯
        return True
    
    async def start(self):
        # 啟動邏輯
        return True
    
    async def stop(self):
        # 停止邏輯
        return True
    
    async def cleanup(self):
        # 清理邏輯
        return True
    
    async def _handle_my_command(self, command, update, context):
        # 命令處理邏輯
        await update.message.reply_text("我的命令回應")
        return True

def create_my_plugin():
    return MyPlugin()
```

#### 2. 載入插件

```bash
# 在 Telegram 中使用命令載入
/load_plugin plugins/my_plugin.py

# 或者將插件放在 plugins/ 目錄中自動載入
```

## 🔧 系統命令

### 基礎命令
- `/start` - 開始使用
- `/help` - 顯示幫助
- `/status` - 系統狀態
- `/plugins` - 插件列表

### 插件管理
- `/plugin_status <插件名>` - 查看插件狀態
- `/load_plugin <路徑>` - 載入插件
- `/unload_plugin <插件名>` - 卸載插件
- `/reload_plugin <插件名>` - 重新載入插件
- `/enable_plugin <插件名>` - 啟用插件
- `/disable_plugin <插件名>` - 停用插件

## 📁 目錄結構

```
FlipAdamTwice/
├── bots/
│   ├── unified_bot.py          # 統一機器人核心
│   ├── base_bot.py             # 基礎機器人類別
│   └── ...
├── core/
│   ├── management/
│   │   └── module_manager.py   # 模組管理器
│   ├── events/
│   │   └── event_bus.py        # 事件總線
│   ├── config/
│   │   └── config_manager.py   # 配置管理器
│   └── plugins/
│       ├── base_plugin.py      # 基礎插件介面
│       ├── plugin_loader.py    # 插件載入器
│       └── plugin_registry.py  # 插件註冊表
├── plugins/
│   ├── alert_plugin.py         # 警報插件
│   ├── subscription_plugin.py  # 訂閱插件
│   └── ...
├── config/
│   ├── config.yaml             # 主配置檔案
│   ├── plugins.yaml            # 插件配置檔案
│   ├── alerts.json             # 警報設定
│   └── subscriptions.json      # 訂閱設定
├── logs/
│   └── unified_bot.log         # 日誌檔案
├── run_unified_bot.py          # 啟動腳本
├── test_unified_bot.py         # 測試腳本
└── README_UnifiedBot.md        # 說明文檔
```

## 🔄 事件系統

### 事件類型

- `system.*` - 系統事件
- `plugin.*` - 插件事件
- `market.*` - 市場數據事件
- `alert.*` - 警報事件
- `analysis.*` - 分析事件
- `report.*` - 報告事件

### 事件訂閱

```python
# 在插件中訂閱事件
self.event_bus.subscribe("market.price_update", self._handle_price_update)

# 發布事件
await self.event_bus.publish("alert.triggered", {
    "alert_id": "price_alert_001",
    "message": "價格突破警報"
})
```

## ⚙️ 配置管理

### 主配置 (config/config.yaml)

```yaml
# 機器人配置
bot:
  name: "FlipAdam UnifiedBot"
  version: "1.0.0"
  
# 日誌配置
logging:
  level: "INFO"
  file: "logs/unified_bot.log"
  
# 插件配置
plugins:
  auto_load: true
  plugins_dir: "plugins"
  
# 事件配置
events:
  max_history: 1000
  enable_stats: true
```

### 插件配置 (config/plugins.yaml)

```yaml
AlertPlugin:
  enabled: true
  check_interval: 300
  alerts_file: "config/alerts.json"

SubscriptionPlugin:
  enabled: true
  analysis_interval: 1800
  subscriptions_file: "config/subscriptions.json"
```

## 🧪 測試

### 運行測試

```bash
# 運行完整測試
python test_unified_bot.py

# 測試特定組件
python -c "
import asyncio
from test_unified_bot import UnifiedBotTester
tester = UnifiedBotTester()
asyncio.run(tester.test_core_components())
"
```

### 測試覆蓋範圍

- ✅ 核心組件測試
- ✅ 插件系統測試
- ✅ 事件系統測試
- ✅ 配置管理測試
- ✅ 機器人初始化測試

## 🚨 故障排除

### 常見問題

1. **插件載入失敗**
   - 檢查插件檔案路徑
   - 確認插件繼承 BasePlugin
   - 檢查插件依賴

2. **事件處理失敗**
   - 確認事件總線已初始化
   - 檢查事件處理器註冊
   - 查看日誌錯誤信息

3. **配置載入失敗**
   - 檢查配置檔案格式
   - 確認檔案權限
   - 查看配置檔案路徑

### 日誌查看

```bash
# 查看即時日誌
tail -f logs/unified_bot.log

# 查看錯誤日誌
grep "ERROR" logs/unified_bot.log
```

## 🔮 未來規劃

### 計劃功能

- [ ] 更多分析插件
- [ ] 圖表生成插件
- [ ] 數據導出插件
- [ ] 用戶管理插件
- [ ] 權限控制系統
- [ ] Web 管理介面

### 技術改進

- [ ] 插件市場
- [ ] 自動更新系統
- [ ] 性能監控
- [ ] 備份恢復
- [ ] 多語言支援

## 📞 支援

如果您遇到問題或有建議，請：

1. 查看日誌檔案
2. 運行測試腳本
3. 檢查配置設定
4. 參考此文檔

## 📄 授權

本專案採用 MIT 授權條款。
