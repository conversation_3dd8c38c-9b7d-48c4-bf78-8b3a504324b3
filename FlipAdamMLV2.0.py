import pandas as pd
import numpy as np
import datetime as dt
import yfinance as yf
import matplotlib.pyplot as plt
from pandas.tseries.offsets import BDay
from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense

#==========================#
# 1. 資料抓取 (Data Acquisition)
#==========================#

symbol = "MSFT"  # 你可以改成其他股票代碼，例如 "AAPL"
end_date = dt.date.today()
start_date = end_date - dt.timedelta(days=365)  # 過去一年

df = yf.download(symbol, start=start_date, end=end_date, progress=False)

# 若欄位為 MultiIndex，扁平化
if isinstance(df.columns, pd.MultiIndex):
    df.columns = df.columns.get_level_values(0)

df = df[['Open', 'High', 'Low', 'Close', 'Volume']].dropna()
print(f"Downloaded {len(df)} rows of data for {symbol}")
print(df.tail(5))

#==========================#
# 2. 技術指標計算
#==========================#

# --- RSI (14日) ---
window_rsi = 14
delta = df['Close'].diff(1)
gain = delta.where(delta > 0, 0.0)
loss = -delta.where(delta < 0, 0.0)

avg_gain = gain.rolling(window_rsi).mean()
avg_loss = loss.rolling(window_rsi).mean()

avg_gain.iloc[:window_rsi] = gain.iloc[:window_rsi].mean()
avg_loss.iloc[:window_rsi] = loss.iloc[:window_rsi].mean()

for i in range(window_rsi, len(df)):
    avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (window_rsi - 1) + gain.iloc[i]) / window_rsi
    avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (window_rsi - 1) + loss.iloc[i]) / window_rsi

RS = avg_gain / avg_loss
df['RSI'] = 100 - (100 / (1 + RS))

# --- Bollinger Bands (20日) ---
window_bb = 20
df['MA20'] = df['Close'].rolling(window_bb).mean()
df['BB_std'] = df['Close'].rolling(window_bb).std()
df['BB_upper'] = df['MA20'] + 2 * df['BB_std']
df['BB_lower'] = df['MA20'] - 2 * df['BB_std']
df['BB_%B'] = ((df['Close'] - df['BB_lower']) / (df['BB_upper'] - df['BB_lower'])).squeeze()

# --- ATR (14日) ---
window_atr = 14
high_low = df['High'] - df['Low']
high_prevclose = (df['High'] - df['Close'].shift(1)).abs()
low_prevclose = (df['Low'] - df['Close'].shift(1)).abs()
df['TR'] = np.maximum.reduce([high_low, high_prevclose, low_prevclose])

df['ATR'] = 0.0
df['ATR'].iloc[:window_atr] = df['TR'].iloc[:window_atr].mean()
for i in range(window_atr, len(df)):
    df['ATR'].iat[i] = (df['ATR'].iat[i-1] * (window_atr - 1) + df['TR'].iat[i]) / window_atr

# 移動平均線
df['SMA50'] = df['Close'].rolling(50).mean()
df['EMA50'] = df['Close'].ewm(span=50, adjust=False).mean()

#==========================#
# 3. LSTM 模型訓練與預測
#==========================#

prices = df['Close'].values.reshape(-1, 1)
scaler = MinMaxScaler(feature_range=(0, 1))
scaled_prices = scaler.fit_transform(prices)

window_size = 60
X, y = [], []
for i in range(window_size, len(scaled_prices)):
    X.append(scaled_prices[i-window_size:i, 0])
    y.append(scaled_prices[i, 0])
X = np.array(X)
y = np.array(y)
X = X.reshape((X.shape[0], X.shape[1], 1))

test_size = 30
X_train, X_test = X[:-test_size], X[-test_size:]
y_train, y_test = y[:-test_size], y[-test_size:]

model = Sequential()
model.add(LSTM(50, activation='relu', return_sequences=False, input_shape=(window_size, 1)))
model.add(Dense(1))
model.compile(optimizer='adam', loss='mse')

model.fit(X_train, y_train, epochs=20, batch_size=16, verbose=0)

y_pred = model.predict(X_test)
y_pred_actual = scaler.inverse_transform(y_pred)
y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1))

mse = np.mean((y_pred_actual - y_test_actual)**2)
rmse = np.sqrt(mse)
print(f"\nLSTM MSE (last {test_size} days): {mse:.4f}, RMSE: {rmse:.4f}")

# 預測未來 30 天
last_sequence = scaled_prices[-window_size:].flatten().tolist()
future_predictions = []
predict_days = 30
for _ in range(predict_days):
    X_input = np.array(last_sequence[-window_size:]).reshape(1, window_size, 1)
    next_pred_scaled = model.predict(X_input, verbose=0)
    future_predictions.append(next_pred_scaled[0, 0])
    last_sequence.append(next_pred_scaled[0, 0])
future_predictions = scaler.inverse_transform(np.array(future_predictions).reshape(-1, 1))
pred_series = future_predictions.flatten()

print(f"\nPredicted prices for next {predict_days} days:")
print(pred_series)

#==========================#
# 4. Adam Theory：歷史走勢 + 一次反射 + 二次反射
#==========================#

# 1) 根據預測結果判斷未來趨勢 (簡易)
pred_change = pred_series[-1] - pred_series[0]
threshold = np.std(pred_series) * 0.1

if pred_change > threshold:
    trend_signal = "up"
elif pred_change < -threshold:
    trend_signal = "down"
else:
    trend_signal = "sideways"

# 初始化變數
pivot_index = None
start_index = None
pivot_price = None
start_price = None

lookback = 60
recent_window = df['Close'][-lookback:]

if trend_signal == "up":
    # 尋找谷底 (pivot)
    pivot_index = recent_window.idxmin()
    pivot_price = df.loc[pivot_index, 'Close']
    prev_window = df.loc[:pivot_index, 'Close']
    if len(prev_window) > 0:
        start_index = prev_window.idxmax()
        start_price = df.loc[start_index, 'Close']
    else:
        start_index, start_price = pivot_index, pivot_price
elif trend_signal == "down":
    # 尋找峰頂 (pivot)
    pivot_index = recent_window.idxmax()
    pivot_price = df.loc[pivot_index, 'Close']
    prev_window = df.loc[:pivot_index, 'Close']
    if len(prev_window) > 0:
        start_index = prev_window.idxmin()
        start_price = df.loc[start_index, 'Close']
    else:
        start_index, start_price = pivot_index, pivot_price
else:
    print("Market is sideways, no reflection paths will be generated.")

if pivot_index:
    # (A) 歷史實際走勢
    historical_path = df.loc[start_index:pivot_index, 'Close']
    hist_values = historical_path.values
    hist_duration = len(hist_values)

    # (B) 一次反射 (First Reflection)
    once_dates = pd.date_range(start=pivot_index + BDay(1), periods=hist_duration - 1, freq=BDay())
    hist_changes = np.diff(hist_values)
    once_changes = -hist_changes
    once_values = np.cumsum(np.array([pivot_price] + once_changes.tolist()))

    once_reflect_path_index = [pivot_index] + once_dates.to_list()
    once_reflect_path = pd.Series(data=once_values, index=once_reflect_path_index)

    # (C) 二次反射 (Second Reflection)
    first_reflect_changes = np.diff(once_values)
    second_reflect_changes = -first_reflect_changes
    second_reflect_values = np.cumsum(np.array([pivot_price] + second_reflect_changes.tolist()))

    twice_reflect_path = pd.Series(data=second_reflect_values, index=once_reflect_path_index)

    # 繪圖
    plt.figure(figsize=(10, 6))
    plt.plot(df.index, df['Close'], label='Stock Price', color='black')
    plt.plot(historical_path.index, hist_values, label='Historical Path', color='blue', linewidth=2)
    plt.plot(once_reflect_path.index, once_reflect_path.values, label='First Reflection', color='red', linestyle='--',
             linewidth=2)
    plt.plot(twice_reflect_path.index, twice_reflect_path.values, label='Second Reflection', color='green',
             linestyle='--', linewidth=2)
    plt.axvline(pivot_index, color='gray', linestyle='--', alpha=0.7)
    plt.scatter(pivot_index, pivot_price, color='orange', marker='o', s=100, label='Pivot Point')

    # 加入回歸趨勢線和標準差線
    x = np.arange(len(df))
    y = df['Close'].values
    coefficients = np.polyfit(x, y, 1)
    trend_line = np.polyval(coefficients, x)
    std_dev = np.std(y - trend_line)

    plt.plot(df.index, trend_line + std_dev, color='blue', linestyle='--', label='_nolegend_')
    plt.plot(df.index, trend_line - std_dev, color='blue', linestyle='--', label='_nolegend_')
    plt.plot(df.index, trend_line + 2 * std_dev, color='yellow', linestyle='--', label='_nolegend_')
    plt.plot(df.index, trend_line - 2 * std_dev, color='yellow', linestyle='--', label='_nolegend_')
    plt.plot(df.index, trend_line + 3 * std_dev, color='red', linestyle='--', label='_nolegend_')
    plt.plot(df.index, trend_line - 3 * std_dev, color='red', linestyle='--', label='_nolegend_')
    plt.legend(loc='upper left')
    # 收集並顯示文本信息
    text_info = [
        f"Based on LSTM prediction, the future trend is: {trend_signal}",
        f"Historical Path start: {start_index.date()}, price={start_price:.2f}",
        f"Pivot index: {pivot_index.date()}, price={pivot_price:.2f}"
    ]

    #==========================#
    # 5. 交易策略回測與勝率計算
    #==========================#

    prices_array = df['Close'].values
    local_max_idx = [i for i in range(1, len(prices_array)-1)
                     if prices_array[i] > prices_array[i-1] and prices_array[i] > prices_array[i+1]]
    local_min_idx = [i for i in range(1, len(prices_array)-1)
                     if prices_array[i] < prices_array[i-1] and prices_array[i] < prices_array[i+1]]
    pivots_idx = sorted(local_max_idx + local_min_idx)

    significant_pivots = []
    if pivots_idx:
        last_idx = pivots_idx[0]
        last_type = 'min' if last_idx in local_min_idx else 'max'
        significant_pivots.append(last_idx)
        for idx in pivots_idx[1:]:
            current_type = 'min' if idx in local_min_idx else 'max'
            if current_type == last_type:
                continue
            if abs(prices_array[idx] - prices_array[last_idx]) >= df['ATR'].iloc[idx]:
                significant_pivots.append(idx)
                last_idx = idx
                last_type = current_type

    wins = 0
    trades = 0
    for i in range(len(significant_pivots) - 1):
        s_idx = significant_pivots[i]
        e_idx = significant_pivots[i+1]
        if s_idx in local_min_idx and e_idx in local_max_idx:
            entry_price = prices_array[s_idx]
            exit_price = prices_array[e_idx]
            profit = exit_price - entry_price
        elif s_idx in local_max_idx and e_idx in local_min_idx:
            entry_price = prices_array[s_idx]
            exit_price = prices_array[e_idx]
            profit = entry_price - exit_price
        else:
            continue
        trades += 1
        if profit > 0:
            wins += 1

    if trades > 0:
        win_rate = wins / trades
        text_info.append(f"Backtest Result: Total Trades: {trades}, Winning Trades: {wins}, Win Rate: {win_rate*100:.2f}%")
    else:
        text_info.append("No significant pivot-based signals found for backtesting.")

    # 在圖的右下方顯示文本
    y_text = 0.1  # 起始 y 座標
    for info in text_info:
        plt.text(0.95, y_text, info, transform=plt.gca().transAxes, ha='right', va='top', fontsize=10)
        y_text -= 0.05  # 每行間隔

    plt.title(f"{symbol} Adam Theory: 1st & 2nd Reflection with Trend Line and Std Dev")
    plt.xlabel("Date")
    plt.ylabel("Price")
    plt.legend()
    plt.tight_layout()
    plt.show()
