"""
統一多功能機器人核心
支援插件系統和動態載入功能
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from telegram import Update, ReplyKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from telegram.error import TelegramError

# 導入核心組件
from core.management.module_manager import ModuleManager
from core.events.event_bus import EventBus
from core.config.config_manager import ConfigManager
from core.plugins.plugin_registry import PluginRegistry


class UnifiedBot:
    """
    統一多功能機器人核心類別
    提供插件系統、事件驅動架構和動態載入功能
    """
    
    def __init__(self, bot_token: str, bot_name: str = "UnifiedBot"):
        """
        初始化統一機器人
        
        Args:
            bot_token: Telegram Bot Token
            bot_name: 機器人名稱
        """
        self.bot_token = bot_token
        self.bot_name = bot_name
        self.application: Optional[Application] = None
        
        # 初始化核心組件
        self.module_manager = ModuleManager()
        self.event_bus = EventBus()
        self.config_manager = ConfigManager()
        self.plugin_registry = PluginRegistry()
        
        # 設置日誌
        self.logger = self._setup_logger()
        
        # 機器人狀態
        self.is_running = False
        self.is_initialized = False
        
        # 命令和處理器
        self.commands: Dict[str, Callable] = {}
        self.message_handlers: Dict[str, Callable] = {}
        
        self.logger.info(f"統一機器人 {self.bot_name} 初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """設置日誌系統"""
        logger = logging.getLogger(f"{self.bot_name}")
        logger.setLevel(logging.INFO)
        
        # 如果沒有處理器，添加一個
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def initialize(self):
        """初始化機器人"""
        try:
            self.logger.info("開始初始化統一機器人...")
            
            # 初始化配置管理器
            await self.config_manager.load_config()
            
            # 初始化事件總線
            await self.event_bus.initialize()
            
            # 初始化模組管理器
            await self.module_manager.initialize(self.event_bus, self.config_manager)
            
            # 初始化插件註冊表
            await self.plugin_registry.initialize()
            
            # 註冊基礎命令
            self._register_base_commands()
            
            self.is_initialized = True
            self.logger.info("統一機器人初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化失敗: {e}")
            raise
    
    def _register_base_commands(self):
        """註冊基礎命令"""
        self.commands.update({
            "/start": self._handle_start,
            "/help": self._handle_help,
            "/status": self._handle_status,
            "/plugins": self._handle_plugins,
            "/plugin_status": self._handle_plugin_status,
            "/load_plugin": self._handle_load_plugin,
            "/unload_plugin": self._handle_unload_plugin,
            "/reload_plugin": self._handle_reload_plugin,
            "/enable_plugin": self._handle_enable_plugin,
            "/disable_plugin": self._handle_disable_plugin,
        })
    
    async def start(self):
        """啟動機器人"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            self.logger.info("啟動統一機器人...")
            
            # 創建 Telegram 應用
            self.application = Application.builder().token(self.bot_token).build()
            
            # 註冊處理器
            self._register_handlers()
            
            # 啟動輪詢
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()
            
            self.is_running = True
            self.logger.info("統一機器人啟動成功，開始輪詢...")
            
        except Exception as e:
            self.logger.error(f"啟動失敗: {e}")
            raise
    
    def _register_handlers(self):
        """註冊所有處理器"""
        # 註冊系統命令處理器
        for command, handler in self.commands.items():
            command_name = command[1:] if command.startswith('/') else command
            self.application.add_handler(CommandHandler(command_name, handler))
            self.logger.info(f"註冊系統命令: /{command_name}")

        # 註冊插件命令處理器
        if self.module_manager and self.module_manager.plugin_registry:
            for plugin_name in self.module_manager.plugin_registry.list_plugins():
                plugin = self.module_manager.plugin_registry.get_plugin(plugin_name)
                if plugin and hasattr(plugin, 'commands'):
                    for command, handler in plugin.commands.items():
                        command_name = command[1:] if command.startswith('/') else command
                        # 創建包裝器來處理插件命令
                        wrapper_handler = self._create_plugin_command_wrapper(plugin, handler)
                        self.application.add_handler(CommandHandler(command_name, wrapper_handler))
                        self.logger.info(f"註冊插件命令: /{command_name} (來自 {plugin_name})")

        # 註冊消息處理器
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_message))

        # 註冊錯誤處理器
        self.application.add_error_handler(self._handle_error)

    def _create_plugin_command_wrapper(self, plugin, handler):
        """創建插件命令包裝器"""
        async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
            try:
                # 檢查處理器的參數簽名
                import inspect
                sig = inspect.signature(handler)
                params = list(sig.parameters.keys())

                # 如果處理器期望 command 參數，則傳遞命令名稱
                if len(params) >= 3 and 'command' in params:
                    command_name = f"/{update.message.text.split()[0][1:]}" if update.message.text.startswith('/') else ""
                    await handler(command_name, update, context)
                else:
                    # 否則只傳遞 update 和 context
                    await handler(update, context)
            except Exception as e:
                self.logger.error(f"插件命令處理失敗: {e}")
                await update.message.reply_text(f"❌ 命令執行失敗: {str(e)}")
        return wrapper

    async def stop(self):
        """停止機器人"""
        try:
            self.logger.info("停止統一機器人...")
            
            if self.application:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
            
            # 停止所有插件
            await self.module_manager.stop_all_plugins()
            
            self.is_running = False
            self.logger.info("統一機器人已停止")
            
        except Exception as e:
            self.logger.error(f"停止失敗: {e}")
            raise
    
    # 基礎命令處理器
    async def _handle_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /start 命令"""
        welcome_text = f"""
🤖 {self.bot_name} 統一系統

歡迎使用統一多功能機器人！

📋 可用命令：
/help - 顯示幫助
/status - 系統狀態
/plugins - 插件管理
/plugin_status <插件名> - 插件狀態

🔧 插件管理：
/load_plugin <路徑> - 載入插件
/unload_plugin <插件名> - 卸載插件
/reload_plugin <插件名> - 重新載入插件
/enable_plugin <插件名> - 啟用插件
/disable_plugin <插件名> - 停用插件
        """
        await update.message.reply_text(welcome_text)
    
    async def _handle_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /help 命令"""
        help_text = f"""
📚 {self.bot_name} 幫助文檔

🎯 核心功能：
• 插件系統管理
• 動態載入功能
• 事件驅動架構
• 配置熱更新

🔧 系統命令：
/start - 開始使用
/help - 顯示此幫助
/status - 系統狀態
/plugins - 插件列表

⚙️ 插件管理：
/plugin_status <插件名> - 查看插件狀態
/load_plugin <路徑> - 載入新插件
/unload_plugin <插件名> - 卸載插件
/reload_plugin <插件名> - 重新載入插件
/enable_plugin <插件名> - 啟用插件
/disable_plugin <插件名> - 停用插件

📊 系統信息：
• 已載入插件：{len(self.module_manager.plugin_registry.list_plugins()) if self.module_manager.plugin_registry else 0}
• 系統狀態：{'運行中' if self.is_running else '已停止'}
• 初始化狀態：{'已完成' if self.is_initialized else '未完成'}
        """
        await update.message.reply_text(help_text)
    
    async def _handle_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /status 命令"""
        status_text = f"""
📊 {self.bot_name} 系統狀態

🔄 運行狀態：
• 機器人：{'✅ 運行中' if self.is_running else '❌ 已停止'}
• 初始化：{'✅ 已完成' if self.is_initialized else '❌ 未完成'}

📦 插件狀態：
• 已載入：{len(self.module_manager.plugin_registry.list_plugins()) if self.module_manager.plugin_registry else 0} 個
• 運行中：{len([p for p in (self.module_manager.plugin_registry.list_plugins() if self.module_manager.plugin_registry else []) if self.module_manager.plugin_registry.get_plugin(p) and self.module_manager.plugin_registry.get_plugin(p).is_running])} 個

🔧 核心組件：
• 模組管理器：{'✅ 正常' if self.module_manager else '❌ 異常'}
• 事件總線：{'✅ 正常' if self.event_bus else '❌ 異常'}
• 配置管理器：{'✅ 正常' if self.config_manager else '❌ 異常'}
• 插件註冊表：{'✅ 正常' if self.plugin_registry else '❌ 異常'}

💾 記憶體使用：
• 系統記憶體：{self._get_memory_usage()} MB
        """
        await update.message.reply_text(status_text)
    
    async def _handle_plugins(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /plugins 命令"""
        # 使用 ModuleManager 的插件註冊表
        if not self.module_manager.plugin_registry:
            await update.message.reply_text("📦 插件系統尚未初始化")
            return

        plugins = self.module_manager.plugin_registry.list_plugins()

        if not plugins:
            await update.message.reply_text("📦 目前沒有載入任何插件")
            return

        plugins_text = "📦 已載入插件列表：\n\n"
        for i, plugin_name in enumerate(plugins, 1):
            plugin = self.module_manager.plugin_registry.get_plugin(plugin_name)
            if plugin:
                status = "🟢 運行中" if plugin.is_running else "🔴 已停止"
                version = getattr(plugin, 'version', '未知')
                description = getattr(plugin, 'description', '無描述')
                plugins_text += f"{i}. **{plugin_name}** {status}\n"
                plugins_text += f"   📝 版本: {version}\n"
                plugins_text += f"   📋 描述: {description}\n\n"

        plugins_text += f"總計：{len(plugins)} 個插件"
        await update.message.reply_text(plugins_text)
    
    async def _handle_plugin_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /plugin_status 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供插件名稱：/plugin_status <插件名>")
            return

        plugin_name = context.args[0]

        # 使用 ModuleManager 的插件註冊表
        if not self.module_manager.plugin_registry:
            await update.message.reply_text("❌ 插件系統尚未初始化")
            return

        plugin = self.module_manager.plugin_registry.get_plugin(plugin_name)

        if not plugin:
            await update.message.reply_text(f"❌ 找不到插件：{plugin_name}")
            return

        status = plugin.get_status()
        status_text = f"""
📊 插件狀態：{plugin_name}

🔄 運行狀態：
• 初始化：{'✅ 已完成' if status.get('initialized', False) else '❌ 未完成'}
• 運行中：{'✅ 是' if status.get('running', False) else '❌ 否'}

📋 插件信息：
• 版本：{status.get('version', '未知')}
• 描述：{status.get('description', '無描述')}
• 作者：{status.get('author', '未知')}

🔧 功能統計：
• 命令數量：{len(status.get('commands', {}))}
• 消息處理器：{len(status.get('message_handlers', {}))}
        """
        await update.message.reply_text(status_text)
    
    async def _handle_load_plugin(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /load_plugin 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供插件路徑：/load_plugin <插件路徑>")
            return
        
        plugin_path = context.args[0]
        
        try:
            await self.module_manager.load_plugin(plugin_path)
            await update.message.reply_text(f"✅ 插件載入成功：{plugin_path}")
        except Exception as e:
            await update.message.reply_text(f"❌ 插件載入失敗：{e}")
    
    async def _handle_unload_plugin(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /unload_plugin 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供插件名稱：/unload_plugin <插件名>")
            return
        
        plugin_name = context.args[0]
        
        try:
            await self.module_manager.unload_plugin(plugin_name)
            await update.message.reply_text(f"✅ 插件卸載成功：{plugin_name}")
        except Exception as e:
            await update.message.reply_text(f"❌ 插件卸載失敗：{e}")
    
    async def _handle_reload_plugin(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /reload_plugin 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供插件名稱：/reload_plugin <插件名>")
            return
        
        plugin_name = context.args[0]
        
        try:
            await self.module_manager.reload_plugin(plugin_name)
            await update.message.reply_text(f"✅ 插件重新載入成功：{plugin_name}")
        except Exception as e:
            await update.message.reply_text(f"❌ 插件重新載入失敗：{e}")
    
    async def _handle_enable_plugin(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /enable_plugin 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供插件名稱：/enable_plugin <插件名>")
            return
        
        plugin_name = context.args[0]
        
        try:
            await self.module_manager.enable_plugin(plugin_name)
            await update.message.reply_text(f"✅ 插件啟用成功：{plugin_name}")
        except Exception as e:
            await update.message.reply_text(f"❌ 插件啟用失敗：{e}")
    
    async def _handle_disable_plugin(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /disable_plugin 命令"""
        if not context.args:
            await update.message.reply_text("❌ 請提供插件名稱：/disable_plugin <插件名>")
            return
        
        plugin_name = context.args[0]
        
        try:
            await self.module_manager.disable_plugin(plugin_name)
            await update.message.reply_text(f"✅ 插件停用成功：{plugin_name}")
        except Exception as e:
            await update.message.reply_text(f"❌ 插件停用失敗：{e}")
    
    async def _handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理一般消息"""
        message_text = update.message.text

        # 轉發給所有啟用的插件
        if self.module_manager.plugin_registry:
            handled = False
            for plugin_name in self.module_manager.plugin_registry.list_plugins():
                plugin = self.module_manager.plugin_registry.get_plugin(plugin_name)
                if plugin and plugin.is_running:
                    try:
                        result = await plugin.handle_message(message_text, update, context)
                        if result:
                            handled = True
                    except Exception as e:
                        self.logger.error(f"插件 {plugin_name} 處理消息失敗: {e}")

            # 如果沒有插件處理，發送預設回應
            if not handled:
                await update.message.reply_text("收到您的消息，但目前沒有插件處理此類消息。")
        else:
            await update.message.reply_text("插件系統尚未初始化。")
    
    async def _handle_error(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理錯誤"""
        self.logger.error(f"處理更新時發生錯誤: {context.error}")
        
        if update and update.effective_message:
            await update.effective_message.reply_text(
                "❌ 處理您的請求時發生錯誤，請稍後再試。"
            )
    
    def _get_memory_usage(self) -> float:
        """獲取記憶體使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return round(process.memory_info().rss / 1024 / 1024, 2)
        except ImportError:
            return 0.0
    
    async def run(self):
        """運行機器人（阻塞模式）"""
        try:
            await self.start()
            
            # 保持運行
            while self.is_running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            self.logger.info("收到中斷信號，正在停止...")
        except Exception as e:
            self.logger.error(f"運行時發生錯誤: {e}")
        finally:
            await self.stop()


# 便捷函數
async def create_unified_bot(bot_token: str, bot_name: str = "UnifiedBot") -> UnifiedBot:
    """創建統一機器人實例"""
    bot = UnifiedBot(bot_token, bot_name)
    await bot.initialize()
    return bot


if __name__ == "__main__":
    # 測試代碼
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    if not bot_token:
        print("❌ 請設置 TELEGRAM_BOT_TOKEN 環境變數")
        exit(1)
    
    async def main():
        bot = await create_unified_bot(bot_token, "測試統一機器人")
        await bot.run()
    
    asyncio.run(main()) 