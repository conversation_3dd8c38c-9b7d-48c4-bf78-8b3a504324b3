#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試 FlipAdam 警報機器人
"""

import os
import json
import asyncio
from telegram_bot_alert import AlertBot

async def quick_test():
    """快速測試"""
    print("=== 快速測試 FlipAdam 警報機器人 ===")
    
    # 建立警報機器人實例
    alert_bot = AlertBot()
    
    # 測試1：獲取所有標的
    symbols = alert_bot.get_all_symbols()
    print(f"找到 {len(symbols)} 個標的: {symbols}")
    
    if not symbols:
        print("❌ 沒有找到任何標的資料")
        return
    
    # 測試2：載入第一個標的的資料
    test_symbol = symbols[0]
    data_list = alert_bot.load_symbol_data(test_symbol, hours=24)
    print(f"載入 {test_symbol} 的資料: {len(data_list)} 筆")
    
    if not data_list:
        print("❌ 沒有資料")
        return
    
    # 測試3：檢查單筆資料警示
    print(f"\n測試單筆資料警示...")
    single_alerts = alert_bot.check_single_data_alerts(data_list)
    print(f"發現 {len(single_alerts)} 個單筆警示")
    
    for alert in single_alerts:
        print(f"  - {alert['type']}")
        message = alert_bot.format_alert_message(alert)
        print(f"    訊息: {message[:100]}...")
    
    # 測試4：分析所有標的
    print(f"\n測試分析所有標的...")
    await alert_bot.analyze_all_symbols()
    
    print("\n=== 快速測試完成 ===")

if __name__ == '__main__':
    asyncio.run(quick_test()) 