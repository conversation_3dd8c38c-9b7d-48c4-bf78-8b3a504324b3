"""
重構後的通知機器人
繼承 BaseBot 類別，使用模組化結構，專注於排名和通知功能
"""

import logging
import asyncio
import pandas as pd
import os
from datetime import datetime
from typing import Dict, List, Optional, Any

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import ContextTypes, CallbackQueryHandler

from bots.base_bot import BaseBot
from bots.keyboards.common_keyboards import CommonKeyboards
from bots.handlers.common_handlers import CommonHandlers

from core.analysis.flip_adam_1d import getMLFlipAdambySymbolWithData, get_cffi_session
from core.utils.memory_manager import cleanup_variables, log_memory_usage, monitor_memory


class NotifyBot(BaseBot):
    """通知機器人類別"""
    
    def __init__(self):
        """初始化通知機器人"""
        super().__init__(bot_name="NotifyBot")
        
        # 初始化共用處理器
        self.common_handlers = CommonHandlers(self)
        
        # 分析結果儲存
        self.analysis_results: List[Dict] = []
        
        # CSV 檔案對應
        self.csv_files = {
            'fund': 'data/raw/stock_name_hold_fundsonly_SortbyValue.csv',
            'stock': 'data/raw/stock_name_hold_stockonly_SortbyValue.csv',
            'crypto': 'data/raw/stock_names_coin.csv',
            'index': 'data/raw/stock_names_watch_index.csv'
        }
        
        # 註冊命令和處理器
        self._register_handlers()
    
    def _register_handlers(self):
        """註冊命令和訊息處理器"""
        # 註冊基本命令
        self.register_command("start")(self.start_command)
        self.register_command("help")(self.help_command)
        self.register_command("status")(self.status_command)
        self.register_command("ranking")(self.ranking_command)
        
        # 註冊訊息處理器
        self.register_message_handler(r"^(📊 基金排名|📈 股票排名|🪙 加密貨幣排名|📊 指數排名|❓ 幫助)$")(self.handle_main_menu)
        self.register_message_handler(r"^(🔙 返回主選單|🔙 返回)$")(self.handle_back_to_main)
        
        # 註冊回調查詢處理器
        self.register_callback_handler(r"^ranking_(fund|stock|crypto|index)$")(self.handle_ranking_callback)
    
    def register_callback_handler(self, pattern: str):
        """裝飾器：註冊回調查詢處理器"""
        def decorator(func):
            # 這裡需要手動添加到 application
            if hasattr(self, 'application') and self.application:
                self.application.add_handler(CallbackQueryHandler(func, pattern=pattern))
            return func
        return decorator
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /start 命令"""
        welcome_message = (
            "📊 歡迎使用 FlipAdam 通知系統！\n\n"
            "📈 這是一個專業的金融排名通知機器人，提供：\n"
            "• 基金漲跌幅排名\n"
            "• 股票漲跌幅排名\n"
            "• 加密貨幣漲跌幅排名\n"
            "• 指數漲跌幅排名\n"
            "• 即時市場監控\n"
            "• 智能排名分析\n\n"
            "請選擇您想要查看的排名："
        )
        
        keyboard = self.create_notify_keyboard()
        await self.send_message(update, context, welcome_message, reply_markup=keyboard)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /help 命令"""
        help_message = (
            "📚 FlipAdam 通知系統使用說明\n\n"
            "🔧 主要功能：\n"
            "• /start - 開始使用機器人\n"
            "• /help - 顯示此說明\n"
            "• /ranking - 查看所有排名\n"
            "• /status - 查看系統狀態\n\n"
            "📊 排名功能：\n"
            "• 基金漲跌幅排名\n"
            "• 股票漲跌幅排名\n"
            "• 加密貨幣漲跌幅排名\n"
            "• 指數漲跌幅排名\n\n"
            "📈 分析特色：\n"
            "• 即時漲跌幅計算\n"
            "• 趨勢信號分析\n"
            "• 交易模擬建議\n"
            "• 回測勝率統計\n\n"
            "💡 使用提示：\n"
            "• 點擊選單按鈕快速查看排名\n"
            "• 使用內聯按鈕進行詳細分析\n"
            "• 定期查看排名變化"
        )
        
        await self.send_message(update, context, help_message, reply_markup=CommonKeyboards.back_button())
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /status 命令"""
        try:
            status_message = (
                f"📊 通知系統狀態報告\n\n"
                f"🤖 機器人狀態: 🟢 正常運行\n"
                f"📈 分析結果: {len(self.analysis_results)} 個\n"
                f"📋 支援類別: 基金、股票、加密貨幣、指數\n"
                f"⏰ 最後更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            await self.send_message(update, context, status_message, reply_markup=CommonKeyboards.back_button())
            
        except Exception as e:
            self.logger.error(f"獲取狀態時發生錯誤: {e}")
            await self.send_message(update, context, "❌ 無法獲取系統狀態，請稍後再試。")
    
    def create_notify_keyboard(self) -> ReplyKeyboardMarkup:
        """創建通知主選單鍵盤"""
        keyboard = [
            [KeyboardButton("📊 基金排名"), KeyboardButton("📈 股票排名")],
            [KeyboardButton("🪙 加密貨幣排名"), KeyboardButton("📊 指數排名")],
            [KeyboardButton("❓ 幫助")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    
    async def handle_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理主選單選擇"""
        text = update.message.text
        
        if text == "📊 基金排名":
            await self.generate_ranking_report(update, context, "fund")
        elif text == "📈 股票排名":
            await self.generate_ranking_report(update, context, "stock")
        elif text == "🪙 加密貨幣排名":
            await self.generate_ranking_report(update, context, "crypto")
        elif text == "📊 指數排名":
            await self.generate_ranking_report(update, context, "index")
        elif text == "❓ 幫助":
            await self.help_command(update, context)
    
    async def handle_back_to_main(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理返回主選單"""
        await self.send_message(
            update, context,
            "🔙 返回主選單",
            reply_markup=self.create_notify_keyboard()
        )
    
    async def ranking_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /ranking 命令"""
        args = context.args
        category = args[0] if args else "stock"
        
        if category not in ["fund", "stock", "crypto", "index"]:
            await self.send_message(update, context, "無效的類別。請使用：fund, stock, crypto, index")
            return
        
        await self.generate_ranking_report(update, context, category)
    
    async def generate_ranking_report(self, update: Update, context: ContextTypes.DEFAULT_TYPE, category: str):
        """生成排名報告"""
        try:
            await self.send_message(update, context, f"📊 正在生成 {category} 排名報告...")
            
            # 這裡實現實際的排名分析邏輯
            # 暫時使用模擬數據
            results = await self.analyze_category(category)
            
            if not results:
                await self.send_message(update, context, f"❌ 無法取得 {category} 的排名資料")
                return
            
            # 格式化排名訊息
            message = self.format_ranking_message(results, category)
            
            # 創建內聯鍵盤
            inline_keyboard = [
                [
                    InlineKeyboardButton("📊 詳細分析", callback_data=f"ranking_{category}"),
                    InlineKeyboardButton("🔄 重新整理", callback_data=f"refresh_{category}")
                ],
                [
                    InlineKeyboardButton("📈 趨勢圖表", callback_data=f"chart_{category}"),
                    InlineKeyboardButton("📋 歷史記錄", callback_data=f"history_{category}")
                ]
            ]
            
            reply_markup = InlineKeyboardMarkup(inline_keyboard)
            
            await self.send_message(update, context, message, reply_markup=reply_markup)
            
        except Exception as e:
            self.logger.error(f"生成排名報告錯誤: {e}")
            await self.send_message(update, context, f"❌ 生成 {category} 排名報告時發生錯誤")
    
    async def handle_ranking_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理排名回調查詢"""
        query = update.callback_query
        await query.answer()
        
        # 解析回調數據
        data = query.data
        if data.startswith("ranking_"):
            category = data.split("_")[1]
            await self.show_detailed_analysis(query, context, category)
        elif data.startswith("refresh_"):
            category = data.split("_")[1]
            await self.refresh_ranking(query, context, category)
        elif data.startswith("chart_"):
            category = data.split("_")[1]
            await self.show_trend_chart(query, context, category)
        elif data.startswith("history_"):
            category = data.split("_")[1]
            await self.show_history(query, context, category)
    
    async def show_detailed_analysis(self, query, context: ContextTypes.DEFAULT_TYPE, category: str):
        """顯示詳細分析"""
        try:
            message = f"📊 {category.upper()} 詳細分析\n\n"
            message += "🔍 分析內容：\n"
            message += "• 漲跌幅統計\n"
            message += "• 趨勢信號分析\n"
            message += "• 交易模擬結果\n"
            message += "• 風險評估\n\n"
            message += "⏰ 分析時間：" + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            await query.edit_message_text(text=message)
            
        except Exception as e:
            self.logger.error(f"顯示詳細分析錯誤: {e}")
            await query.edit_message_text(text="❌ 顯示詳細分析時發生錯誤")
    
    async def refresh_ranking(self, query, context: ContextTypes.DEFAULT_TYPE, category: str):
        """重新整理排名"""
        try:
            await query.edit_message_text(text=f"🔄 正在重新整理 {category} 排名...")
            
            # 重新分析
            results = await self.analyze_category(category)
            
            if not results:
                await query.edit_message_text(text=f"❌ 無法取得 {category} 的排名資料")
                return
            
            # 格式化排名訊息
            message = self.format_ranking_message(results, category)
            
            # 創建內聯鍵盤
            inline_keyboard = [
                [
                    InlineKeyboardButton("📊 詳細分析", callback_data=f"ranking_{category}"),
                    InlineKeyboardButton("🔄 重新整理", callback_data=f"refresh_{category}")
                ],
                [
                    InlineKeyboardButton("📈 趨勢圖表", callback_data=f"chart_{category}"),
                    InlineKeyboardButton("📋 歷史記錄", callback_data=f"history_{category}")
                ]
            ]
            
            reply_markup = InlineKeyboardMarkup(inline_keyboard)
            
            await query.edit_message_text(text=message, reply_markup=reply_markup)
            
        except Exception as e:
            self.logger.error(f"重新整理排名錯誤: {e}")
            await query.edit_message_text(text="❌ 重新整理排名時發生錯誤")
    
    async def show_trend_chart(self, query, context: ContextTypes.DEFAULT_TYPE, category: str):
        """顯示趨勢圖表"""
        try:
            message = f"📈 {category.upper()} 趨勢圖表\n\n"
            message += "📊 圖表功能：\n"
            message += "• 價格走勢圖\n"
            message += "• 技術指標\n"
            message += "• 趨勢線分析\n"
            message += "• 支撐阻力位\n\n"
            message += "⏰ 更新時間：" + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            await query.edit_message_text(text=message)
            
        except Exception as e:
            self.logger.error(f"顯示趨勢圖表錯誤: {e}")
            await query.edit_message_text(text="❌ 顯示趨勢圖表時發生錯誤")
    
    async def show_history(self, query, context: ContextTypes.DEFAULT_TYPE, category: str):
        """顯示歷史記錄"""
        try:
            message = f"📋 {category.upper()} 歷史記錄\n\n"
            message += "📅 歷史數據：\n"
            message += "• 過去7天排名\n"
            message += "• 過去30天排名\n"
            message += "• 季度排名變化\n"
            message += "• 年度排名趨勢\n\n"
            message += "⏰ 記錄時間：" + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            await query.edit_message_text(text=message)
            
        except Exception as e:
            self.logger.error(f"顯示歷史記錄錯誤: {e}")
            await query.edit_message_text(text="❌ 顯示歷史記錄時發生錯誤")
    
    async def analyze_category(self, category: str) -> List[Dict]:
        """分析指定類別"""
        try:
            # 這裡實現實際的分析邏輯
            # 暫時返回模擬數據
            mock_results = [
                {
                    'symbol': 'AAPL',
                    'name': 'Apple Inc.',
                    'percentage_change': 2.5,
                    'direction': '漲',
                    'trend_signal': '上升趨勢',
                    'reflection_direction': '正向',
                    'current_trend_line': 150.25,
                    'current_trailing_stop': 145.50,
                    'current_atr': 3.25,
                    'best_atr_period': 14,
                    'best_atr_multiple': 2.0,
                    'latest_signals': ['做多'],
                    'win_rate': 0.65,
                    'total_trades': 20,
                    'winning_trades': 13
                },
                {
                    'symbol': 'GOOGL',
                    'name': 'Alphabet Inc.',
                    'percentage_change': -1.2,
                    'direction': '跌',
                    'trend_signal': '下降趨勢',
                    'reflection_direction': '負向',
                    'current_trend_line': 2800.75,
                    'current_trailing_stop': 2850.25,
                    'current_atr': 45.50,
                    'best_atr_period': 14,
                    'best_atr_multiple': 1.5,
                    'latest_signals': ['做空'],
                    'win_rate': 0.55,
                    'total_trades': 15,
                    'winning_trades': 8
                }
            ]
            
            return mock_results
            
        except Exception as e:
            self.logger.error(f"分析 {category} 錯誤: {e}")
            return []
    
    def format_ranking_message(self, results: List[Dict], category: str) -> str:
        """格式化排名訊息"""
        if not results:
            return f"❌ 無法取得 {category} 的排名資料"
        
        # 按漲跌幅排序（從大到小）
        sorted_results = sorted(results, key=lambda x: x['percentage_change'], reverse=True)
        
        message = f"📊 {category.upper()} 漲跌幅排行\n"
        message += f"📅 分析時間: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
        message += f"📈 總計分析: {len(results)} 個標的\n"
        message += f"🔄 資料同步: ✅ 與產圖邏輯完全一致\n\n"
        
        # 顯示前5名的詳細資訊
        message += "🏆 前5名詳細分析:\n"
        for i, result in enumerate(sorted_results[:5], 1):
            direction_emoji = "🚀" if result['direction'] == '漲' else "📉" if result['direction'] == '跌' else "➡️"
            message += f"{i}. {direction_emoji} {result['symbol']} ({result['name']})\n"
            message += f"   漲跌幅: {result['percentage_change']:+.2f}% | 趨勢: {result['trend_signal']}\n"
            message += f"   計算: {result['reflection_direction']} | 勝率: {result['win_rate']*100:.1f}%\n"
            
            # 顯示目前信號
            latest_signals = result.get('latest_signals', [])
            if latest_signals:
                signal_emojis = []
                for signal in latest_signals:
                    if signal == "做多":
                        signal_emojis.append("🟢")
                    elif signal == "做空":
                        signal_emojis.append("🔴")
                    elif signal == "跳空":
                        signal_emojis.append("🟣")
                message += f"   信號: {' '.join(signal_emojis)} {' '.join(latest_signals)}\n"
            else:
                message += f"   信號: ⚪ 無信號\n"
            
            message += "\n"
        
        return message
    
    def get_symbol_name(self, symbol: str, category: str) -> str:
        """獲取標的名稱"""
        try:
            csv_file = self.csv_files.get(category)
            if csv_file and os.path.exists(csv_file):
                df = pd.read_csv(csv_file)
                if symbol in df['Symbol'].values:
                    return df[df['Symbol'] == symbol]['Name'].iloc[0]
        except Exception as e:
            self.logger.error(f"獲取 {symbol} 名稱時發生錯誤: {e}")
        return symbol


async def main():
    """主函數"""
    bot = NotifyBot()
    
    try:
        await bot.start()
        
        # 保持運行
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("正在停止機器人...")
    except Exception as e:
        print(f"發生錯誤: {e}")
    finally:
        await bot.stop()


if __name__ == "__main__":
    asyncio.run(main()) 