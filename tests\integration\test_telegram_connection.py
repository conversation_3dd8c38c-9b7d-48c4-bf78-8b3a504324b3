#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Telegram Bot 連接
"""

import asyncio
import logging
from telegram import Bo<PERSON>
from telegram.error import TelegramError

# 設定
TOKEN = "**********************************************"
USER_ID = 1057529499

# 日誌設定
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_telegram_connection():
    """測試 Telegram 連接"""
    print("=== 測試 Telegram Bot 連接 ===")
    
    try:
        # 建立 Bot 實例
        bot = Bot(token=TOKEN)
        
        # 測試獲取 Bot 資訊
        print("1. 測試獲取 Bot 資訊...")
        bot_info = await bot.get_me()
        print(f"✅ Bot 名稱: {bot_info.first_name}")
        print(f"✅ Bot 用戶名: @{bot_info.username}")
        print(f"✅ Bot ID: {bot_info.id}")
        
        # 測試發送訊息
        print("\n2. 測試發送訊息...")
        test_message = "🧪 **測試訊息**\n\n這是 FlipAdam 警報機器人的連接測試訊息。\n\n✅ 連接正常！"
        
        await bot.send_message(
            chat_id=USER_ID,
            text=test_message,
            parse_mode='Markdown'
        )
        print("✅ 測試訊息發送成功！")
        
        # 測試發送圖片
        print("\n3. 測試發送圖片...")
        try:
            # 檢查是否有趨勢圖可以發送
            import os
            figure_dir = 'figure'
            if os.path.exists(figure_dir):
                files = [f for f in os.listdir(figure_dir) if f.endswith('.png')]
                if files:
                    test_image = os.path.join(figure_dir, files[0])
                    await bot.send_photo(
                        chat_id=USER_ID,
                        photo=open(test_image, 'rb'),
                        caption="📈 測試圖片：趨勢圖"
                    )
                    print("✅ 測試圖片發送成功！")
                else:
                    print("ℹ️ 沒有找到圖片檔案")
            else:
                print("ℹ️ figure 目錄不存在")
        except Exception as e:
            print(f"⚠️ 圖片發送失敗: {e}")
        
        print("\n=== 測試完成 ===")
        print("✅ Telegram Bot 連接正常！")
        
    except TelegramError as e:
        print(f"❌ Telegram 錯誤: {e}")
        print("請檢查：")
        print("1. Token 是否正確")
        print("2. 網路連接是否正常")
        print("3. Bot 是否已啟動")
        
    except Exception as e:
        print(f"❌ 其他錯誤: {e}")
        logger.error(f"錯誤詳情: {e}")

if __name__ == '__main__':
    asyncio.run(test_telegram_connection()) 