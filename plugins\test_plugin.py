"""
測試插件
用於驗證插件系統的功能
"""

from core.plugins.base_plugin import BasePlugin


class TestPlugin(BasePlugin):
    """測試插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "TestPlugin"
        self.version = "1.0.0"
        self.description = "測試插件，用於驗證插件系統"
        self.author = "System"
        self.dependencies = []
    
    async def initialize(self, config: dict) -> bool:
        """初始化插件"""
        try:
            self.logger.info("初始化測試插件...")
            self.config = config
            self.is_initialized = True
            self.logger.info("測試插件初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"測試插件初始化失敗: {e}")
            return False
    
    async def start(self) -> bool:
        """啟動插件"""
        try:
            self.logger.info("啟動測試插件...")
            self.is_running = True
            self.logger.info("測試插件啟動完成")
            return True
        except Exception as e:
            self.logger.error(f"測試插件啟動失敗: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止插件"""
        try:
            self.logger.info("停止測試插件...")
            self.is_running = False
            self.logger.info("測試插件停止完成")
            return True
        except Exception as e:
            self.logger.error(f"測試插件停止失敗: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """清理插件"""
        try:
            self.logger.info("清理測試插件...")
            self.is_initialized = False
            self.is_running = False
            self.logger.info("測試插件清理完成")
            return True
        except Exception as e:
            self.logger.error(f"測試插件清理失敗: {e}")
            return False
    
    async def handle_test_command(self, update, context):
        """處理測試命令"""
        await update.message.reply_text("這是來自測試插件的回應！")
    
    def get_help_text(self) -> str:
        """獲取幫助文字"""
        return "TestPlugin: 這是一個測試插件，用於驗證插件系統的功能。" 