"""
重構後的訂閱機器人
繼承 BaseBot 類別，使用模組化結構
"""

import logging
import asyncio
import sys
import pandas as pd
import os
import re
import yfinance as yf
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, ReplyKeyboardRemove, InputMediaPhoto
from telegram.ext import ContextTypes
from telegram.error import RetryAfter, TimedOut, NetworkError

from bots.base_bot import BaseBot
from bots.keyboards.common_keyboards import CommonKeyboards
from bots.handlers.common_handlers import CommonHandlers

from core.analysis.flip_adam_30min import getMLFlipAdambySymbol, getMLFlipAdambySymbolWithData, get_cffi_session
from core.analysis.flip_adam_1hr import getMLFlipAdambySymbol as getMLFlipAdambySymbol1HR, getMLFlipAdambySymbolWithData as getMLFlipAdambySymbolWithData1HR
from core.analysis.flip_adam_4hr import getMLFlipAdambySymbol as getMLFlipAdambySymbol4HR, getMLFlipAdambySymbolWithData as getMLFlipAdambySymbolWithData4HR
from core.utils.memory_manager import cleanup_variables, log_memory_usage, monitor_memory


class SubscriptionBot(BaseBot):
    """訂閱機器人類別"""
    
    def __init__(self):
        """初始化訂閱機器人"""
        super().__init__(bot_name="SubscriptionBot")
        
        # 初始化共用處理器
        self.common_handlers = CommonHandlers(self)
        
        # 訂閱管理
        self.subscriptions: Dict[int, Dict[str, int]] = {}  # 用戶ID -> {symbol: interval_minutes}
        self.subscription_tasks: Dict[int, Dict[str, str]] = {}  # 用戶ID -> {symbol: task_id}
        
        # 用戶狀態管理
        self.user_states: Dict[int, str] = {}  # 用戶ID -> 當前狀態
        
        # 分頁管理
        self.user_pages: Dict[int, int] = {}  # 用戶ID -> 當前頁面
        
        # 頻率傳送清單管理
        self.frequency_lists: Dict[str, List[str]] = {}  # 載入的頻率傳送清單
        self.user_selected_list: Dict[int, str] = {}  # 用戶ID -> 選擇的清單
        
        # CSV 檔案路徑
        self.csv_files = [
            'data/raw/stock_name_hold_fundsonly_SortbyValue.csv',
            'data/raw/stock_name_hold_stockonly_SortbyValue.csv',
            'data/raw/stock_names_Ady.csv',
            'data/raw/stock_names_coin.csv',
            'data/raw/stock_names_ETF.csv',
            'data/raw/stock_names_watch_index.csv',
        ]
        
        # 載入初始數據
        self._load_initial_data()
        
        # 註冊命令和處理器
        self._register_handlers()
    
    def _load_initial_data(self):
        """載入初始數據"""
        try:
            self.load_frequency_lists()
            self.load_subscriptions()
            self.logger.info("初始數據載入完成")
        except Exception as e:
            self.logger.error(f"載入初始數據時發生錯誤: {e}")
    
    def _register_handlers(self):
        """註冊命令和訊息處理器"""
        # 註冊基本命令
        self.register_command("start")(self.start_command)
        self.register_command("help")(self.help_command)
        self.register_command("status")(self.status_command)
        self.register_command("subscribe")(self.subscribe_command)
        self.register_command("unsubscribe")(self.unsubscribe_command)
        self.register_command("subscriptions")(self.subscriptions_command)
        self.register_command("stop")(self.stop_command)
        
        # 註冊訊息處理器
        self.register_message_handler(r"^(📈 訂閱|❌ 取消訂閱|📋 檢查訂閱|🔄 頻率傳送清單|⏰ 時間版本選擇|❓ 幫助)$")(self.handle_main_menu)
        self.register_message_handler(r"^(5分鐘|10分鐘|15分鐘|30分鐘)$")(self.handle_interval_selection)
        self.register_message_handler(r"^(🌅 上午清單|🌙 晚上清單|🌞 全日清單)$")(self.handle_frequency_list_selection)
        self.register_message_handler(r"^(🔙 返回主選單|🔙 返回)$")(self.handle_back_to_main)
        self.register_message_handler(r"^(上一頁|下一頁)$")(self.handle_pagination)
        self.register_message_handler(r"^[A-Z]{1,5}(-[A-Z]{3})?$")(self.handle_symbol_input)
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /start 命令"""
        welcome_message = (
            "🎉 歡迎使用 FlipAdam 訂閱系統！\n\n"
            "📊 這是一個專業的金融分析訂閱機器人，提供：\n"
            "• 多時間框架技術分析訂閱\n"
            "• 股票、基金、加密貨幣分析\n"
            "• 智能頻率傳送功能\n"
            "• 即時市場監控\n\n"
            "請選擇您想要使用的功能："
        )
        
        keyboard = self.create_subscription_keyboard()
        await self.send_message(update, context, welcome_message, reply_markup=keyboard)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /help 命令"""
        help_message = (
            "📚 FlipAdam 訂閱系統使用說明\n\n"
            "🔧 主要功能：\n"
            "• /start - 開始使用機器人\n"
            "• /help - 顯示此說明\n"
            "• /subscribe <代碼> - 快速訂閱\n"
            "• /unsubscribe <代碼> - 取消訂閱\n"
            "• /subscriptions - 查看訂閱\n"
            "• /status - 查看系統狀態\n\n"
            "📊 訂閱功能：\n"
            "• 支援 5分鐘、10分鐘、15分鐘、30分鐘頻率\n"
            "• 自動生成技術分析圖表\n"
            "• 多版本分析（30分鐘、1小時、4小時、日線）\n\n"
            "📈 頻率傳送：\n"
            "• 上午清單、晚上清單、全日清單\n"
            "• 自動批量訂閱管理\n"
            "• 智能提醒功能\n\n"
            "💡 使用提示：\n"
            "• 點擊選單按鈕快速操作\n"
            "• 輸入股票代碼進行訂閱\n"
            "• 使用頻率傳送清單批量管理"
        )
        
        await self.send_message(update, context, help_message, reply_markup=CommonKeyboards.back_button())
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /status 命令"""
        try:
            user_id = update.effective_user.id
            user_subs = self.get_user_subscriptions(user_id)
            
            status_message = (
                f"📊 系統狀態報告\n\n"
                f"🤖 機器人狀態: 🟢 正常運行\n"
                f"📈 您的訂閱: {len(user_subs)} 個\n"
                f"🔄 活躍任務: {len(self.subscription_tasks.get(user_id, {}))} 個\n"
                f"📋 頻率清單: {len(self.frequency_lists)} 個\n"
                f"⏰ 最後更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            
            await self.send_message(update, context, status_message, reply_markup=CommonKeyboards.back_button())
            
        except Exception as e:
            self.logger.error(f"獲取狀態時發生錯誤: {e}")
            await self.send_message(update, context, "❌ 無法獲取系統狀態，請稍後再試。")
    
    def create_subscription_keyboard(self) -> ReplyKeyboardMarkup:
        """創建訂閱主選單鍵盤"""
        keyboard = [
            [KeyboardButton("📈 訂閱"), KeyboardButton("❌ 取消訂閱")],
            [KeyboardButton("📋 檢查訂閱"), KeyboardButton("🔄 頻率傳送清單")],
            [KeyboardButton("⏰ 時間版本選擇")],
            [KeyboardButton("❓ 幫助")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
    
    async def handle_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理主選單選擇"""
        text = update.message.text
        
        if text == "📈 訂閱":
            await self.handle_subscribe_button(update, context)
        elif text == "❌ 取消訂閱":
            await self.handle_unsubscribe_button(update, context)
        elif text == "📋 檢查訂閱":
            await self.handle_check_subscriptions_button(update, context)
        elif text == "🔄 頻率傳送清單":
            await self.handle_frequency_list_button(update, context)
        elif text == "⏰ 時間版本選擇":
            await self.handle_time_version_button(update, context)
        elif text == "❓ 幫助":
            await self.help_command(update, context)
    
    async def handle_subscribe_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理訂閱按鈕"""
        user_id = update.effective_user.id
        self.user_states[user_id] = "waiting_for_symbol"
        
        message = (
            "📈 新增訂閱\n\n"
            "請輸入股票代碼（例如：AAPL、TSLA、BTC-USD）："
        )
        
        await self.send_message(update, context, message, reply_markup=CommonKeyboards.cancel_button())
    
    async def handle_unsubscribe_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理取消訂閱按鈕"""
        user_id = update.effective_user.id
        user_subs = self.get_user_subscriptions(user_id)
        
        if not user_subs:
            await self.send_message(update, context, "您目前沒有訂閱任何股票。")
            return
        
        self.user_states[user_id] = "waiting_for_unsubscribe"
        
        # 創建取消訂閱鍵盤
        keyboard = []
        for symbol in user_subs:
            keyboard.append([KeyboardButton(symbol)])
        keyboard.append([KeyboardButton("🔙 返回主選單")])
        
        unsubscribe_keyboard = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        
        message = "請選擇要取消訂閱的股票："
        await self.send_message(update, context, message, reply_markup=unsubscribe_keyboard)
    
    async def handle_check_subscriptions_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理檢查訂閱按鈕"""
        user_id = update.effective_user.id
        user_subs = self.get_user_subscriptions(user_id)
        
        if not user_subs:
            await self.send_message(update, context, "您目前沒有訂閱任何股票。")
            return
        
        message = "📋 您的訂閱清單：\n\n"
        for symbol, interval in user_subs.items():
            symbol_name = self.get_symbol_name(symbol)
            message += f"• {symbol} ({symbol_name}) - 每{interval}分鐘\n"
        
        await self.send_message(update, context, message, reply_markup=CommonKeyboards.back_button())
    
    async def handle_frequency_list_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理頻率傳送清單按鈕"""
        keyboard = [
            [KeyboardButton("🌅 上午清單"), KeyboardButton("🌙 晚上清單")],
            [KeyboardButton("🌞 全日清單")],
            [KeyboardButton("🔙 返回主選單")]
        ]
        frequency_keyboard = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        
        message = "請選擇頻率傳送清單："
        await self.send_message(update, context, message, reply_markup=frequency_keyboard)
    
    async def handle_time_version_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理時間版本選擇按鈕"""
        keyboard = [
            [KeyboardButton("30分鐘"), KeyboardButton("1小時")],
            [KeyboardButton("4小時"), KeyboardButton("日線")],
            [KeyboardButton("🔙 返回主選單")]
        ]
        time_keyboard = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        
        message = "請選擇分析時間版本："
        await self.send_message(update, context, message, reply_markup=time_keyboard)
    
    async def handle_interval_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理間隔選擇"""
        user_id = update.effective_user.id
        text = update.message.text
        
        if user_id not in self.user_states or self.user_states[user_id] != "waiting_for_interval":
            return
        
        # 解析間隔分鐘數
        interval_map = {"5分鐘": 5, "10分鐘": 10, "15分鐘": 15, "30分鐘": 30}
        interval_minutes = interval_map.get(text)
        
        if not interval_minutes:
            await self.send_message(update, context, "無效的間隔選擇。")
            return
        
        # 獲取之前選擇的股票代碼
        symbol = context.user_data.get("pending_symbol")
        if not symbol:
            await self.send_message(update, context, "找不到股票代碼，請重新開始。")
            return
        
        # 添加訂閱
        await self.add_subscription(user_id, symbol, interval_minutes)
        
        # 清理狀態
        del self.user_states[user_id]
        if "pending_symbol" in context.user_data:
            del context.user_data["pending_symbol"]
        
        message = f"✅ 已成功訂閱 {symbol}，每{interval_minutes}分鐘發送分析。"
        await self.send_message(update, context, message, reply_markup=self.create_subscription_keyboard())
    
    async def handle_symbol_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理股票代碼輸入"""
        user_id = update.effective_user.id
        symbol = update.message.text.upper()
        
        if user_id not in self.user_states:
            return
        
        state = self.user_states[user_id]
        
        if state == "waiting_for_symbol":
            # 驗證股票代碼
            if not self.is_valid_symbol(symbol):
                await self.send_message(update, context, f"❌ 無效的股票代碼：{symbol}")
                return
            
            # 儲存股票代碼並等待間隔選擇
            context.user_data["pending_symbol"] = symbol
            self.user_states[user_id] = "waiting_for_interval"
            
            keyboard = [
                [KeyboardButton("5分鐘"), KeyboardButton("10分鐘")],
                [KeyboardButton("15分鐘"), KeyboardButton("30分鐘")],
                [KeyboardButton("🔙 返回主選單")]
            ]
            interval_keyboard = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
            
            message = f"請選擇 {symbol} 的分析頻率："
            await self.send_message(update, context, message, reply_markup=interval_keyboard)
        
        elif state == "waiting_for_unsubscribe":
            # 取消訂閱
            if symbol in self.get_user_subscriptions(user_id):
                await self.remove_subscription(user_id, symbol)
                message = f"✅ 已取消訂閱 {symbol}"
            else:
                message = f"❌ 您沒有訂閱 {symbol}"
            
            del self.user_states[user_id]
            await self.send_message(update, context, message, reply_markup=self.create_subscription_keyboard())
    
    async def handle_back_to_main(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理返回主選單"""
        user_id = update.effective_user.id
        if user_id in self.user_states:
            del self.user_states[user_id]
        
        await self.send_message(
            update, context,
            "🔙 返回主選單",
            reply_markup=self.create_subscription_keyboard()
        )
    
    async def handle_frequency_list_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理頻率傳送清單選擇"""
        text = update.message.text
        user_id = update.effective_user.id
        
        list_map = {
            "🌅 上午清單": "morning",
            "🌙 晚上清單": "evening", 
            "🌞 全日清單": "full_day"
        }
        
        list_id = list_map.get(text)
        if not list_id:
            await self.send_message(update, context, "無效的清單選擇。")
            return
        
        self.user_selected_list[user_id] = list_id
        
        keyboard = [
            [KeyboardButton("➕ 新增到訂閱"), KeyboardButton("📋 查看清單")],
            [KeyboardButton("🔙 返回主選單")]
        ]
        action_keyboard = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=True)
        
        message = f"請選擇對 {text} 的操作："
        await self.send_message(update, context, message, reply_markup=action_keyboard)
    
    async def handle_pagination(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理分頁"""
        text = update.message.text
        user_id = update.effective_user.id
        
        current_page = self.user_pages.get(user_id, 0)
        
        if text == "上一頁":
            if current_page > 0:
                self.user_pages[user_id] = current_page - 1
        elif text == "下一頁":
            self.user_pages[user_id] = current_page + 1
        
        # 這裡可以實現實際的分頁邏輯
        await self.send_message(update, context, f"當前頁面: {self.user_pages[user_id]}")
    
    # 訂閱管理方法
    async def add_subscription(self, user_id: int, symbol: str, interval_minutes: int, symbol_name: str = None):
        """添加訂閱"""
        if user_id not in self.subscriptions:
            self.subscriptions[user_id] = {}
        
        self.subscriptions[user_id][symbol] = interval_minutes
        self.save_subscriptions()
        
        # 啟動訂閱任務
        self.start_subscription_task(user_id, symbol, interval_minutes)
        
        self.logger.info(f"用戶 {user_id} 添加訂閱: {symbol} ({interval_minutes}分鐘)")
    
    async def remove_subscription(self, user_id: int, symbol: str):
        """移除訂閱"""
        if user_id in self.subscriptions and symbol in self.subscriptions[user_id]:
            del self.subscriptions[user_id][symbol]
            self.save_subscriptions()
            
            # 停止訂閱任務
            self.stop_subscription_task(user_id, symbol)
            
            self.logger.info(f"用戶 {user_id} 移除訂閱: {symbol}")
            return True
        return False
    
    def get_user_subscriptions(self, user_id: int) -> Dict[str, int]:
        """獲取用戶訂閱"""
        return self.subscriptions.get(user_id, {})
    
    def start_subscription_task(self, user_id: int, symbol: str, interval_minutes: int):
        """啟動訂閱任務"""
        task_id = f"sub_{user_id}_{symbol}_{interval_minutes}"
        
        if user_id not in self.subscription_tasks:
            self.subscription_tasks[user_id] = {}
        
        self.subscription_tasks[user_id][symbol] = task_id
        
        # 啟動異步任務
        asyncio.create_task(self.subscription_task_loop(user_id, task_id, symbol, interval_minutes))
    
    def stop_subscription_task(self, user_id: int, symbol: str):
        """停止訂閱任務"""
        if user_id in self.subscription_tasks and symbol in self.subscription_tasks[user_id]:
            del self.subscription_tasks[user_id][symbol]
    
    async def subscription_task_loop(self, user_id: int, task_id: str, symbol: str, interval_minutes: int):
        """訂閱任務循環"""
        while True:
            try:
                # 檢查任務是否仍然有效
                if (user_id not in self.subscription_tasks or 
                    symbol not in self.subscription_tasks[user_id] or
                    self.subscription_tasks[user_id][symbol] != task_id):
                    break
                
                # 發送分析
                await self.send_subscription_analysis(user_id, symbol)
                
                # 等待指定間隔
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                self.logger.error(f"訂閱任務錯誤 {task_id}: {e}")
                await asyncio.sleep(60)  # 錯誤時等待1分鐘
    
    async def send_subscription_analysis(self, user_id: int, symbol: str):
        """發送訂閱分析"""
        try:
            # 這裡實現實際的分析和發送邏輯
            # 暫時使用簡單的訊息
            message = f"📊 {symbol} 分析更新\n時間: {datetime.now().strftime('%H:%M:%S')}"
            
            # 使用 application 發送訊息
            if self.application:
                await self.application.bot.send_message(chat_id=user_id, text=message)
            
        except Exception as e:
            self.logger.error(f"發送訂閱分析錯誤: {e}")
    
    # 工具方法
    def is_valid_symbol(self, symbol: str) -> bool:
        """檢查股票代碼是否有效"""
        # 簡單的驗證邏輯，可以根據需要擴展
        return bool(re.match(r'^[A-Z]{1,5}(-[A-Z]{3})?$', symbol))
    
    def get_symbol_name(self, symbol: str) -> str:
        """獲取股票名稱"""
        # 這裡可以實現從 CSV 檔案或其他來源獲取股票名稱的邏輯
        return symbol
    
    def load_frequency_lists(self):
        """載入頻率傳送清單"""
        try:
            # 這裡實現載入頻率清單的邏輯
            self.frequency_lists = {
                "morning": ["AAPL", "GOOGL", "MSFT"],
                "evening": ["TSLA", "AMZN", "META"],
                "full_day": ["BTC-USD", "ETH-USD", "LTC-USD"]
            }
        except Exception as e:
            self.logger.error(f"載入頻率清單錯誤: {e}")
    
    def load_subscriptions(self):
        """載入訂閱數據"""
        try:
            # 這裡實現載入訂閱數據的邏輯
            pass
        except Exception as e:
            self.logger.error(f"載入訂閱數據錯誤: {e}")
    
    def save_subscriptions(self):
        """儲存訂閱數據"""
        try:
            # 這裡實現儲存訂閱數據的邏輯
            pass
        except Exception as e:
            self.logger.error(f"儲存訂閱數據錯誤: {e}")
    
    # 命令處理方法
    async def subscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /subscribe 命令"""
        args = context.args
        if not args:
            await self.send_message(update, context, "請提供股票代碼，例如：/subscribe AAPL")
            return
        
        symbol = args[0].upper()
        if not self.is_valid_symbol(symbol):
            await self.send_message(update, context, f"無效的股票代碼：{symbol}")
            return
        
        user_id = update.effective_user.id
        interval_minutes = 30  # 預設30分鐘
        
        await self.add_subscription(user_id, symbol, interval_minutes)
        message = f"✅ 已成功訂閱 {symbol}，每{interval_minutes}分鐘發送分析。"
        await self.send_message(update, context, message)
    
    async def unsubscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /unsubscribe 命令"""
        args = context.args
        if not args:
            await self.send_message(update, context, "請提供股票代碼，例如：/unsubscribe AAPL")
            return
        
        symbol = args[0].upper()
        user_id = update.effective_user.id
        
        if await self.remove_subscription(user_id, symbol):
            message = f"✅ 已取消訂閱 {symbol}"
        else:
            message = f"❌ 您沒有訂閱 {symbol}"
        
        await self.send_message(update, context, message)
    
    async def subscriptions_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /subscriptions 命令"""
        user_id = update.effective_user.id
        user_subs = self.get_user_subscriptions(user_id)
        
        if not user_subs:
            await self.send_message(update, context, "您目前沒有訂閱任何股票。")
            return
        
        message = "📋 您的訂閱清單：\n\n"
        for symbol, interval in user_subs.items():
            symbol_name = self.get_symbol_name(symbol)
            message += f"• {symbol} ({symbol_name}) - 每{interval}分鐘\n"
        
        await self.send_message(update, context, message)
    
    async def stop_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """處理 /stop 命令"""
        user_id = update.effective_user.id
        
        # 移除所有訂閱
        user_subs = self.get_user_subscriptions(user_id).copy()
        for symbol in user_subs:
            await self.remove_subscription(user_id, symbol)
        
        message = f"✅ 已停止所有訂閱（共 {len(user_subs)} 個）"
        await self.send_message(update, context, message)


async def main():
    """主函數"""
    bot = SubscriptionBot()
    
    try:
        await bot.start()
        
        # 保持運行
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("正在停止機器人...")
    except Exception as e:
        print(f"發生錯誤: {e}")
    finally:
        await bot.stop()


if __name__ == "__main__":
    asyncio.run(main()) 