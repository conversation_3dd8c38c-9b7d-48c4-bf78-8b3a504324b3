import pandas as pd
import numpy as np
import datetime as dt
import yfinance as yf
import matplotlib.pyplot as plt
from pandas.tseries.offsets import BDay
import os

import matplotlib

matplotlib.font_manager.fontManager.addfont('TaipeiSansTCBeta-Regular.ttf')
matplotlib.rc('font', family='Taipei Sans TC Beta')

def getMLFlipAdambySymbol(symbol):
    #==========================#
    # 1. 資料抓取 (Data Acquisition)
    #==========================#

    symbol = symbol  # 你可以改成其他股票代碼，例如 "AAPL"
    end_date = dt.date.today()
    start_date = end_date - dt.timedelta(days=365*2)  # 過去一年

    df = yf.download(symbol, start=start_date, end=end_date, progress=False)

    # 若欄位為 MultiIndex，扁平化
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(0)

    df = df[['Open', 'High', 'Low', 'Close', 'Volume']].dropna()
    print(f"Downloaded {len(df)} rows of data for {symbol}")
    print(df.tail(5))

    #==========================#
    # 2. 技術指標計算
    #==========================#

    # --- RSI (14日) ---
    window_rsi = 14
    delta = df['Close'].diff(1)
    gain = delta.where(delta > 0, 0.0)
    loss = -delta.where(delta < 0, 0.0)

    avg_gain = gain.rolling(window_rsi).mean()
    avg_loss = loss.rolling(window_rsi).mean()

    avg_gain.iloc[:window_rsi] = gain.iloc[:window_rsi].mean()
    avg_loss.iloc[:window_rsi] = loss.iloc[:window_rsi].mean()

    for i in range(window_rsi, len(df)):
        avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (window_rsi - 1) + gain.iloc[i]) / window_rsi
        avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (window_rsi - 1) + loss.iloc[i]) / window_rsi

    RS = avg_gain / avg_loss
    df['RSI'] = 100 - (100 / (1 + RS))

    # --- Bollinger Bands (20日) ---
    window_bb = 20
    df['MA20'] = df['Close'].rolling(window_bb).mean()
    df['BB_std'] = df['Close'].rolling(window_bb).std()
    df['BB_upper'] = df['MA20'] + 2 * df['BB_std']
    df['BB_lower'] = df['MA20'] - 2 * df['BB_std']
    df['BB_%B'] = ((df['Close'] - df['BB_lower']) / (df['BB_upper'] - df['BB_lower'])).squeeze()

    # --- ATR (14日) ---
    window_atr = 14
    high_low = df['High'] - df['Low']
    high_prevclose = (df['High'] - df['Close'].shift(1)).abs()
    low_prevclose = (df['Low'] - df['Close'].shift(1)).abs()
    df['TR'] = np.maximum.reduce([high_low, high_prevclose, low_prevclose])

    # 計算 ATR 的函數
    def atr(data, period):
        tr = data['TR']
        atr = pd.Series(index=data.index)
        atr.iloc[:period] = tr.iloc[:period].mean()
        for i in range(period, len(data)):
            atr.iloc[i] = (atr.iloc[i-1] * (period - 1) + tr.iloc[i]) / period
        return atr

    # 計算追踪止損價的函式
    def calculate_trailing_stop(df, nATRPeriod, nATRMultip):
        xATR = atr(df, nATRPeriod)
        nLoss = nATRMultip * xATR

        xATRTrailingStop = pd.Series(index=df.index)
        for i in range(1, len(df)):
            prev_close = df['Close'].iloc[i - 1]
            prev_stop = xATRTrailingStop.iloc[i - 1]
            close = df['Close'].iloc[i]

            if close > prev_stop and prev_close > prev_stop:
                xATRTrailingStop.iloc[i] = max(prev_stop, close - nLoss.iloc[i])
            elif close < prev_stop and prev_close < prev_stop:
                xATRTrailingStop.iloc[i] = min(prev_stop, close + nLoss.iloc[i])
            else:
                xATRTrailingStop.iloc[i] = close - nLoss.iloc[i] if close > prev_stop else close + nLoss.iloc[i]
        return xATRTrailingStop

    # 計算回測報酬的函式
    def calculate_returns(df, nATRPeriod, nATRMultip):
        trailing_stop = calculate_trailing_stop(df, nATRPeriod, nATRMultip)
        position = 0  # 0: 空倉, 1: 多倉, -1: 空倉
        entry_price = 0
        returns = []
        
        for i in range(1, len(df)):
            close = df['Close'].iloc[i]
            prev_close = df['Close'].iloc[i-1]
            
            if position == 0:  # 空倉
                if close > trailing_stop.iloc[i] and prev_close <= trailing_stop.iloc[i-1]:
                    position = 1
                    entry_price = close
                elif close < trailing_stop.iloc[i] and prev_close >= trailing_stop.iloc[i-1]:
                    position = -1
                    entry_price = close
            elif position == 1:  # 多倉
                if close < trailing_stop.iloc[i]:
                    returns.append((close - entry_price) / entry_price)
                    position = 0
            else:  # 空倉
                if close > trailing_stop.iloc[i]:
                    returns.append((entry_price - close) / entry_price)
                    position = 0
        
        return sum(returns) if returns else 0

    # ATR 參數最佳化
    periods = range(5, 31)  # ATR 週期範圍：5-30天
    multiples = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]  # ATR 倍數範圍
    best_return = float('-inf')
    best_period = None
    best_multiple = None

    for period in periods:
        for multiple in multiples:
            returns = calculate_returns(df, period, multiple)
            if returns > best_return:
                best_return = returns
                best_period = period
                best_multiple = multiple

    print(f"最佳 ATR 週期: {best_period} 天")
    print(f"最佳 ATR 倍數: {best_multiple}")
    print(f"最佳累積報酬: {best_return:.2%}")

    # 使用最佳參數計算追踪止損價
    trailing_stop = calculate_trailing_stop(df, best_period, best_multiple)

    # 移動平均線
    df['SMA50'] = df['Close'].rolling(50).mean()
    df['EMA50'] = df['Close'].ewm(span=50, adjust=False).mean()

    #==========================#
    # 3. Adam Theory：歷史走勢 + 單一反射(左右) + 雙重反射(上下)
    #==========================#

    # 初始化變數
    pivot_index = None
    start_index = None
    pivot_price = None
    start_price = None

    lookback = 60
    recent_window = df['Close'][-lookback:]

    # 尋找轉折點 - 改為使用最新收盤點
    pivot_index = df.index[-1]  # 使用最新日期
    pivot_price = df['Close'].iloc[-1]  # 使用最新收盤價
    prev_window = df.loc[:pivot_index, 'Close']
    if len(prev_window) > 0:
        start_index = prev_window.idxmax()
        start_price = df.loc[start_index, 'Close']
    else:
        start_index, start_price = pivot_index, pivot_price

    # 計算歷史路徑和反射路徑
    if pivot_index:
        # (A) 歷史實際走勢：從 start_index 到 pivot_index
        historical_path = df.loc[start_index:pivot_index, 'Close']
        hist_values = historical_path.values
        hist_duration = len(hist_values)

        # (B) 單一反射 (Horizontal Mirror) = 只做時間序列的左右翻轉
        once_values = hist_values[::-1]  # 左右翻轉
        once_dates = pd.date_range(start=pivot_index + BDay(1), periods=hist_duration, freq='B')
        once_reflect_path = pd.Series(data=once_values, index=once_dates)

        # (C) 雙重反射 (Vertical Mirror)
        pivot_for_vertical = pivot_price
        second_reflect_values = 2 * pivot_for_vertical - once_values
        twice_reflect_path = pd.Series(data=second_reflect_values, index=once_dates)

        # 合併歷史數據和雙重反射數據用於計算趨勢
        combined_data = pd.concat([historical_path, twice_reflect_path])
        
        # 計算回歸趨勢線
        x = np.arange(len(combined_data))
        y = combined_data.values
        coefficients = np.polyfit(x, y, 1)
        slope = coefficients[0]
        
        # 根據斜率判斷趨勢
        if slope < 0:
            trend_signal = "down"
        else:
            trend_signal = "up"
            
        print(f"Based on regression slope ({slope:.6f}), the trend signal is: {trend_signal}")
    else:
        print("No pivot point found, using default trend signal.")
        trend_signal = "sideways"

    # 計算 ATR 並加入到 DataFrame
    df['ATR'] = atr(df, best_period)

    # 計算重要轉折點
    prices_array = df['Close'].values
    local_max_idx = [i for i in range(1, len(prices_array)-1)
                     if prices_array[i] > prices_array[i-1] and prices_array[i] > prices_array[i+1]]
    local_min_idx = [i for i in range(1, len(prices_array)-1)
                     if prices_array[i] < prices_array[i-1] and prices_array[i] < prices_array[i+1]]
    pivots_idx = sorted(local_max_idx + local_min_idx)

    significant_pivots = []
    if pivots_idx:
        last_idx = pivots_idx[0]
        last_type = 'min' if last_idx in local_min_idx else 'max'
        significant_pivots.append(last_idx)
        for idx in pivots_idx[1:]:
            current_type = 'min' if idx in local_min_idx else 'max'
            if current_type == last_type:
                continue
            if abs(prices_array[idx] - prices_array[last_idx]) >= df['ATR'].iloc[idx]:
                significant_pivots.append(idx)
                last_idx = idx
                last_type = current_type

    # 下面就是繪圖部分
    plt.figure(figsize=(10, 6))
    plt.plot(df.index, df['Close'], label='Stock Price', color='black')
    plt.plot(historical_path.index, hist_values, label='Historical Path', color='blue', linewidth=2)
    plt.plot(twice_reflect_path.index, twice_reflect_path.values,
             label='Double Reflection', color='green', linestyle='--', linewidth=2)

    plt.axvline(pivot_index, color='gray', linestyle='--', alpha=0.7, label='Pivot Line')
    plt.scatter(pivot_index, pivot_price, color='orange', marker='o', s=100, label='Pivot Point')

    # 這裡維持你原本的回歸線與標準差線畫法
    x = np.arange(len(df))
    y = df['Close'].values
    coefficients = np.polyfit(x, y, 1)
    trend_line = np.polyval(coefficients, x)
    std_dev = np.std(y - trend_line)

    # 繪製趨勢線和標準差帶
    plt.plot(df.index, trend_line, color='purple', linestyle='-', label='Trend Line')
    
    # 添加標準差帶的底色
    plt.fill_between(df.index, trend_line + std_dev, trend_line - std_dev, color='white', alpha=0.2)
    plt.fill_between(df.index, trend_line + 2 * std_dev, trend_line + std_dev, color='white', alpha=0.2)
    plt.fill_between(df.index, trend_line - std_dev, trend_line - 2 * std_dev, color='white', alpha=0.2)
    plt.fill_between(df.index, trend_line + 3 * std_dev, trend_line + 2 * std_dev, color='white', alpha=0.2)
    plt.fill_between(df.index, trend_line - 2 * std_dev, trend_line - 3 * std_dev, color='white', alpha=0.2)
    
    # 繪製標準差線（不添加圖例）
    plt.plot(df.index, trend_line + std_dev, color='gray', linestyle='--')
    plt.plot(df.index, trend_line - std_dev, color='gray', linestyle='--')
    plt.plot(df.index, trend_line + 2 * std_dev, color='gray', linestyle='--')
    plt.plot(df.index, trend_line - 2 * std_dev, color='gray', linestyle='--')
    plt.plot(df.index, trend_line + 3 * std_dev, color='gray', linestyle='--')
    plt.plot(df.index, trend_line - 3 * std_dev, color='gray', linestyle='--')

    # 計算延伸到二次反射線圖的追踪止損線
    extended_trailing_stop = pd.Series(index=twice_reflect_path.index)
    last_stop = trailing_stop.iloc[-1]
    last_close = df['Close'].iloc[-1]
    
    for i in range(len(twice_reflect_path)):
        current_price = twice_reflect_path.iloc[i]
        if i == 0:
            extended_trailing_stop.iloc[i] = last_stop
        else:
            prev_stop = extended_trailing_stop.iloc[i-1]
            if current_price > prev_stop:
                extended_trailing_stop.iloc[i] = max(prev_stop, current_price - best_multiple * df['ATR'].iloc[-1])
            else:
                extended_trailing_stop.iloc[i] = min(prev_stop, current_price + best_multiple * df['ATR'].iloc[-1])

    # 繪製追踪止損價（包括延伸部分）
    plt.plot(df.index, trailing_stop, color='gray', linestyle='--', label='Trailing Stop')
    plt.plot(twice_reflect_path.index, extended_trailing_stop, color='gray', linestyle='--')

    # 檢測突破和跌破點（包括延伸部分）
    for i in range(1, len(df)):
        prev_close = df['Close'].iloc[i-1]
        curr_close = df['Close'].iloc[i]
        prev_stop = trailing_stop.iloc[i-1]
        curr_stop = trailing_stop.iloc[i]
        
        # 檢測向上突破
        if prev_close <= prev_stop and curr_close > curr_stop:
            plt.scatter(df.index[i], curr_close, color='green', marker='o', s=100, label='突破點' if i == 1 else "")
            plt.annotate(f'{curr_close:.2f}', 
                        (df.index[i], curr_close),
                        xytext=(10, 10), 
                        textcoords='offset points',
                        color='green',
                        fontsize=8)
        
        # 檢測向下跌破
        elif prev_close >= prev_stop and curr_close < curr_stop:
            plt.scatter(df.index[i], curr_close, color='red', marker='o', s=100, label='跌破點' if i == 1 else "")
            plt.annotate(f'{curr_close:.2f}', 
                        (df.index[i], curr_close),
                        xytext=(10, -15), 
                        textcoords='offset points',
                        color='red',
                        fontsize=8)

    # 檢測延伸部分的突破和跌破點
    for i in range(1, len(twice_reflect_path)):
        prev_price = twice_reflect_path.iloc[i-1]
        curr_price = twice_reflect_path.iloc[i]
        prev_stop = extended_trailing_stop.iloc[i-1]
        curr_stop = extended_trailing_stop.iloc[i]
        
        # 檢測向上突破
        if prev_price <= prev_stop and curr_price > curr_stop:
            plt.scatter(twice_reflect_path.index[i], curr_price, color='green', marker='o', s=100)
            plt.annotate(f'{curr_price:.2f}', 
                        (twice_reflect_path.index[i], curr_price),
                        xytext=(10, 10), 
                        textcoords='offset points',
                        color='green',
                        fontsize=8)
        
        # 檢測向下跌破
        elif prev_price >= prev_stop and curr_price < curr_stop:
            plt.scatter(twice_reflect_path.index[i], curr_price, color='red', marker='o', s=100)
            plt.annotate(f'{curr_price:.2f}', 
                        (twice_reflect_path.index[i], curr_price),
                        xytext=(10, -15), 
                        textcoords='offset points',
                        color='red',
                        fontsize=8)

    # 修改圖例位置為左上方
    plt.legend(loc='upper left', bbox_to_anchor=(0.0, 1.0))

    #==========================#
    # 4. 交易策略回測與勝率計算
    #==========================#

    wins = 0
    trades = 0
    for i in range(len(significant_pivots) - 1):
        s_idx = significant_pivots[i]
        e_idx = significant_pivots[i+1]
        if s_idx in local_min_idx and e_idx in local_max_idx:
            entry_price = prices_array[s_idx]
            exit_price = prices_array[e_idx]
            profit = exit_price - entry_price
        elif s_idx in local_max_idx and e_idx in local_min_idx:
            entry_price = prices_array[s_idx]
            exit_price = prices_array[e_idx]
            profit = entry_price - exit_price
        else:
            continue
        trades += 1
        if profit > 0:
            wins += 1

    if trades > 0:
        win_rate = wins / trades
        text_info = [
            f"Based on regression slope, the trend signal is: {trend_signal}",
            f"Historical Path start: {start_index.date()}, price={start_price:.2f}",
            f"Pivot index: {pivot_index.date()}, price={pivot_price:.2f}",
            f"Double Reflection Path: Start={second_reflect_values[0]:.2f}, End={second_reflect_values[-1]:.2f}",
            f"Backtest Result: Total Trades: {trades}, Winning Trades: {wins}, Win Rate: {win_rate*100:.2f}%"
        ]
    else:
        text_info = [
            f"Based on regression slope, the trend signal is: {trend_signal}",
            f"Historical Path start: {start_index.date()}, price={start_price:.2f}",
            f"Pivot index: {pivot_index.date()}, price={pivot_price:.2f}",
            f"Double Reflection Path: Start={second_reflect_values[0]:.2f}, End={second_reflect_values[-1]:.2f}",
            "No significant pivot-based signals found for backtesting."
        ]

    # 在圖的左下方顯示文本
    y_text = 0.3
    for info in text_info:
        plt.text(0.02, y_text, info, transform=plt.gca().transAxes, ha='left', va='top', fontsize=10)
        y_text -= 0.05

    plt.title(f"{symbol} Adam Theory: Double Reflection")
    plt.xlabel("Date")
    plt.ylabel("Price")
    plt.legend(loc='upper left', bbox_to_anchor=(0.0, 1.0))
    plt.tight_layout()
    # 記得把圖另存檔案
    plot_filename = f"{symbol}_FlipTwice.png"
    if os.path.exists(plot_filename):
        os.remove(plot_filename)
    plt.savefig(plot_filename)
    print(f"Plot saved as {plot_filename}")
    plt.show()

getMLFlipAdambySymbol("TRUMP-USD")
