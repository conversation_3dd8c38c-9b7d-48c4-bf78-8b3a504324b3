"""
插件載入器
負責動態載入、驗證和初始化插件
"""

import asyncio
import importlib.util
import logging
import os
from typing import Optional, Dict, Any
from pathlib import Path

from .base_plugin import BasePlugin


class PluginLoader:
    """
    插件載入器
    提供插件的動態載入、驗證和初始化功能
    """
    
    def __init__(self):
        """初始化插件載入器"""
        self.logger = logging.getLogger("PluginLoader")
        self.loaded_modules: Dict[str, Any] = {}
        self.is_initialized = False
    
    async def initialize(self):
        """初始化插件載入器"""
        try:
            self.logger.info("初始化插件載入器...")
            self.is_initialized = True
            self.logger.info("插件載入器初始化完成")
        except Exception as e:
            self.logger.error(f"插件載入器初始化失敗: {e}")
            raise
    
    async def load_plugin_file(self, plugin_path: str) -> BasePlugin:
        """
        從檔案載入插件
        
        Args:
            plugin_path: 插件檔案路徑
            
        Returns:
            插件實例
        """
        try:
            self.logger.info(f"載入插件檔案: {plugin_path}")
            
            # 檢查檔案是否存在
            if not os.path.exists(plugin_path):
                raise FileNotFoundError(f"插件檔案不存在: {plugin_path}")
            
            # 獲取檔案名稱（不含副檔名）
            file_name = Path(plugin_path).stem
            
            # 載入模組
            spec = importlib.util.spec_from_file_location(file_name, plugin_path)
            if spec is None or spec.loader is None:
                raise ImportError(f"無法載入插件模組: {plugin_path}")
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找插件類別
            plugin_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    issubclass(attr, BasePlugin) and 
                    attr != BasePlugin):
                    plugin_class = attr
                    break
            
            if plugin_class is None:
                raise ValueError(f"在 {plugin_path} 中找不到有效的插件類別")
            
            # 創建插件實例
            plugin = plugin_class()
            
            # 設置插件路徑
            plugin.set_plugin_path(plugin_path)
            
            # 保存載入的模組
            self.loaded_modules[plugin.name] = module
            
            self.logger.info(f"插件 {plugin.name} 載入成功")
            return plugin
            
        except Exception as e:
            self.logger.error(f"載入插件檔案 {plugin_path} 失敗: {e}")
            raise
    
    async def validate_plugin(self, plugin: BasePlugin) -> bool:
        """
        驗證插件
        
        Args:
            plugin: 插件實例
            
        Returns:
            驗證是否通過
        """
        try:
            self.logger.info(f"驗證插件: {plugin.name}")
            
            # 檢查必要屬性
            required_attrs = ['name', 'version', 'description']
            for attr in required_attrs:
                if not hasattr(plugin, attr) or not getattr(plugin, attr):
                    raise ValueError(f"插件缺少必要屬性: {attr}")
            
            # 檢查必要方法
            required_methods = ['initialize', 'start', 'stop', 'cleanup']
            for method in required_methods:
                if not hasattr(plugin, method) or not callable(getattr(plugin, method)):
                    raise ValueError(f"插件缺少必要方法: {method}")
            
            # 檢查插件名稱格式
            if not plugin.name.replace('_', '').replace('-', '').isalnum():
                raise ValueError(f"插件名稱格式無效: {plugin.name}")
            
            # 檢查版本格式
            if not self._is_valid_version(plugin.version):
                raise ValueError(f"插件版本格式無效: {plugin.version}")
            
            self.logger.info(f"插件 {plugin.name} 驗證通過")
            return True
            
        except Exception as e:
            self.logger.error(f"插件 {plugin.name} 驗證失敗: {e}")
            raise
    
    def _is_valid_version(self, version: str) -> bool:
        """
        檢查版本格式是否有效
        
        Args:
            version: 版本字符串
            
        Returns:
            是否有效
        """
        try:
            parts = version.split('.')
            if len(parts) != 3:
                return False
            
            for part in parts:
                if not part.isdigit():
                    return False
            
            return True
        except:
            return False
    
    async def initialize_plugin(self, plugin: BasePlugin, config: Dict[str, Any]) -> bool:
        """
        初始化插件
        
        Args:
            plugin: 插件實例
            config: 插件配置
            
        Returns:
            初始化是否成功
        """
        try:
            self.logger.info(f"初始化插件: {plugin.name}")
            
            # 設置配置
            plugin.config = config
            
            # 調用插件初始化方法
            success = await plugin.initialize(config)
            
            if success:
                plugin.is_initialized = True
                self.logger.info(f"插件 {plugin.name} 初始化成功")
            else:
                self.logger.error(f"插件 {plugin.name} 初始化失敗")
            
            return success
            
        except Exception as e:
            self.logger.error(f"初始化插件 {plugin.name} 失敗: {e}")
            return False
    
    async def reload_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """
        重新載入插件
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            重新載入的插件實例
        """
        try:
            self.logger.info(f"重新載入插件: {plugin_name}")
            
            # 獲取原始模組
            if plugin_name not in self.loaded_modules:
                raise ValueError(f"找不到插件模組: {plugin_name}")
            
            module = self.loaded_modules[plugin_name]
            plugin_path = getattr(module, '__file__', None)
            
            if not plugin_path:
                raise ValueError(f"無法獲取插件檔案路徑: {plugin_name}")
            
            # 重新載入模組
            spec = importlib.util.spec_from_file_location(plugin_name, plugin_path)
            if spec is None or spec.loader is None:
                raise ImportError(f"無法重新載入插件模組: {plugin_path}")
            
            # 重新載入
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 更新載入的模組
            self.loaded_modules[plugin_name] = module
            
            # 查找插件類別
            plugin_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    issubclass(attr, BasePlugin) and 
                    attr != BasePlugin):
                    plugin_class = attr
                    break
            
            if plugin_class is None:
                raise ValueError(f"在重新載入的模組中找不到有效的插件類別")
            
            # 創建新的插件實例
            plugin = plugin_class()
            plugin.set_plugin_path(plugin_path)
            
            self.logger.info(f"插件 {plugin_name} 重新載入成功")
            return plugin
            
        except Exception as e:
            self.logger.error(f"重新載入插件 {plugin_name} 失敗: {e}")
            raise
    
    def get_loaded_modules(self) -> Dict[str, Any]:
        """
        獲取已載入的模組
        
        Returns:
            已載入模組字典
        """
        return self.loaded_modules.copy()
    
    def is_module_loaded(self, module_name: str) -> bool:
        """
        檢查模組是否已載入
        
        Args:
            module_name: 模組名稱
            
        Returns:
            是否已載入
        """
        return module_name in self.loaded_modules
    
    def unload_module(self, module_name: str):
        """
        卸載模組
        
        Args:
            module_name: 模組名稱
        """
        try:
            if module_name in self.loaded_modules:
                del self.loaded_modules[module_name]
                self.logger.info(f"模組 {module_name} 已卸載")
        except Exception as e:
            self.logger.error(f"卸載模組 {module_name} 失敗: {e}")
    
    def get_module_info(self, module_name: str) -> Dict[str, Any]:
        """
        獲取模組信息
        
        Args:
            module_name: 模組名稱
            
        Returns:
            模組信息字典
        """
        try:
            if module_name not in self.loaded_modules:
                return {"error": f"模組 {module_name} 未載入"}
            
            module = self.loaded_modules[module_name]
            
            return {
                "name": module_name,
                "file": getattr(module, '__file__', 'Unknown'),
                "spec": getattr(module, '__spec__', None),
                "attributes": [attr for attr in dir(module) if not attr.startswith('_')]
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    async def cleanup(self):
        """清理插件載入器"""
        try:
            self.logger.info("清理插件載入器...")
            
            # 清空載入的模組
            self.loaded_modules.clear()
            
            self.is_initialized = False
            self.logger.info("插件載入器清理完成")
            
        except Exception as e:
            self.logger.error(f"清理插件載入器失敗: {e}")


# 便捷函數
def create_plugin_loader() -> PluginLoader:
    """創建插件載入器實例"""
    return PluginLoader()


async def setup_plugin_loader() -> PluginLoader:
    """設置並初始化插件載入器"""
    loader = create_plugin_loader()
    await loader.initialize()
    return loader 